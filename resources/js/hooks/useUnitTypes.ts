import { useState, useEffect } from 'react';

interface UnitTypes {
  discrete_units: string[];
  continuous_units: string[];
}

type UnitType = 'discrete' | 'continuous' | 'custom';

let cachedUnitTypes: UnitTypes | null = null;
let fetchPromise: Promise<UnitTypes> | null = null;

export function useUnitTypes() {
  const [unitTypes, setUnitTypes] = useState<UnitTypes | null>(cachedUnitTypes);
  const [loading, setLoading] = useState(!cachedUnitTypes);

  useEffect(() => {
    if (cachedUnitTypes) {
      setUnitTypes(cachedUnitTypes);
      setLoading(false);
      return;
    }

    if (fetchPromise) {
      fetchPromise.then((data) => {
        setUnitTypes(data);
        setLoading(false);
      });
      return;
    }

    fetchPromise = fetch('/inventory/unit-types')
      .then(response => {
        if (!response.ok) {
          throw new Error('Failed to fetch unit types');
        }
        return response.json();
      })
      .then((data: UnitTypes) => {
        cachedUnitTypes = data;
        setUnitTypes(data);
        setLoading(false);
        return data;
      })
      .catch(error => {
        console.error('Failed to fetch unit types:', error);
        const fallbackData: UnitTypes = { discrete_units: [], continuous_units: [] };
        cachedUnitTypes = fallbackData;
        setUnitTypes(fallbackData);
        setLoading(false);
        return fallbackData;
      });

    fetchPromise.then((data) => {
      setUnitTypes(data);
      setLoading(false);
    });
  }, []);

  const getUnitType = (unitOfMeasure: string): UnitType => {
    if (!unitTypes || !unitOfMeasure) return 'custom';
    
    const lowerUnit = unitOfMeasure.toLowerCase().trim();
    
    if (unitTypes.discrete_units.includes(lowerUnit)) {
      return 'discrete';
    }
    
    if (unitTypes.continuous_units.includes(lowerUnit)) {
      return 'continuous';
    }
    
    return 'custom';
  };

  return {
    unitTypes,
    loading,
    getUnitType
  };
}