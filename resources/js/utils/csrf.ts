/**
 * Get current CSRF token from meta tag
 */
export function getCSRFToken(): string {
    return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
}

/**
 * Refresh CSRF token from server
 */
export async function refreshCSRFToken(): Promise<string> {
    try {
        const response = await fetch('/csrf-token', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            const metaTag = document.querySelector('meta[name="csrf-token"]');
            if (metaTag && data.csrf_token) {
                metaTag.setAttribute('content', data.csrf_token);
            }
            return data.csrf_token || '';
        }
    } catch (error) {
        console.warn('Failed to refresh CSRF token:', error);
    }
    
    return getCSRFToken();
}

/**
 * Make a fetch request with automatic CSRF token refresh on 419 errors
 */
export async function fetchWithCSRF(url: string, options: RequestInit = {}): Promise<Response> {
    const makeRequest = async (csrfToken: string): Promise<Response> => {
        const headers = {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken,
            ...options.headers,
        };

        return fetch(url, {
            ...options,
            headers,
        });
    };

    // First attempt with current CSRF token
    let response = await makeRequest(getCSRFToken());

    if (response.status === 419) {
        console.log('CSRF token expired, refreshing and retrying...');
        const newToken = await refreshCSRFToken();
        response = await makeRequest(newToken);
    }

    return response;
}

/**
 * Enhanced fetch wrapper with CSRF handling and better error messages
 */
export async function apiRequest<T = unknown>(
    url: string,
    options: RequestInit = {}
): Promise<{ data?: T; error?: string; response: Response }> {
    try {
        const response = await fetchWithCSRF(url, options);
        
        if (!response.ok) {
            let errorMessage = `HTTP error! status: ${response.status}`;
            
            // Provide more specific error messages
            switch (response.status) {
                case 419:
                    errorMessage = 'Session expired. Please refresh the page and try again.';
                    break;
                case 403:
                    errorMessage = 'You do not have permission to perform this action.';
                    break;
                case 404:
                    errorMessage = 'The requested resource was not found.';
                    break;
                case 422:
                    errorMessage = 'Validation error. Please check your input.';
                    break;
                case 500:
                    errorMessage = 'Server error. Please try again later.';
                    break;
                default:
                    if (response.status >= 500) {
                        errorMessage = 'Server error. Please try again later.';
                    } else if (response.status >= 400) {
                        errorMessage = 'Request error. Please check your input and try again.';
                    }
            }
            
            return { error: errorMessage, response };
        }

        const data = await response.json();
        return { data, response };
    } catch (error) {
        console.error('API request failed:', error);
        
        let errorMessage = 'Network error. Please check your connection and try again.';
        if (error instanceof Error) {
            if (error.message.includes('fetch')) {
                errorMessage = 'Network error. Please check your connection and try again.';
            } else if (error.message.includes('JSON')) {
                errorMessage = 'Invalid response from server. Please try again.';
            }
        }
        
        return { 
            error: errorMessage, 
            response: new Response(null, { status: 0, statusText: 'Network Error' })
        };
    }
}

/**
 * POST request with CSRF handling
 */
export async function postWithCSRF<T = unknown>(
    url: string,
    data: Record<string, unknown> = {},
    options: RequestInit = {}
): Promise<{ data?: T; error?: string; response: Response }> {
    return apiRequest<T>(url, {
        method: 'POST',
        body: JSON.stringify(data),
        ...options,
    });
}

/**
 * PUT request with CSRF handling
 */
export async function putWithCSRF<T = unknown>(
    url: string,
    data: Record<string, unknown> = {},
    options: RequestInit = {}
): Promise<{ data?: T; error?: string; response: Response }> {
    return apiRequest<T>(url, {
        method: 'PUT',
        body: JSON.stringify(data),
        ...options,
    });
}

/**
 * DELETE request with CSRF handling
 */
export async function deleteWithCSRF<T = unknown>(
    url: string,
    options: RequestInit = {}
): Promise<{ data?: T; error?: string; response: Response }> {
    return apiRequest<T>(url, {
        method: 'DELETE',
        ...options,
    });
}

/**
 * Check if an error is a CSRF-related error
 */
export function isCSRFError(error: unknown): boolean {
    if (typeof error === 'string') {
        return error.includes('419') || error.includes('CSRF') || error.includes('Session expired');
    }
    
    if (error && typeof error === 'object') {
        const errorObj = error as Record<string, unknown>;
        const message = String(errorObj.message || errorObj.error || '');
        return message.includes('419') || message.includes('CSRF') || message.includes('Session expired');
    }
    
    return false;
}

/**
 * Get user-friendly error message for CSRF errors
 */
export function getCSRFErrorMessage(): string {
    return 'Your session has expired. Please refresh the page and try again.';
}

/**
 * Initialize CSRF token refresh on page load
 */
export function initializeCSRFHandling(): void {
    // Refresh CSRF token every 30 minutes to prevent expiration
    setInterval(async () => {
        try {
            await refreshCSRFToken();
            console.log('CSRF token refreshed automatically');
        } catch (error) {
            console.warn('Failed to auto-refresh CSRF token:', error);
        }
    }, 30 * 60 * 1000); // 30 minutes

    // Listen for focus events to refresh token when user returns to tab
    window.addEventListener('focus', async () => {
        try {
            await refreshCSRFToken();
        } catch (error) {
            console.warn('Failed to refresh CSRF token on focus:', error);
        }
    });
}
