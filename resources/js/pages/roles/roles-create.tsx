import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';

interface RolesCreateProps {
    isPlatformAdmin: boolean;
    organizations?: {
        id: number;
        name: string;
    }[];
    organization?: {
        id: number;
        name: string;
    };
    branches: {
        id: number;
        name: string;
        organization_id: number;
    }[];
    departments: {
        id: number;
        name: string;
        organization_id: number;
        branch_id: number;
    }[];
    permissions: {
        id: number;
        name: string;
        description: string | null;
    }[];
    groupedPermissions: {
        [category: string]: {
            id: number;
            name: string;
            description: string | null;
        }[];
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Roles',
        href: '/roles',
    },
    {
        title: 'Create',
        href: '/roles/create',
    },
];

interface Permission {
    id: number;
    name: string;
    description: string | null;
}

export default function RolesCreate({ isPlatformAdmin, organizations, organization, branches, departments, groupedPermissions }: RolesCreateProps) {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        description: '',
        organization_id: organization?.id?.toString() || '',
        branch_id: '',
        department_id: '',
        is_active: true,
        permission_ids: [] as number[], // Changed to permission_ids to match backend
    });

    const handlePermissionChange = (permissionId: number) => {
        if (data.permission_ids.includes(permissionId)) {
            setData(
                'permission_ids',
                data.permission_ids.filter((id) => id !== permissionId),
            );
        } else {
            setData('permission_ids', [...data.permission_ids, permissionId]);
        }
    };

    const handleSelectAllInCategory = (category: string, categoryPermissions: Permission[], isSelected: boolean) => {
        const permissionIds = categoryPermissions.map(permission => permission.id);
        
        if (isSelected) {
            // Add all permissions from this category that aren't already selected
            const newPermissionIds = [...data.permission_ids];
            permissionIds.forEach(id => {
                if (!newPermissionIds.includes(id)) {
                    newPermissionIds.push(id);
                }
            });
            setData('permission_ids', newPermissionIds);
        } else {
            // Remove all permissions from this category
            setData(
                'permission_ids',
                data.permission_ids.filter(id => !permissionIds.includes(id))
            );
        }
    };

    const handleOrganizationChange = (value: string) => {
        setData((prev) => ({
            ...prev,
            organization_id: value,
            branch_id: '',
            department_id: '',
        }));
    };

    const filteredBranches = branches.filter((branch) => branch.organization_id === parseInt(data.organization_id));

    const filteredDepartments = departments.filter(
        (department) =>
            department.organization_id === parseInt(data.organization_id) &&
            (data.branch_id ? department.branch_id === parseInt(data.branch_id) : true),
    );

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/roles');
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Role" />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-foreground/90 text-2xl font-bold">Create New Role</h1>
                </div>

                <form onSubmit={handleSubmit} className="mx-auto w-full max-w-3xl">
                    <Card>
                        <CardHeader>
                            <CardTitle>Role Information</CardTitle>
                            <CardDescription>Enter the details for the new role</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6 p-6">
                            <div className="space-y-2">
                                <Label htmlFor="name" className="text-sm font-medium">
                                    Role Name
                                </Label>
                                <Input
                                    id="name"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    placeholder="Enter role name"
                                />
                                {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description" className="text-sm font-medium">
                                    Description (Optional)
                                </Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Enter role description"
                                    rows={3}
                                />
                                {errors.description && <p className="text-sm text-destructive">{errors.description}</p>}
                            </div>

                            {isPlatformAdmin && organizations && (
                                <div className="space-y-2">
                                    <Label htmlFor="organization_id" className="text-sm font-medium">
                                        Organization
                                    </Label>
                                    <Select
                                        value={data.organization_id}
                                        onValueChange={handleOrganizationChange}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select organization" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {organizations.map((org) => (
                                                <SelectItem key={org.id} value={org.id.toString()}>
                                                    {org.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.organization_id && <p className="text-sm text-destructive">{errors.organization_id}</p>}
                                </div>
                            )}

                            {data.organization_id && (
                                <div className="space-y-2">
                                    <Label htmlFor="branch_id" className="text-sm font-medium">
                                        Role Scope
                                    </Label>
                                    <Select
                                        value={data.branch_id}
                                        onValueChange={(value) => {
                                            setData('branch_id', value);
                                            setData('department_id', '');
                                        }}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select scope" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="0">Organization-wide</SelectItem>
                                            {filteredBranches.map((branch) => (
                                                <SelectItem key={branch.id} value={branch.id.toString()}>
                                                    Branch: {branch.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.branch_id && <p className="text-sm text-destructive">{errors.branch_id}</p>}
                                </div>
                            )}

                            {data.branch_id && data.branch_id !== '0' && (
                                <div className="space-y-2">
                                    <Label htmlFor="department_id" className="text-sm font-medium">
                                        Department Scope
                                    </Label>
                                    <Select value={data.department_id} onValueChange={(value) => setData('department_id', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select department scope" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="0">Branch-wide (All Departments)</SelectItem>
                                            {filteredDepartments.map((department) => (
                                                <SelectItem key={department.id} value={department.id.toString()}>
                                                    Department: {department.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.department_id && <p className="text-sm text-destructive">{errors.department_id}</p>}
                                </div>
                            )}

                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="is_active"
                                    checked={data.is_active}
                                    onCheckedChange={(checked: boolean) => setData('is_active', checked as true)}
                                />
                                <Label htmlFor="is_active" className="text-sm font-medium">
                                    Active
                                </Label>
                            </div>

                            <div className="space-y-6">
                                <div>
                                    <h3 className="text-lg font-medium">Permissions</h3>
                                    <p className="text-sm text-muted-foreground">Select the permissions for this role</p>
                                </div>

                                {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => {
                                    // Check if all permissions in this category are selected
                                    const allSelected = categoryPermissions.every(permission => 
                                        data.permission_ids.includes(permission.id)
                                    );
                                    
                                    // Check if some permissions in this category are selected
                                    const someSelected = categoryPermissions.some(permission => 
                                        data.permission_ids.includes(permission.id)
                                    );
                                    
                                    return (
                                        <div key={category} className="space-y-3">
                                            <div className="flex items-center justify-between border-b pb-1">
                                                <h4 className="text-md font-medium">
                                                    {category}
                                                </h4>
                                                <div className="flex items-center space-x-2">
                                                    <Checkbox
                                                        id={`select-all-${category}`}
                                                        checked={allSelected}
                                                        // Use "indeterminate" state when some but not all permissions are selected
                                                        ref={(el) => {
                                                            const input = el as unknown as HTMLInputElement | null;
                                                            if (input) {
                                                                input.indeterminate = someSelected && !allSelected;
                                                            }
                                                        }}
                                                        onCheckedChange={(checked) => 
                                                            handleSelectAllInCategory(category, categoryPermissions, Boolean(checked))
                                                        }
                                                    />
                                                    <Label 
                                                        htmlFor={`select-all-${category}`}
                                                        className="text-sm font-medium"
                                                    >
                                                        Select All
                                                    </Label>
                                                </div>
                                            </div>
                                            <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3">
                                                {categoryPermissions.map((permission) => (
                                                    <div key={permission.id} className="flex items-center space-x-2">
                                                        <Checkbox
                                                            id={`permission-${permission.id}`}
                                                            checked={data.permission_ids.includes(permission.id)}
                                                            onCheckedChange={() => handlePermissionChange(permission.id)}
                                                        />
                                                        <div>
                                                            <Label
                                                                htmlFor={`permission-${permission.id}`}
                                                                className="text-sm font-medium"
                                                            >
                                                                {permission.name.replace(/^(manage|view|create|edit|delete|approve|reject)-/, '')}
                                                            </Label>
                                                            {permission.description && (
                                                                <p className="text-xs text-muted-foreground">{permission.description}</p>
                                                            )}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    );
                                })}
                                {errors.permission_ids && <p className="text-sm text-destructive">{errors.permission_ids}</p>}
                            </div>

                            <div className="flex justify-end space-x-2 pt-4">
                                <Button
                                    variant="destructive"
                                    type="button"
                                    onClick={() => window.history.back()}
                                    disabled={processing}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={processing}
                                >
                                    Create Role
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </form>
            </div>
        </AppLayout>
    );
}