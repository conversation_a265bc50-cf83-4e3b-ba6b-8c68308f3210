import React, { useState } from 'react';
import { Link, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatCurrency } from '@/lib/utils';
import { type BreadcrumbItem } from '@/types';

interface ChartOfAccount {
    id: number;
    name: string;
}

interface TransactionItem {
    id: number;
    description: string;
    total_price: number;
    chart_of_account: ChartOfAccount;
}

interface User {
    id: number;
    name?: string;
    first_name: string;
    last_name: string;
    full_name?: string;
}

interface Requisition {
    id: number;
    purpose: string;
    requester: User;
}

interface Transaction {
    id: number;
    transaction_type: string;
    total_amount: number;
    transaction_cost?: number;
    payment_method?: string;
    account_details?: string;
    disbursement_transaction_id?: string;
    status: string;
    created_at: string;
    created_by: number;
    requisition?: Requisition;
    items: TransactionItem[];
    creator: User;
    running_balance: number;
    cash_in: number;
    cash_out: number;
    is_initial_issuance?: boolean;
}

interface CashFloat {
    id: number;
    name: string;
    initial_amount: number;
    remaining_amount?: number;
    current_balance: number;
    alert_threshold?: number;
    status: string;
    issued_at: string;
    created_at: string;
    department?: {
        id: number;
        name: string;
    };
    branch?: {
        id: number;
        name: string;
    };
    user?: {
        id: number;
        name: string;
    };
    transactions: Transaction[];
}

interface Auth {
    user: User;
}

interface Props {
    cashFloat: CashFloat;
    chartOfAccounts: ChartOfAccount[];
    auth: Auth;
    financeManager?: User;
    filters?: {
        start_date?: string;
        end_date?: string;
    };
}

export default function Show({ cashFloat, chartOfAccounts, auth, financeManager, filters }: Props) {


    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Cash Floats', href: '/cash-floats' },
        { title: cashFloat.name, href: `/cash-floats/${cashFloat.id}` },
    ];

    const [startDate, setStartDate] = useState<string>(filters?.start_date || '');
    const [endDate, setEndDate] = useState<string>(filters?.end_date || '');

    const handleFilter = () => {
        if (!startDate || !endDate) {
            alert('Please select both start and end dates');
            return;
        }

        router.get(`/cash-floats/${cashFloat.id}`, {
            start_date: startDate,
            end_date: endDate
        }, {
            preserveState: true,
            preserveScroll: true
        });
    };

    const handleExportPDF = () => {
        const params = new URLSearchParams();
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);

        window.open(`/cash-floats/${cashFloat.id}/export-pdf?${params.toString()}`, '_blank');
    };

    const handleClearFilter = () => {
        setStartDate('');
        setEndDate('');
        router.get(`/cash-floats/${cashFloat.id}`, {}, {
            preserveState: true,
            preserveScroll: true
        });
    };

    const getTransactionTypeLabel = (type: string) => {
        switch (type) {
            case 'float_issuance':
                return 'Float Issuance';
            case 'reimbursement':
                return 'Reimbursement';
            case 'expense':
                return 'Expense';
            case 'float_return':
                return 'Float Return';
            case 'disbursement':
                return 'Disbursement';
            default:
                return type;
        }
    };

    const accountAmmountsCollection: Record<number, number>[] = [];


    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-xl md:text-2xl font-semibold text-foreground/90">{cashFloat.name}</h1>
                        <p className="mt-1 text-sm text-foreground/90">Cash float details and transaction history</p>
                    </div>
                    <div className="flex space-x-3">
                        <Link href="/cash-floats">
                            <Button variant="outline" className="w-full sm:w-auto">Back to List</Button>
                        </Link>
                    </div>
                </div>

                {/* Cash Float Details */}
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Float Details</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <p className="text-foreground/90 text-sm font-medium">Current Balance</p>
                                    <p
                                        className={`text-lg font-semibold ${
                                            cashFloat.alert_threshold && cashFloat.current_balance <= cashFloat.alert_threshold
                                                ? 'text-destructive/80'
                                                : 'text-foreground/90'
                                        }`}
                                    >
                                        {formatCurrency(cashFloat.current_balance)}
                                    </p>
                                </div>
                                <div>
                                    <p className="text-foreground/90 text-sm font-medium">Initial Amount</p>
                                    <p className="text-foreground/90 text-lg font-semibold">{formatCurrency(cashFloat.initial_amount)}</p>
                                </div>
                                {cashFloat.alert_threshold && (
                                    <div>
                                        <p className="text-sm font-medium text-foreground/90">Alert Threshold</p>
                                        <p className="text-lg font-semibold text-foreground/90">{formatCurrency(cashFloat.alert_threshold)}</p>
                                    </div>
                                )}
                                <div>
                                    <p className="text-sm font-medium text-foreground/90">Status</p>
                                    <Badge
                                        variant={
                                            cashFloat.status === 'active' ? 'default' : cashFloat.status === 'inactive' ? 'secondary' : 'destructive'
                                        }
                                    >
                                        {cashFloat.status}
                                    </Badge>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Assignment Details</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-3">
                                {cashFloat.department && (
                                    <div>
                                        <p className="text-sm font-medium text-foreground/90">Department</p>
                                        <p className="text-lg font-semibold text-muted-foreground/90">{cashFloat.department.name}</p>
                                    </div>
                                )}
                                {cashFloat.branch && (
                                    <div>
                                        <p className="text-sm font-medium text-foreground/90">Branch</p>
                                        <p className="text-lg font-semibold text-foreground/90">{cashFloat.branch.name}</p>
                                    </div>
                                )}
                                {cashFloat.user && (
                                    <div>
                                        <p className="text-sm font-medium text-foreground/90">Assigned User</p>
                                        <p className="text-lg font-semibold text-foreground/90">{cashFloat.user.name}</p>
                                    </div>
                                )}
                                <div>
                                    <p className="text-sm font-medium text-foreground/90">Issued Date</p>
                                    <p className="text-base md:text-lg font-semibold text-foreground/90">{new Date(cashFloat.issued_at).toLocaleDateString()}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Petty Cash Log */}
                <Card>
                    <CardHeader>
                        <CardTitle className="text-lg md:text-xl">Petty Cash Log</CardTitle>
                        <CardDescription>
                            Detailed transaction log with running balance calculation
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {/* Petty Cash Log Header */}
                        <div className="mb-4 md:mb-6 p-3 md:p-4 border border-gray-200 rounded-md bg-gray-50">
                            <h2 className="text-black md:text-xl font-bold text-center mb-2">PETTY CASH LOG</h2>

                            <div className="flex flex-col space-y-2 lg:flex-row lg:justify-between lg:space-y-0 text-sm text-gray-700">
                                <p><strong>Person Responsible:</strong> {financeManager?.full_name || `${auth.user.first_name} ${auth.user.last_name}`.trim() || 'N/A'}</p>
                                <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2">
                                    <p><strong>Reporting Period:</strong></p>
                                    <div className="flex flex-wrap items-center gap-2">
                                        <input
                                            type="date"
                                            className="border rounded-md px-2 py-1 text-sm w-full sm:w-auto"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                        <span className="hidden sm:inline">-</span>
                                        <input
                                            type="date"
                                            className="border rounded-md px-2 py-1 text-sm w-full sm:w-auto"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={handleFilter}
                                            className="w-full sm:w-auto"
                                        >
                                            Apply Filter
                                        </Button>
                                        {(startDate || endDate) && (
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={handleClearFilter}
                                                className="w-full sm:w-auto"
                                            >
                                                Clear
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {cashFloat.transactions.length > 0 ? (
                            <div className="overflow-x-auto border border-gray-200 rounded-md" style={{ WebkitOverflowScrolling: 'touch' }}>
                                <Table className="min-w-full divide-y divide-gray-200" style={{ minWidth: '1200px' }}>
                                    <TableHeader style={{ backgroundColor: '#f8f9fa' }}>
                                        <TableRow>
                                            <TableHead className="px-2 md:px-4 py-2 text-left text-xs font-medium uppercase tracking-wider min-w-[80px]" style={{ color: '#2d3748', borderRight: '1px solid #000000' }}>Date</TableHead>
                                            <TableHead className="px-2 md:px-4 py-2 text-left text-xs font-medium uppercase tracking-wider min-w-[150px]" style={{ color: '#2d3748', borderRight: '1px solid #000000' }}>Particulars</TableHead>
                                            <TableHead className="px-2 md:px-4 py-2 text-left text-xs font-medium uppercase tracking-wider min-w-[120px]" style={{ color: '#2d3748', borderRight: '1px solid #000000' }}>Paid To / Received From</TableHead>
                                            {/* Dynamic chart of account columns */}
                                            {chartOfAccounts.map(account => (
                                                <TableHead key={account.id} className="px-1 md:px-2 py-2 text-center text-xs font-medium uppercase tracking-wider min-w-[80px] rotate-header" style={{ color: '#2d3748', borderRight: '1px solid #000000' }}>
                                                    <div className="whitespace-nowrap">
                                                        {account.name}
                                                    </div>
                                                </TableHead>
                                            ))}
                                            <TableHead className="px-2 md:px-4 py-2 text-right text-xs font-medium uppercase tracking-wider min-w-[100px]" style={{ color: '#2d3748', borderRight: '1px solid #000000' }}>Transaction Cost</TableHead>
                                            <TableHead className="px-2 md:px-4 py-2 text-right text-xs font-medium uppercase tracking-wider min-w-[80px]" style={{ color: '#2d3748', borderRight: '1px solid #000000', backgroundColor: '#fed7d7' }}>Cash Out</TableHead>
                                            <TableHead className="px-2 md:px-4 py-2 text-right text-xs font-medium uppercase tracking-wider min-w-[80px]" style={{ color: '#2d3748', borderRight: '1px solid #000000', backgroundColor: '#c6f6d5' }}>Cash In</TableHead>
                                            <TableHead className="px-2 md:px-4 py-2 text-right text-xs font-medium uppercase tracking-wider min-w-[80px]" style={{ color: '#2d3748', backgroundColor: '#bee3f8' }}>Balance</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody style={{ backgroundColor: '#ffffff' }}>
                                        {cashFloat.transactions.map((transaction, index) => {
                                            // Calculate amounts by chart of account
                                            const accountAmounts: Record<number, number> = {};

                                            chartOfAccounts.forEach(account => {
                                                accountAmounts[account.id] = 0;
                                            });

                                            transaction.items.forEach(item => {
                                                if (Object.prototype.hasOwnProperty.call(accountAmounts, item.chart_of_account.id)) {
                                                    const p = "" + item.total_price;
                                                    const price = parseFloat(p) || 0;
                                                    accountAmounts[item.chart_of_account.id] += price;
                                                }
                                            });

                                            accountAmmountsCollection.push(accountAmounts);

                                            // Get particulars
                                            const particulars = transaction.is_initial_issuance
                                                ? 'Initial Float Issuance'
                                                : (transaction.requisition?.purpose ||
                                                   transaction.items.map(item => item.description).join(', ') ||
                                                   getTransactionTypeLabel(transaction.transaction_type));

                                            // Get paid to/received from
                                            const isCashIn = transaction.transaction_type === 'float_issuance';
                                            const paidToFrom = transaction.requisition?.requester?.name ||
                                                             transaction.creator?.name ||
                                                             (isCashIn ? 'Company' : '-');

                                            const rowBgColor = index % 2 === 0 ? '#ffffff' : '#f7fafc';

                                            return (
                                                <TableRow key={transaction.id} style={{ backgroundColor: rowBgColor, borderBottom: '1px solid #e2e8f0' }}>
                                                    <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm max-w-xs truncate" style={{ color: '#2d3748', borderRight: '1px solid #000000' }}>
                                                        {new Date(transaction.created_at).toLocaleDateString('en-US', {
                                                            month: '2-digit',
                                                            day: '2-digit',
                                                            year: '2-digit'
                                                        })}
                                                    </TableCell>
                                                    <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm max-w-xs truncate" style={{ color: '#2d3748', borderRight: '1px solid #000000' }} title={particulars}>
                                                        {particulars}
                                                    </TableCell>
                                                    <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm" style={{ color: '#2d3748', borderRight: '1px solid #000000' }}>
                                                        {paidToFrom}
                                                    </TableCell>
                                                    {/* Dynamic chart of account amount columns */}
                                                    {chartOfAccounts.map(account => (
                                                        <TableCell key={account.id} className="px-1 md:px-2 py-2 text-xs md:text-sm text-right" style={{ color: '#2d3748', borderRight: '1px solid #000000' }}>
                                                            {accountAmounts[account.id] > 0 ? formatCurrency(accountAmounts[account.id]) : '-'}
                                                        </TableCell>
                                                    ))}
                                                    <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ color: '#2d3748', borderRight: '1px solid #000000' }}>
                                                        {transaction.transaction_cost ? formatCurrency(transaction.transaction_cost) : '-'}
                                                    </TableCell>
                                                    <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right font-medium" style={{ color: '#2d3748', borderRight: '1px solid #000000', backgroundColor: '#fed7d7' }}>
                                                        {transaction.cash_out > 0 ? formatCurrency(transaction.cash_out) : '-'}
                                                    </TableCell>
                                                    <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right font-medium" style={{ color: '#2d3748', borderRight: '1px solid #000000', backgroundColor: '#c6f6d5' }}>
                                                        {transaction.cash_in > 0 ? formatCurrency(transaction.cash_in) :
                                                         transaction.is_initial_issuance ? `(${formatCurrency(transaction.total_amount)})` : '-'}
                                                    </TableCell>
                                                    <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right font-semibold" style={{ backgroundColor: '#bee3f8' }}>
                                                        <span style={{ color: transaction.running_balance < (cashFloat.alert_threshold || 0) ? '#e53e3e' : '#2d3748' }}>
                                                            {formatCurrency(transaction.running_balance)}
                                                        </span>
                                                    </TableCell>
                                                </TableRow>
                                            );
                                        })}
                                        {/* Reconciliation Section */}
                                        {/* Row 1: Total (Cash Out)/In - Header with totals */}
                                        <TableRow style={{ borderTop: '2px solid #000000' }}>
                                            <TableCell rowSpan={4} className="px-2 md:px-4 py-2 text-center text-xs md:text-sm font-bold align-middle" style={{ borderRight: '2px solid #000000', backgroundColor: '#059669', color: '#1a202c' }}>
                                                <div className="transform -rotate-90 whitespace-nowrap">
                                                    Reconciliation
                                                </div>
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-left text-xs md:text-sm font-medium" style={{ borderRight: '1px solid #000000', backgroundColor: '#f1f5f9', color: '#1a202c' }}>
                                                Total (Cash Out)/In
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-center text-xs md:text-sm" style={{ borderRight: '1px solid #000000', backgroundColor: '#f1f5f9', color: '#1a202c' }}>
                                                -
                                            </TableCell>
                                            {chartOfAccounts.map(account => {
                                                let totalAccountAmount = 0;

                                                accountAmmountsCollection.forEach(element => {
                                                    for(const key in element) {
                                                        if (key == account.id.toString()) {
                                                            totalAccountAmount += element[key];
                                                        }
                                                    }
                                                });

                                                return (
                                                    <TableCell key={`total-${account.id}`} className="px-1 md:px-2 py-2 text-xs md:text-sm text-right" style={{ borderRight: '1px solid #000000', backgroundColor: '#f1f5f9', color: '#1a202c' }}>
                                                        {totalAccountAmount > 0 ? formatCurrency(totalAccountAmount) : '0.00'}
                                                    </TableCell>
                                                );
                                            })}
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ borderRight: '1px solid #000000', backgroundColor: '#f1f5f9', color: '#1a202c' }}>
                                                {formatCurrency(cashFloat.transactions.reduce((sum, t) => sum + (t.transaction_cost || 0), 0))}
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ borderRight: '1px solid #000000', backgroundColor: '#fed7d7', color: '#1a202c' }}>
                                                {formatCurrency(cashFloat.transactions.reduce((sum, t) => sum + t.cash_out, 0))}
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ borderRight: '1px solid #000000', backgroundColor: '#c6f6d5', color: '#1a202c' }}>
                                                {formatCurrency(cashFloat.transactions.reduce((sum, t) => sum + t.cash_in, 0))}
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ backgroundColor: '#bee3f8', color: '#1a202c' }}>
                                                {formatCurrency(cashFloat.current_balance)}
                                            </TableCell>
                                        </TableRow>

                                        {/* Row 2: Cash at Hand - with checkmarks and negative balance */}
                                        <TableRow style={{ backgroundColor: '#ffffff' }}>
                                            <TableCell className="px-2 md:px-4 py-2 text-left text-xs md:text-sm font-medium" style={{ borderRight: '1px solid #000000', color: '#1a202c' }}>
                                                Cash at Hand
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-center text-xs md:text-sm" style={{ borderRight: '1px solid #000000', color: '#1a202c' }}>
                                                -
                                            </TableCell>
                                            {chartOfAccounts.map(account => (
                                                <TableCell key={`cash-hand-${account.id}`} className="px-1 md:px-2 py-2 text-xs md:text-sm text-center" style={{ borderRight: '1px solid #000000' }}>
                                                    <span style={{ color: '#38a169', fontWeight: 'bold', fontSize: '16px' }}>✓</span>
                                                </TableCell>
                                            ))}
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ borderRight: '1px solid #000000', color: '#1a202c' }}>
                                                -
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ borderRight: '1px solid #000000', backgroundColor: '#fed7d7', color: '#1a202c' }}>
                                                -
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ borderRight: '1px solid #000000', backgroundColor: '#c6f6d5', color: '#1a202c' }}>
                                                -
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ backgroundColor: '#bee3f8', color: '#1a202c' }}>
                                                {formatCurrency(cashFloat.current_balance)}
                                            </TableCell>
                                        </TableRow>

                                        {/* Row 3: Petty Cash reimbursement - empty row */}
                                        <TableRow style={{ backgroundColor: '#f7fafc' }}>
                                            <TableCell className="px-2 md:px-4 py-2 text-left text-xs md:text-sm font-medium" style={{ borderRight: '1px solid #000000', color: '#1a202c' }}>
                                                Petty Cash reimbursement
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-center text-xs md:text-sm" style={{ borderRight: '1px solid #000000', color: '#1a202c' }}>
                                                -
                                            </TableCell>
                                            {chartOfAccounts.map(account => (
                                                <TableCell key={`reimbursement-${account.id}`} className="px-1 md:px-2 py-2 text-xs md:text-sm text-right" style={{ borderRight: '1px solid #000000', color: '#1a202c' }}>
                                                    -
                                                </TableCell>
                                            ))}
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ borderRight: '1px solid #000000', color: '#1a202c' }}>
                                                -
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ borderRight: '1px solid #000000', backgroundColor: '#fed7d7', color: '#1a202c' }}>
                                                -
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ borderRight: '1px solid #000000', backgroundColor: '#c6f6d5', color: '#1a202c' }}>
                                                -
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ backgroundColor: '#bee3f8', color: '#1a202c' }}>
                                                0.00
                                            </TableCell>
                                        </TableRow>

                                        {/* Row 4: Balance Carried forward - final balance */}
                                        <TableRow className="font-bold" style={{ backgroundColor: '#ffffff' }}>
                                            <TableCell className="px-2 md:px-4 py-2 text-left text-xs md:text-sm font-bold" style={{ borderRight: '1px solid #000000', color: '#1a202c' }}>
                                                Balance Carried forward
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-center text-xs md:text-sm" style={{ borderRight: '1px solid #000000', color: '#1a202c' }}>
                                                -
                                            </TableCell>
                                            {chartOfAccounts.map(account => (
                                                <TableCell key={`balance-forward-${account.id}`} className="px-1 md:px-2 py-2 text-xs md:text-sm text-right" style={{ borderRight: '1px solid #000000', color: '#1a202c' }}>
                                                    -
                                                </TableCell>
                                            ))}
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ borderRight: '1px solid #000000', color: '#1a202c' }}>
                                                -
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ borderRight: '1px solid #000000', backgroundColor: '#fed7d7', color: '#1a202c' }}>
                                                -
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right" style={{ borderRight: '1px solid #000000', backgroundColor: '#c6f6d5', color: '#1a202c' }}>
                                                -
                                            </TableCell>
                                            <TableCell className="px-2 md:px-4 py-2 text-xs md:text-sm text-right font-bold" style={{ backgroundColor: '#bee3f8', color: '#1a202c' }}>
                                                {formatCurrency(cashFloat.current_balance)}
                                            </TableCell>
                                        </TableRow>
                                    </TableBody>
                                </Table>
                            </div>
                        ) : (
                            <div className="py-8 text-center">
                                <p className="text-foreground/90">No transactions found for this cash float.</p>
                            </div>
                        )}

                        {/* Export PDF Button */}
                        <div className="mt-4 flex justify-end">
                            <Button
                                variant="outline"
                                onClick={handleExportPDF}
                                className="flex items-center space-x-2 w-full sm:w-auto"
                            >
                                <svg
                                    className="w-4 h-4"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                    />
                                </svg>
                                <span>Export as PDF</span>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
