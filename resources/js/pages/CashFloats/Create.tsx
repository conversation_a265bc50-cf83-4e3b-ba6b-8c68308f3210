import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { PageProps } from '@inertiajs/core';
import { router, usePage } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler, useState } from 'react';
import { Toaster, toast } from 'sonner';

interface Department {
    id: number;
    name: string;
}

interface Branch {
    id: number;
    name: string;
}

interface User {
    id: number;
    name?: string;
    first_name: string;
    last_name: string;
}

interface Organization {
    id: number;
    name: string;
}

interface CreateCashFloatPageProps extends PageProps {
    departments: Department[];
    branches: Branch[];
    users: User[];
    organization: Organization;
}

interface FormData {
    organization_id: number;
    branch_id: string;
    department_id: string;
    user_id: string;
    name: string;
    initial_amount: string;
    issued_at: string;
    alert_threshold: string;
    status: string;
    payment_method: string;
    account_details: string;
    reference_number: string;
    transaction_cost: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Cash Floats', href: '/cash-floats' },
    { title: 'Create Cash Float', href: '/cash-floats/create' },
];

export default function CreateCashFloat() {
    const { departments, branches, users, organization } = usePage<CreateCashFloatPageProps>().props;
    const [formData, setFormData] = useState<FormData>({
        organization_id: organization.id,
        branch_id: '',
        department_id: '',
        user_id: '',
        name: '',
        initial_amount: '',
        issued_at: new Date().toISOString().split('T')[0],
        alert_threshold: '',
        status: 'active',
        payment_method: '',
        account_details: '',
        reference_number: '',
        transaction_cost: '',
    });

    const [errors, setErrors] = useState<Record<string, string>>({});
    const [processing, setProcessing] = useState(false);

    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};

        const assignmentCount = [formData.branch_id, formData.department_id, formData.user_id].filter((id) => id && id !== '').length;

        if (assignmentCount > 1) {
            newErrors.assignment = 'Cash float can only be assigned to one of: branch, department, or user.';
        }

        if (!formData.name.trim()) {
            newErrors.name = 'Name is required';
        }

        if (!formData.initial_amount) {
            newErrors.initial_amount = 'Initial amount is required';
        } else {
            const amount = Number(formData.initial_amount);
            if (isNaN(amount) || amount <= 0) {
                newErrors.initial_amount = 'Initial amount must be a valid number greater than zero';
            }
        }

        if (!formData.issued_at) {
            newErrors.issued_at = 'Issue date is required';
        } else {
            const issueDate = new Date(formData.issued_at);
            if (isNaN(issueDate.getTime())) {
                newErrors.issued_at = 'Issue date must be a valid date';
            }
        }

        if (formData.alert_threshold) {
            const threshold = Number(formData.alert_threshold);
            if (isNaN(threshold) || threshold < 0) {
                newErrors.alert_threshold = 'Alert threshold must be a valid number greater than or equal to zero';
            }
        }

        if (!formData.status) {
            newErrors.status = 'Status is required';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit: FormEventHandler<HTMLFormElement> = async (e) => {
        e.preventDefault();

        if (!validateForm()) {
            toast.error('Please fix the errors in the form');
            return;
        }

        setProcessing(true);

        const submissionData = {
            ...formData,
            organization_id: Number(formData.organization_id),
            branch_id: formData.branch_id ? Number(formData.branch_id) : null,
            department_id: formData.department_id ? Number(formData.department_id) : null,
            user_id: formData.user_id ? Number(formData.user_id) : null,
            initial_amount: Number(formData.initial_amount),
            alert_threshold: formData.alert_threshold ? Number(formData.alert_threshold) : null,
        };

        router.post('/cash-floats', submissionData, {
            onSuccess: () => {
                toast.success('Cash float created successfully!');
            },
            onError: (serverErrors) => {
                console.error('Form submission errors:', serverErrors);
                if (typeof serverErrors === 'object' && serverErrors !== null) {
                    setErrors(serverErrors as Record<string, string>);
                }
                toast.error('Failed to create cash float. Please check the form for errors.');
            },
            onFinish: () => {
                setProcessing(false);
            },
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleCancel = () => {
        router.visit('/cash-floats');
    };

    const handleDateChange = (date: Date | undefined) => {
        if (date) {
            // Use local date in YYYY-MM-DD format to avoid timezone issues
            const localDate = date.toLocaleDateString('en-CA');
            setFormData((prev) => ({ ...prev, issued_at: localDate }));
        }
    };

    const handleBranchChange = (value: string) => {
        if (value !== 'none') {
            setFormData((prev) => ({
                ...prev,
                branch_id: value,
                department_id: '',
                user_id: '',
            }));
        } else {
            setFormData((prev) => ({ ...prev, branch_id: '' }));
        }
    };

    const handleDepartmentChange = (value: string) => {
        if (value !== 'none') {
            setFormData((prev) => ({
                ...prev,
                department_id: value,
                branch_id: '',
                user_id: '',
            }));
        } else {
            setFormData((prev) => ({ ...prev, department_id: '' }));
        }
    };

    const handleUserChange = (value: string) => {
        if (value !== 'none') {
            setFormData((prev) => ({
                ...prev,
                user_id: value,
                branch_id: '',
                department_id: '',
            }));
        } else {
            setFormData((prev) => ({ ...prev, user_id: '' }));
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Toaster />
            <div className="space-y-6">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-2xl font-semibold text-muted-foreground">Create Cash Float</h1>
                        <p className="mt-1 text-sm text-muted-foreground">Create a new cash float for {organization.name}</p>
                    </div>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Cash Float Details</CardTitle>
                        <CardDescription>
                            Enter the details for the new cash float. Fields marked with * are required.
                            <br />
                            <strong>Note:</strong> A cash float can only be assigned to one of the following: branch, department, or user. You cannot
                            assign a float to multiple entities.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {errors.assignment && (
                                <div className="rounded-md border border-destructive bg-destructive/80 p-4">
                                    <p className="text-sm text-destructive/80">{errors.assignment}</p>
                                </div>
                            )}

                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="branch_id" className={formData.department_id || formData.user_id ? 'text-muted-foreground' : ''}>
                                        Branch
                                    </Label>
                                    <Select 
                                        value={formData.branch_id || 'none'} 
                                        onValueChange={handleBranchChange}
                                        disabled={!!(formData.department_id || formData.user_id)}
                                    >
                                        <SelectTrigger className={formData.department_id || formData.user_id ? 'bg-muted-foreground text-muted-foreground' : ''}>
                                            <SelectValue placeholder="Select a branch" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="none">None</SelectItem>
                                            {branches.map((branch) => (
                                                <SelectItem key={branch.id} value={branch.id.toString()}>
                                                    {branch.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.branch_id && <p className="text-sm text-destructive/80">{errors.branch_id}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="department_id" className={formData.branch_id || formData.user_id ? 'text-muted-foreground' : ''}>
                                        Department
                                    </Label>
                                    <Select 
                                        value={formData.department_id || 'none'} 
                                        onValueChange={handleDepartmentChange}
                                        disabled={!!(formData.branch_id || formData.user_id)}
                                    >
                                        <SelectTrigger className={formData.branch_id || formData.user_id ? 'bg-muted-foreground text-muted-foreground' : ''}>
                                            <SelectValue placeholder="Select a department" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="none">None</SelectItem>
                                            {departments.map((department) => (
                                                <SelectItem key={department.id} value={department.id.toString()}>
                                                    {department.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.department_id && <p className="text-sm text-destructive/80">{errors.department_id}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="user_id" className={formData.branch_id || formData.department_id ? 'text-muted-foreground' : ''}>
                                        Assigned User
                                    </Label>
                                    <Select 
                                        value={formData.user_id || 'none'} 
                                        onValueChange={handleUserChange}
                                        disabled={!!(formData.branch_id || formData.department_id)}
                                    >
                                        <SelectTrigger className={formData.branch_id || formData.department_id ? 'bg-muted-foreground text-muted-foreground' : ''}>
                                            <SelectValue placeholder="Select a user" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="none">None</SelectItem>
                                            {users.map((user) => (
                                                <SelectItem key={user.id} value={user.id.toString()}>
                                                    {user.name || `${user.first_name} ${user.last_name}`.trim()}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.user_id && <p className="text-sm text-destructive/80">{errors.user_id}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="name">Name *</Label>
                                    <Input
                                        id="name"
                                        value={formData.name}
                                        onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                                        placeholder="Enter cash float name"
                                        aria-invalid={errors.name ? 'true' : 'false'}
                                        aria-describedby={errors.name ? 'name-error' : undefined}
                                    />
                                    {errors.name && (
                                        <p id="name-error" className="text-sm text-destructive/80">
                                            {errors.name}
                                        </p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="initial_amount">Initial Amount *</Label>
                                    <Input
                                        id="initial_amount"
                                        type="number"
                                        min="0"
                                        step="0.01"
                                        value={formData.initial_amount}
                                        onChange={(e) => setFormData((prev) => ({ ...prev, initial_amount: e.target.value }))}
                                        placeholder="Enter initial amount"
                                        aria-invalid={errors.initial_amount ? 'true' : 'false'}
                                        aria-describedby={errors.initial_amount ? 'initial-amount-error' : undefined}
                                    />
                                    {errors.initial_amount && (
                                        <p id="initial-amount-error" className="text-sm text-destructive/80">
                                            {errors.initial_amount}
                                        </p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="issued_at">Issue Date *</Label>
                                    <DatePicker
                                        id="issued_at"
                                        selected={formData.issued_at ? new Date(formData.issued_at) : undefined}
                                        onSelect={handleDateChange}
                                        placeholder="Select issue date"
                                        fromDate={new Date()}
                                        toDate={new Date(new Date().setFullYear(new Date().getFullYear() + 1))}
                                    />
                                    {errors.issued_at && <p className="text-sm text-destructive/80">{errors.issued_at}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="alert_threshold">Alert Threshold</Label>
                                    <Input
                                        id="alert_threshold"
                                        type="number"
                                        min="0"
                                        step="0.01"
                                        value={formData.alert_threshold}
                                        onChange={(e) => setFormData((prev) => ({ ...prev, alert_threshold: e.target.value }))}
                                        placeholder="Enter alert threshold"
                                        aria-invalid={errors.alert_threshold ? 'true' : 'false'}
                                        aria-describedby={errors.alert_threshold ? 'alert-threshold-error' : undefined}
                                    />
                                    {errors.alert_threshold && (
                                        <p id="alert-threshold-error" className="text-sm text-destructive/80">
                                            {errors.alert_threshold}
                                        </p>
                                    )}
                                </div>

                                    <div className="space-y-2">
                                    <Label htmlFor="status">Status *</Label>
                                    <Select value={formData.status} onValueChange={(value: string) => setFormData((prev) => ({ ...prev, status: value }))}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="active">Active</SelectItem>
                                            <SelectItem value="inactive">Inactive</SelectItem>
                                            <SelectItem value="reconciled">Reconciled</SelectItem>
                                            <SelectItem value="closed">Closed</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.status && <p className="text-sm text-destructive/80">{errors.status}</p>}
                                </div>
                            </div>

                            <div className="border-t pt-6">
                                <h3 className="mb-4 text-lg font-medium text-muted-foreground/80">Transaction Details</h3>
                                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="transaction_cost">Transaction Cost</Label>
                                        <Input
                                            id="transaction_cost"
                                            type="number"
                                            min="0"
                                            value={formData.transaction_cost}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData((prev) => ({ ...prev, transaction_cost: e.target.value }))}
                                            placeholder="Enter transaction cost "
                                            aria-invalid={errors.transaction_cost ? 'true' : 'false'}
                                            aria-describedby={errors.transaction_cost ? 'transaction-cost-error' : undefined}
                                        />
                                        {errors.transaction_cost && (
                                            <p id="transaction-cost-error" className="text-sm text-destructive/80">
                                                {errors.transaction_cost}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="payment_method">Payment Method</Label>
                                        <Select
                                            value={formData.payment_method || 'none'}
                                            onValueChange={(value: string) =>
                                                setFormData((prev) => ({ ...prev, payment_method: value === 'none' ? '' : value }))
                                            }
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select payment method" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="none">None</SelectItem>
                                                <SelectItem value="cash">Cash</SelectItem>
                                                <SelectItem value="mpesa">M-Pesa</SelectItem>
                                                <SelectItem value="bank">Bank Transfer</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.payment_method && <p className="text-sm text-destructive/80">{errors.payment_method}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="account_details">Account Details</Label>
                                        <Input
                                            id="account_details"
                                            value={formData.account_details}
                                            onChange={(e) => setFormData((prev) => ({ ...prev, account_details: e.target.value }))}
                                            placeholder="Account details for the transaction"
                                        />
                                        {errors.account_details && <p className="text-sm text-destructive/80">{errors.account_details}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="reference_number">Reference Number</Label>
                                        <Input
                                            id="reference_number"
                                            value={formData.reference_number}
                                            onChange={(e) => setFormData((prev) => ({ ...prev, reference_number: e.target.value }))}
                                            placeholder="Transaction reference number"
                                        />
                                        {errors.reference_number && <p className="text-sm text-destructive/80">{errors.reference_number}</p>}
                                    </div>
                                </div>
                            </div>

                            <div className="flex flex-col gap-4 sm:flex-row sm:justify-end sm:space-x-4">
                                <Button type="button" variant="destructive" onClick={handleCancel} disabled={processing}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                                    Create Cash Float
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
