import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { CheckCircle, Info, Upload, File, X, FileText, Building2, Shield, Calendar, IdCard, Receipt, Users } from 'lucide-react';
import { useState, useRef, type ChangeEvent, type ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface SupplierDocumentUploadProps {
    supplier?: {
        name: string;
        is_verified: boolean;
        status: string;
    };
}

interface DocumentType {
    id: string;
    name: string;
    description: string;
    icon: ReactNode;
    required: boolean;
    acceptedFormats: string;
}

interface FileWithDescription {
    file: File;
    description: string;
    documentType: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Supplier Dashboard',
        href: '/suppliers',
    },
    {
        title: 'Document Upload',
        href: '/suppliers/documents',
    },
];

const documentTypes: DocumentType[] = [
    // Required Documents First
    {
        id: 'kra_pin',
        name: 'KRA PIN Certificate',
        description: 'Tax registration certificate from Kenya Revenue Authority. Required for all business tax obligations.',
        icon: <IdCard className="h-5 w-5" />,
        required: true,
        acceptedFormats: 'PDF, JPG, PNG'
    },
    {
        id: 'business_registration',
        name: 'Business Registration Certificate',
        description: 'Official business name registration certificate from Registrar of Companies.',
        icon: <FileText className="h-5 w-5" />,
        required: true,
        acceptedFormats: 'PDF, JPG, PNG'
    },
    {
        id: 'business_permit',
        name: 'Business Permit/License',
        description: 'County-level trading license or Single Business Permit (SBP) required for business operations.',
        icon: <Shield className="h-5 w-5" />,
        required: true,
        acceptedFormats: 'PDF, JPG, PNG'
    },
    {
        id: 'tax_compliance',
        name: 'Tax Compliance Certificate',
        description: 'Current tax compliance certificate from Kenya Revenue Authority showing good standing.',
        icon: <Receipt className="h-5 w-5" />,
        required: true,
        acceptedFormats: 'PDF, JPG, PNG'
    },
    {
        id: 'directors_ids',
        name: 'Directors National IDs',
        description: 'National identification documents for all company directors and key personnel.',
        icon: <Users className="h-5 w-5" />,
        required: true,
        acceptedFormats: 'PDF, JPG, PNG'
    },
    // Optional Documents
    {
        id: 'certificate_incorporation',
        name: 'Certificate of Incorporation',
        description: 'Company incorporation certificate (for limited companies). Required if registered as a company.',
        icon: <Building2 className="h-5 w-5" />,
        required: false,
        acceptedFormats: 'PDF, JPG, PNG'
    },
    {
        id: 'vat_certificate',
        name: 'VAT Registration Certificate',
        description: 'Value Added Tax registration certificate. Required if annual turnover exceeds KES 5 million.',
        icon: <Calendar className="h-5 w-5" />,
        required: false,
        acceptedFormats: 'PDF, JPG, PNG'
    }
];

/**
 * Supplier Document Upload Component
 * 
 * This component handles the document verification process for suppliers.
 * It includes a multi-step form for uploading required and optional business documents.
 * 
 * @param {SupplierDocumentUploadProps} props The component props.
 * 
 * Features:
 * - Multi-step upload process
 * - Required vs optional document separation
 * - File validation and preview
 * - Document number input for specific documents
 * - Responsive design for mobile and desktop
 * - Skip verification option with warning
 */
export default function SupplierDocumentUpload({ supplier }: SupplierDocumentUploadProps) {
    const [step, setStep] = useState(1);
    const [uploadedFiles, setUploadedFiles] = useState<{ [key: string]: FileWithDescription[] }>({});
    const [isUploading, setIsUploading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [skipVerification, setSkipVerification] = useState(false);
    const [documentNumbers, setDocumentNumbers] = useState<{ [key: string]: string }>({
        kra_pin: '',
        business_registration: ''
    });
    const fileInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});

    /**
     * Handle document number changes for KRA PIN and Business Registration
     * These numbers are required for certain document types
     */
    const handleDocumentNumberChange = (documentType: string, value: string) => {
        setDocumentNumbers(prev => ({
            ...prev,
            [documentType]: value
        }));
    };

    /**
     * Handle file selection for a specific document type
     * Validates files and adds them to the upload queue
     */
    const handleFileSelect = (documentType: string, event: ChangeEvent<HTMLInputElement>) => {
        if (!event.target.files) return;

        try {
            const files = Array.from(event.target.files);
            const newFiles: FileWithDescription[] = files.map(file => ({
                file,
                description: '',
                documentType,
            }));

            setUploadedFiles(prev => ({
                ...prev,
                [documentType]: [...(prev[documentType] || []), ...newFiles]
            }));
            setError(null);
        } catch (err) {
            console.error('Error handling file selection:', err);
            setError('Failed to process selected files. Please try again.');
        }
    };

    // Remove a file from uploads
    const removeFile = (documentType: string, index: number) => {
        setUploadedFiles(prev => ({
            ...prev,
            [documentType]: prev[documentType]?.filter((_, i) => i !== index) || []
        }));
    };

    // Update file description
    const updateDescription = (documentType: string, index: number, description: string) => {
        setUploadedFiles(prev => ({
            ...prev,
            [documentType]: prev[documentType]?.map((item, i) => 
                i === index ? { ...item, description } : item
            ) || []
        }));
    };

    // Handle form submission
    const handleSubmit = async () => {
        if (skipVerification) {
            // Redirect to dashboard
            router.visit('/suppliers');
            return;
        }

        const totalFiles = Object.values(uploadedFiles).flat().length;
        if (totalFiles === 0) {
            setError('Please upload at least one document or choose to skip verification.');
            return;
        }

        setIsUploading(true);
        setError(null);

        try {
            const formData = new FormData();
            
            Object.entries(uploadedFiles).forEach(([documentType, files]) => {
                files.forEach((item, index) => {
                    formData.append(`files[${documentType}][${index}]`, item.file);
                    formData.append(`descriptions[${documentType}][${index}]`, item.description);
                    formData.append(`document_types[${documentType}][${index}]`, documentType);
                });
            });

            // Add document numbers
            Object.entries(documentNumbers).forEach(([documentType, number]) => {
                if (number.trim()) {
                    formData.append(`document_numbers[${documentType}]`, number);
                }
            });

            // For now, since it's frontend only, just simulate success
            setTimeout(() => {
                setIsUploading(false);
                router.visit('/suppliers', {
                    onSuccess: () => {
                        // Could show a success message here
                    }
                });
            }, 2000);

        } catch (err) {
            console.error('Error during upload:', err);
            setError('An unexpected error occurred. Please try again.');
            setIsUploading(false);
        }
    };

    // Go to next step
    const nextStep = () => {
        if (step === 1 && Object.values(uploadedFiles).flat().length === 0 && !skipVerification) {
            setError('Please upload at least one document or choose to skip verification.');
            return;
        }
        setError(null);
        setStep(step + 1);
    };

    // Go to previous step
    const prevStep = () => {
        setStep(step - 1);
        setError(null);
    };

    // Skip verification and go to dashboard
    const handleSkipVerification = () => {
        setSkipVerification(true);
        router.visit('/suppliers');
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Business Document Upload" />
            <div className="bg-background/80 container mx-auto py-4 sm:py-6 lg:py-8 px-4 sm:px-6 lg:px-8">
                <div className="mx-auto max-w-4xl">
                    <div className="mb-6 sm:mb-8 text-center">
                        <h1 className="text-foreground mb-4 text-2xl sm:text-3xl font-bold">
                            Business Document Verification
                        </h1>
                        <p className="text-muted-foreground text-base sm:text-lg">
                            Upload your business documents for verification to enhance your supplier credibility
                        </p>
                    </div>

                    {/* Skip Option Banner */}
                    <Alert className="mb-8 border-warning/50 bg-warning/10">
                        <Info className="h-4 w-4" />
                        <AlertTitle>Document Verification Required</AlertTitle>
                        <AlertDescription className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                            <div className="flex-1">
                                <p className="mb-2">
                                    Document verification is <strong>required to bid for tenders</strong>. Without verified documents, you won't be able to participate in any tender opportunities.
                                </p>
                                <p className="text-sm text-muted-foreground">
                                    <strong>Note:</strong> While you can skip verification now, your account will be automatically deleted if not verified within 7 days.
                                </p>
                            </div>
                            <Button 
                                variant="outline" 
                                size="sm" 
                                onClick={handleSkipVerification}
                                className="shrink-0"
                            >
                                Skip for Now
                            </Button>
                        </AlertDescription>
                    </Alert>

                    <div className="mb-6 sm:mb-8">
                        <div className="flex items-center justify-between">
                            <div className={`flex-1 ${step >= 1 ? 'text-primary' : 'text-muted-foreground'}`}>
                                <div className="flex items-center">
                                    <div
                                        className={`mr-2 flex h-6 w-6 sm:h-8 sm:w-8 items-center justify-center rounded-full text-xs sm:text-sm ${step >= 1 ? 'bg-primary text-white' : 'bg-muted'}`}
                                    >
                                        1
                                    </div>
                                    <span className="text-sm sm:text-base">Upload Documents</span>
                                </div>
                            </div>
                            <div className="bg-muted mx-2 h-1 w-8 sm:w-16"></div>
                            <div className={`flex-1 ${step >= 2 ? 'text-primary' : 'text-muted-foreground'}`}>
                                <div className="flex items-center">
                                    <div
                                        className={`mr-2 flex h-6 w-6 sm:h-8 sm:w-8 items-center justify-center rounded-full text-xs sm:text-sm ${step >= 2 ? 'bg-primary text-white' : 'bg-muted'}`}
                                    >
                                        2
                                    </div>
                                    <span className="text-sm sm:text-base">Confirm & Submit</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {error && (
                        <Alert className="mb-6 border-destructive/50 bg-destructive/10">
                            <AlertTitle>Error</AlertTitle>
                            <AlertDescription>{error}</AlertDescription>
                        </Alert>
                    )}

                    {/* Step 1: Upload Files */}
                    {step === 1 && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Upload Business Documents</CardTitle>
                                <CardDescription>
                                    Upload your business documents for verification. Required documents enhance your supplier profile.
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4 sm:space-y-6 p-4 sm:p-6">
                                <Alert className="mb-4 sm:mb-6">
                                    <Info className="h-4 w-4" />
                                    <AlertTitle>Kenyan Business Requirements</AlertTitle>
                                    <AlertDescription>
                                        These are standard business documents required for operating in Kenya. Upload clear, readable copies of your original documents.
                                    </AlertDescription>
                                </Alert>

                                {/* Required Documents Section */}
                                <div className="mb-6 sm:mb-8">
                                    <h3 className="text-lg sm:text-xl font-semibold text-foreground mb-4 flex items-center gap-2">
                                        <Shield className="h-5 w-5 text-primary" />
                                        Required Documents
                                    </h3>
                                    <div className="space-y-4 sm:space-y-6">
                                        {documentTypes.filter(doc => doc.required).map((docType) => (
                                            <div key={docType.id} className="border-border bg-card/50 rounded-md border p-3 sm:p-4">
                                                <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-4">
                                                    <div className="flex items-center gap-2">
                                                        {docType.icon}
                                                        <h4 className="text-foreground text-base sm:text-lg font-medium">{docType.name}</h4>
                                                    </div>
                                                    <span className="text-xs bg-primary/20 text-primary px-2 py-1 rounded w-fit">
                                                        Required
                                                    </span>
                                                </div>
                                                
                                                <p className="text-muted-foreground text-sm mb-4">{docType.description}</p>

                                                {/* Document Number Input for KRA PIN and Business Registration */}
                                                {(docType.id === 'kra_pin' || docType.id === 'business_registration') && (
                                                    <div className="mb-4">
                                                        <Label htmlFor={`number-${docType.id}`}>
                                                            {docType.id === 'kra_pin' ? 'KRA PIN Number' : 'Business Registration Number'}
                                                        </Label>
                                                        <Input
                                                            id={`number-${docType.id}`}
                                                            type="text"
                                                            placeholder={docType.id === 'kra_pin' ? 'Enter KRA PIN (e.g., A123456789B)' : 'Enter registration number'}
                                                            value={documentNumbers[docType.id] || ''}
                                                            onChange={(e) => handleDocumentNumberChange(docType.id, e.target.value)}
                                                            disabled={isUploading}
                                                            className="mt-1"
                                                        />
                                                    </div>
                                                )}

                                                <div className="mb-4">
                                                    <Label htmlFor={`file-${docType.id}`}>Select Files</Label>
                                                    <input
                                                        ref={(el) => {
                                                            fileInputRefs.current[docType.id] = el;
                                                        }}
                                                        id={`file-${docType.id}`}
                                                        type="file"
                                                        multiple
                                                        accept=".pdf,.jpg,.jpeg,.png"
                                                        onChange={(e) => handleFileSelect(docType.id, e)}
                                                        disabled={isUploading}
                                                        className={cn(
                                                            "mt-1 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0",
                                                            "file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground",
                                                            "hover:file:bg-primary/90 file:transition-colors",
                                                            "flex h-12 w-full rounded-md border border-input bg-transparent px-1 py-1",
                                                            "text-sm shadow-sm transition-colors file:border-0 file:bg-transparent",
                                                            "file:text-foreground/100",
                                                            "focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring",
                                                            "disabled:cursor-not-allowed disabled:opacity-50"
                                                        )}
                                                    />
                                                    <p className="text-sm text-muted-foreground mt-1">
                                                        {docType.acceptedFormats} (Max 10MB each)
                                                    </p>
                                                </div>

                                                {uploadedFiles[docType.id] && uploadedFiles[docType.id].length > 0 && (
                                                    <div className="space-y-3">
                                                        <Label>Uploaded Files</Label>
                                                        {uploadedFiles[docType.id].map((item, index) => (
                                                            <div key={index} className="border rounded-lg p-3 space-y-2">
                                                                <div className="flex items-center justify-between">
                                                                    <div className="flex items-center gap-2">
                                                                        <File className="h-4 w-4 text-muted-foreground" />
                                                                        <span className="text-sm font-medium">{item.file.name}</span>
                                                                        <span className="text-xs text-muted-foreground">
                                                                            ({formatFileSize(item.file.size)})
                                                                        </span>
                                                                    </div>
                                                                    <Button
                                                                        type="button"
                                                                        variant="ghost"
                                                                        size="sm"
                                                                        onClick={() => removeFile(docType.id, index)}
                                                                        disabled={isUploading}
                                                                    >
                                                                        <X className="h-4 w-4" />
                                                                    </Button>
                                                                </div>
                                                                <div>
                                                                    <Label htmlFor={`description-${docType.id}-${index}`} className="text-xs">
                                                                        Description (optional)
                                                                    </Label>
                                                                    <Textarea
                                                                        id={`description-${docType.id}-${index}`}
                                                                        placeholder="Add notes about this document..."
                                                                        value={item.description}
                                                                        onChange={(e) => updateDescription(docType.id, index, e.target.value)}
                                                                        className="mt-1"
                                                                        rows={2}
                                                                        disabled={isUploading}
                                                                    />
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* Optional Documents Section */}
                                <div className="mb-6">
                                    <h3 className="text-lg sm:text-xl font-semibold text-foreground mb-4 flex items-center gap-2">
                                        <Calendar className="h-5 w-5 text-muted-foreground" />
                                        Optional Documents
                                    </h3>
                                    <div className="space-y-4 sm:space-y-6">
                                        {documentTypes.filter(doc => !doc.required).map((docType) => (
                                            <div key={docType.id} className="border-border bg-card/30 rounded-md border p-3 sm:p-4 opacity-90">
                                                <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-4">
                                                    <div className="flex items-center gap-2">
                                                        {docType.icon}
                                                        <h4 className="text-foreground text-base sm:text-lg font-medium">{docType.name}</h4>
                                                    </div>
                                                    <span className="text-xs bg-muted text-muted-foreground px-2 py-1 rounded w-fit">
                                                        Optional
                                                    </span>
                                                </div>
                                                
                                                <p className="text-muted-foreground text-sm mb-4">{docType.description}</p>
                                                
                                                <div className="mb-4">
                                                    <Label htmlFor={`file-${docType.id}`}>Select Files</Label>
                                                    <input
                                                        ref={(el) => {
                                                            fileInputRefs.current[docType.id] = el;
                                                        }}
                                                        id={`file-${docType.id}`}
                                                        type="file"
                                                        multiple
                                                        accept=".pdf,.jpg,.jpeg,.png"
                                                        onChange={(e) => handleFileSelect(docType.id, e)}
                                                        disabled={isUploading}
                                                        className={cn(
                                                            "mt-1 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0",
                                                            "file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground",
                                                            "hover:file:bg-primary/90 file:transition-colors",
                                                            "flex h-12 w-full rounded-md border border-input bg-transparent px-1 py-1",
                                                            "text-sm shadow-sm transition-colors file:border-0 file:bg-transparent",
                                                            "file:text-foreground/100",
                                                            "focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring",
                                                            "disabled:cursor-not-allowed disabled:opacity-50"
                                                        )}
                                                    />
                                                    <p className="text-sm text-muted-foreground mt-1">
                                                        {docType.acceptedFormats} (Max 10MB each)
                                                    </p>
                                                </div>

                                                {uploadedFiles[docType.id] && uploadedFiles[docType.id].length > 0 && (
                                                    <div className="space-y-3">
                                                        <Label>Uploaded Files</Label>
                                                        {uploadedFiles[docType.id].map((item, index) => (
                                                            <div key={index} className="border rounded-lg p-3 space-y-2">
                                                                <div className="flex items-center justify-between">
                                                                    <div className="flex items-center gap-2">
                                                                        <File className="h-4 w-4 text-muted-foreground" />
                                                                        <span className="text-sm font-medium">{item.file.name}</span>
                                                                        <span className="text-xs text-muted-foreground">
                                                                            ({formatFileSize(item.file.size)})
                                                                        </span>
                                                                    </div>
                                                                    <Button
                                                                        type="button"
                                                                        variant="ghost"
                                                                        size="sm"
                                                                        onClick={() => removeFile(docType.id, index)}
                                                                        disabled={isUploading}
                                                                    >
                                                                        <X className="h-4 w-4" />
                                                                    </Button>
                                                                </div>
                                                                <div>
                                                                    <Label htmlFor={`description-${docType.id}-${index}`} className="text-xs">
                                                                        Description (optional)
                                                                    </Label>
                                                                    <Textarea
                                                                        id={`description-${docType.id}-${index}`}
                                                                        placeholder="Add notes about this document..."
                                                                        value={item.description}
                                                                        onChange={(e) => updateDescription(docType.id, index, e.target.value)}
                                                                        className="mt-1"
                                                                        rows={2}
                                                                        disabled={isUploading}
                                                                    />
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </CardContent>
                            <CardFooter className="flex flex-col sm:flex-row justify-between gap-3 p-4 sm:p-6">
                                <Button 
                                    type="button" 
                                    variant="outline" 
                                    onClick={handleSkipVerification}
                                    className="w-full sm:w-auto"
                                >
                                    Skip Verification
                                </Button>
                                <Button 
                                    type="button" 
                                    onClick={nextStep}
                                    disabled={Object.values(uploadedFiles).flat().length === 0}
                                    className="w-full sm:w-auto"
                                >
                                    Next
                                </Button>
                            </CardFooter>
                        </Card>
                    )}

                    {/* Step 2: Confirm & Submit */}
                    {step === 2 && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Confirm & Submit Documents</CardTitle>
                                <CardDescription>
                                    Review your document uploads and submit for verification.
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="p-4 sm:p-6">
                                <Alert className="mb-4 sm:mb-6">
                                    <CheckCircle className="h-4 w-4" />
                                    <AlertTitle>Ready for Verification</AlertTitle>
                                    <AlertDescription>
                                        Your documents will be reviewed by our team within 2-3 business days. You'll receive an email notification once the review is complete.
                                    </AlertDescription>
                                </Alert>

                                <div className="space-y-4">
                                    <h4 className="font-medium">Document Summary:</h4>
                                    {Object.entries(uploadedFiles).map(([docId, files]) => {
                                        const docType = documentTypes.find(d => d.id === docId);
                                        if (!docType || files.length === 0) return null;

                                        return (
                                            <div key={docId} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 p-3 bg-muted/50 rounded-md">
                                                <div className="flex items-center gap-2">
                                                    {docType.icon}
                                                    <span className="font-medium text-sm sm:text-base">{docType.name}</span>
                                                </div>
                                                <span className="text-sm text-muted-foreground">
                                                    {files.length} file{files.length > 1 ? 's' : ''}
                                                </span>
                                            </div>
                                        );
                                    })}
                                </div>
                            </CardContent>
                            <CardFooter className="flex flex-col sm:flex-row justify-between gap-3 p-4 sm:p-6">
                                <Button 
                                    type="button" 
                                    variant="outline" 
                                    onClick={prevStep}
                                    className="w-full sm:w-auto"
                                >
                                    Back
                                </Button>
                                <Button 
                                    onClick={handleSubmit}
                                    disabled={isUploading}
                                    className="bg-primary hover:bg-primary/90 w-full sm:w-auto"
                                >
                                    {isUploading ? (
                                        <>
                                            <Upload className="mr-2 h-4 w-4 animate-spin" />
                                            Uploading...
                                        </>
                                    ) : (
                                        <>
                                            <Upload className="mr-2 h-4 w-4" />
                                            Submit Documents
                                        </>
                                    )}
                                </Button>
                            </CardFooter>
                        </Card>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}