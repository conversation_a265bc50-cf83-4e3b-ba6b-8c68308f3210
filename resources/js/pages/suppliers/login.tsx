import { motion } from 'framer-motion';
import type React from 'react';

import { useForm } from '@inertiajs/react';
import { Eye, EyeOff, LoaderCircle } from 'lucide-react';
import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import InputError from '@/components/ui/input-error';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';
import TextLink from '@/components/text-link';

type SupplierLoginForm = {
    email: string;
    password: string;
    remember: boolean;
};

interface SupplierLoginProps {
    status?: string;
    canResetPassword: boolean;
}

export default function SupplierLogin({ status, canResetPassword }: SupplierLoginProps) {
    const { data, setData, errors, post, reset } = useForm<Required<SupplierLoginForm>>({
        email: '',
        password: '',
        remember: false,
    });

    const [Email, setEmail] = useState('');
    const [Password, setPassword] = useState('');
    const [Remember, setRemember] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');

    useEffect(() => {
        setData({
            email: Email,
            password: Password,
            remember: Remember,
        });
    }, [Email, Password, Remember, setData]);

    const submit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');

        if (!data.email || !data.password) {
            setError('Please fill in all fields');
            return;
        }

        setIsLoading(true);

        await new Promise((resolve) => setTimeout(resolve, 1500));

        setIsLoading(false);

        post(route('suppliers.authorize'), {
            onFinish: () => reset('password'),
        });
    };

    const inputVariants = {
        focus: { scale: 1.02, transition: { duration: 0.2 } },
        blur: { scale: 1, transition: { duration: 0.2 } },
    };

    return (
        <div className="min-h-screen max-w-full overflow-x-hidden overflow-y-auto">
            <AuthLayout
                title="Supplier Portal Sign In"
                description="Access your supplier dashboard and manage your business with us"
                reverseLayout={true}
                brandingComponent={
                    <div className="flex flex-col items-center justify-center h-full text-center">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                            className="space-y-6"
                        >
                            <div className="w-24 h-24 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                                <svg
                                    className="w-12 h-12 text-primary"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                                    />
                                </svg>
                            </div>
                            <div>
                                <h2 className="text-2xl font-bold text-foreground mb-2">
                                    Welcome Back, Partner
                                </h2>
                                <p className="text-muted-foreground">
                                    Continue your journey with Sippar's supplier network
                                </p>
                            </div>
                        </motion.div>
                    </div>
                }
                showBranding={true}
                useAcrylicBackground={true}
            >
                {error && (
                    <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-destructive/10 text-destructive mb-4 rounded-md p-3 text-sm"
                    >
                        {error}
                    </motion.div>
                )}

                {status && (
                    <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-green-50 text-green-700 mb-4 rounded-md p-3 text-sm"
                    >
                        {status}
                    </motion.div>
                )}

                <form className="flex flex-col gap-4 lg:gap-6 w-full max-w-md mx-auto px-2 sm:px-4 md:px-0" onSubmit={submit}>
                    <div className="space-y-1 lg:space-y-2">
                        <Label htmlFor="email">Email Address</Label>
                        <motion.div whileFocus="focus" variants={inputVariants}>
                            <Input
                                id="email"
                                type="email"
                                required
                                tabIndex={1}
                                value={Email}
                                onChange={(e) => setEmail(e.target.value)}
                                disabled={isLoading}
                                placeholder="Enter your email address"
                                className="w-full"
                            />
                        </motion.div>
                        <InputError message={errors.email} className="mt-1" />
                    </div>

                    <div className="space-y-1 lg:space-y-2">
                        <Label htmlFor="password">Password</Label>
                        <motion.div whileFocus="focus" variants={inputVariants} className="relative">
                            <Input
                                id="password"
                                type={showPassword ? 'text' : 'password'}
                                required
                                tabIndex={2}
                                value={Password}
                                onChange={(e) => setPassword(e.target.value)}
                                disabled={isLoading}
                                placeholder="Enter your password"
                                className="w-full pr-10"
                            />
                            <button
                                type="button"
                                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                onClick={() => setShowPassword(!showPassword)}
                                tabIndex={-1}
                            >
                                {showPassword ? (
                                    <EyeOff className="h-4 w-4 text-gray-400" />
                                ) : (
                                    <Eye className="h-4 w-4 text-gray-400" />
                                )}
                            </button>
                        </motion.div>
                        <InputError message={errors.password} className="mt-1" />
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="remember"
                                checked={Remember}
                                onCheckedChange={(checked) => setRemember(checked as boolean)}
                                disabled={isLoading}
                            />
                            <Label htmlFor="remember" className="text-sm font-normal">
                                Remember me
                            </Label>
                        </div>

                        {canResetPassword && (
                            <TextLink
                                href={route('password.request')}
                                className="text-sm text-primary hover:text-primary-700 hover:underline"
                            >
                                Forgot password?
                            </TextLink>
                        )}
                    </div>

                    <Button type="submit" className="w-full" disabled={isLoading}>
                        {isLoading ? (
                            <>
                                <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                                Signing in...
                            </>
                        ) : (
                            'Sign In'
                        )}
                    </Button>

                    <div className="text-center text-sm mt-4 px-2">
                        <span className="text-foreground/70">Don't have a supplier account? </span>
                        <TextLink
                            href={route('suppliers.create')}
                            className="text-primary hover:text-primary-700 font-medium hover:underline"
                        >
                            Register as a supplier
                        </TextLink>
                    </div>
                </form>
            </AuthLayout>
        </div>
    );
}
