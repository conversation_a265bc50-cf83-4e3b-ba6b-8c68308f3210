import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAppearance } from '@/hooks/use-appearance';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Briefcase, Building2, Moon, Sun, UserCog, Users, LogOut, FileText, Upload } from 'lucide-react';

export interface SupplierProps {
  name: string;
  contact_person: string;
  email: string;
  phone: string;
  address?: string | null;
  tax_number?: string | null;
  registration_number?: string | null;
  company_description?: string | null;
  established_year?: number | null;
  website?: string | null;
  is_verified: boolean;
  status: string;
  overall_rating: number;
  total_orders: number;
  successful_deliveries: number;
  email_verified_at?: string | null;
  verification_date?: string | null;
  last_login_at?: string | null;
  created_at: string;
  updated_at: string;
}


const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Supplier Dashboard',
        href: '/suppliers/dashboard',
    },
];

interface DashboardProps {
    supplier?: SupplierProps;
}



export default function SupplierDashboard({ supplier }: DashboardProps) {
    const { appearance, updateAppearance } = useAppearance();

    const toggleTheme = () => {
        const newAppearance = appearance === 'dark' ? 'light' : 'dark';
        updateAppearance(newAppearance);
    };

    return (
        <>
            <Head title="Supplier Dashboard" />
            <AppLayout breadcrumbs={breadcrumbs}>
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-foreground text-3xl font-bold tracking-tight">
                                Welcome back, {supplier?.name || 'Supplier'}
                            </h1>
                            <p className="text-muted-foreground">
                                Manage your business partnership with Sippar from your supplier portal.
                            </p>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button
                                variant="outline"
                                size="icon"
                                onClick={toggleTheme}
                                className="h-9 w-9"
                            >
                                {appearance === 'dark' ? (
                                    <Sun className="h-4 w-4" />
                                ) : (
                                    <Moon className="h-4 w-4" />
                                )}
                            </Button>
                            <Link href={route('suppliers.logout')} method="post" as="button" className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-9 px-3">
                                <LogOut className="h-4 w-4 mr-2" />
                                Logout
                            </Link>
                        </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                                <Briefcase className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{supplier?.total_orders || 0}</div>
                                <p className="text-xs text-muted-foreground">
                                    Orders received to date
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Successful Deliveries</CardTitle>
                                <Building2 className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{supplier?.successful_deliveries || 0}</div>
                                <p className="text-xs text-muted-foreground">
                                    Successfully completed orders
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Overall Rating</CardTitle>
                                <Users className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{supplier?.overall_rating || 'N/A'}</div>
                                <p className="text-xs text-muted-foreground">
                                    Average customer rating
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Account Status</CardTitle>
                                <UserCog className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold capitalize">{supplier?.status || 'Pending'}</div>
                                <p className="text-xs text-muted-foreground">
                                    {supplier?.is_verified ? 'Verified' : 'Pending verification'}
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Quick Actions Section */}
                    <div className="mt-8">
                        <h2 className="text-foreground text-xl font-semibold mb-4">Quick Actions</h2>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                            <Card className="hover:shadow-md transition-shadow cursor-pointer">
                                <CardContent className="p-6">
                                    <Link href="/suppliers/documents" className="block">
                                        <div className="flex items-center space-x-4">
                                            <div className="p-3 bg-primary/10 rounded-lg">
                                                <FileText className="h-6 w-6 text-primary" />
                                            </div>
                                            <div>
                                                <h3 className="font-medium">Business Documents</h3>
                                                <p className="text-sm text-muted-foreground">
                                                    Upload verification documents
                                                </p>
                                            </div>
                                        </div>
                                    </Link>
                                </CardContent>
                            </Card>

                            <Card className="hover:shadow-md transition-shadow cursor-pointer opacity-75">
                                <CardContent className="p-6">
                                    <div className="flex items-center space-x-4">
                                        <div className="p-3 bg-muted rounded-lg">
                                            <Briefcase className="h-6 w-6 text-muted-foreground" />
                                        </div>
                                        <div>
                                            <h3 className="font-medium text-muted-foreground">Order Management</h3>
                                            <p className="text-sm text-muted-foreground">
                                                Coming soon
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="hover:shadow-md transition-shadow cursor-pointer opacity-75">
                                <CardContent className="p-6">
                                    <div className="flex items-center space-x-4">
                                        <div className="p-3 bg-muted rounded-lg">
                                            <UserCog className="h-6 w-6 text-muted-foreground" />
                                        </div>
                                        <div>
                                            <h3 className="font-medium text-muted-foreground">Profile Settings</h3>
                                            <p className="text-sm text-muted-foreground">
                                                Coming soon
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                    
                </div>
            </AppLayout>
        </>
    );
}
