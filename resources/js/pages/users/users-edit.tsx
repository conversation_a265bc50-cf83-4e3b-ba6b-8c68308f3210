import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type User } from '@/types';
import { Head, useForm, usePage } from '@inertiajs/react';
import { useEffect, useState } from 'react';

interface Organization {
    id: number;
    name: string;
}

interface UsersEditProps {
    user: User & {
        roles?: { id: number; name: string }[];
        departments?: { id: number; name: string }[];
    };
    isPlatformAdmin: boolean;
    organizations?: Organization[];
    organization?: Organization;
    roles: Role[];
    departments: Department[];
}

export default function UsersEdit({ user, isPlatformAdmin, roles, departments }: UsersEditProps) {
    const { auth } = usePage<{ auth: { user: User } }>().props;
    const currentUser = auth.user;

    // Check if current user can edit this user's password (only if they are the same user)
    const canEditPassword = currentUser && currentUser.id === user.id;

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Users',
            href: '/users',
        },
        {
            title: user.first_name + ' ' + user.last_name,
            href: `/users/${user.id}`,
        },
        {
            title: 'Edit',
            href: `/users/${user.id}/edit`,
        },
    ];

    const { data, setData, put, processing, errors } = useForm({
        username: user.username,
        first_name: user.first_name,
        last_name: user.last_name,
        email: user.email,
        phone: user.phone || '',
        ...(canEditPassword && { password: '' }), 
        is_platform_admin: user.is_platform_admin,
        status: user.status,
        role_ids: (user.roles || []).map((role) => role.id),
        department_ids: (user.departments || []).map((dept) => dept.id),
    });

    const [selectedRoles, setSelectedRoles] = useState<number[]>(data.role_ids);
    const [selectedDepartments, setSelectedDepartments] = useState<number[]>(data.department_ids);

    useEffect(() => {
        setSelectedRoles(data.role_ids);
        setSelectedDepartments(data.department_ids);
    }, [user, data.role_ids, data.department_ids]);

    const handleRoleChange = (roleId: number) => {
        const newSelectedRoles = selectedRoles.includes(roleId) ? selectedRoles.filter((id) => id !== roleId) : [...selectedRoles, roleId];

        setSelectedRoles(newSelectedRoles);
        setData('role_ids', newSelectedRoles);
    };

    const handleDepartmentChange = (departmentId: number) => {
        const newSelectedDepartments = selectedDepartments.includes(departmentId)
            ? selectedDepartments.filter((id) => id !== departmentId)
            : [...selectedDepartments, departmentId];

        setSelectedDepartments(newSelectedDepartments);
        setData('department_ids', newSelectedDepartments);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(`/users/${user.id}`);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit User: ${user.first_name} ${user.last_name}`} />
            <div className="text-foreground/90 flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-2xl font-bold md:text-3xl">
                        Edit User: {user.first_name} {user.last_name}
                    </h1>
                </div>

                <form onSubmit={handleSubmit} className="mx-auto w-full max-w-3xl">
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>User Information</CardTitle>
                                <CardDescription>Update the user's details</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="first_name" className="text-sm font-medium">
                                            First Name
                                        </Label>
                                        <Input
                                            id="first_name"
                                            value={data.first_name}
                                            onChange={(e) => setData('first_name', e.target.value)}
                                        />
                                        {errors.first_name && <p className="text-sm text-destructive">{errors.first_name}</p>}
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="last_name" className="text-sm font-medium">
                                            Last Name
                                        </Label>
                                        <Input
                                            id="last_name"
                                            value={data.last_name}
                                            onChange={(e) => setData('last_name', e.target.value)}
                                        />
                                        {errors.last_name && <p className="text-sm text-destructive">{errors.last_name}</p>}
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="username" className="text-sm font-medium">
                                            Username
                                        </Label>
                                        <Input
                                            id="username"
                                            value={data.username}
                                            onChange={(e) => setData('username', e.target.value)}
                                        />
                                        {errors.username && <p className="text-sm text-destructive">{errors.username}</p>}
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="email" className="text-sm font-medium">
                                            Email
                                        </Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                        />
                                        {errors.email && <p className="text-sm text-destructive">{errors.email}</p>}
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="phone" className="text-sm font-medium">
                                            Phone
                                        </Label>
                                        <Input
                                            id="phone"
                                            value={data.phone}
                                            onChange={(e) => setData('phone', e.target.value)}
                                        />
                                        {errors.phone && <p className="text-sm text-destructive">{errors.phone}</p>}
                                    </div>

                                    {canEditPassword && (
                                        <div className="space-y-2">
                                            <Label htmlFor="password" className="text-sm font-medium">
                                                Password (Leave blank to keep current)
                                            </Label>
                                            <Input
                                                id="password"
                                                type="password"
                                                value={data.password || ''}
                                                onChange={(e) => setData('password', e.target.value)}
                                            />
                                            {errors.password && <p className="text-sm text-destructive">{errors.password}</p>}
                                        </div>
                                    )}
                                </div>

                                <div className="space-y-6">
                                    <div className="space-y-2">
                                        <Label htmlFor="status" className="text-sm font-medium">
                                            Status
                                        </Label>
                                        <Select
                                            value={data.status}
                                            onValueChange={(value: 'active' | 'invited' | 'inactive') => setData('status', value)}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="active">Active</SelectItem>
                                                <SelectItem value="invited">Invited</SelectItem>
                                                <SelectItem value="inactive">Inactive</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.status && <p className="text-sm text-destructive">{errors.status}</p>}
                                    </div>

                                    {isPlatformAdmin && (
                                        <div className="flex items-center space-x-2">
                                            <Checkbox
                                                id="is_platform_admin"
                                                checked={data.is_platform_admin}
                                                onCheckedChange={(checked: boolean) => setData('is_platform_admin', Boolean(checked))}
                                            />
                                            <Label htmlFor="is_platform_admin" className="text-sm font-medium">
                                                Platform Administrator
                                            </Label>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Roles</CardTitle>
                                <CardDescription>Assign roles to this user</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {roles.length === 0 ? (
                                    <p className="text-sm text-muted-foreground">No roles available.</p>
                                ) : (
                                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
                                        {roles.map((role) => (
                                            <div key={role.id} className="flex items-start space-x-2">
                                                <Checkbox
                                                    id={`role-${role.id}`}
                                                    checked={selectedRoles.includes(role.id)}
                                                    onCheckedChange={() => handleRoleChange(role.id)}
                                                    className="mt-1"
                                                />
                                                <Label htmlFor={`role-${role.id}`} className="text-sm font-medium">
                                                    {role.name}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                )}
                                {errors.role_ids && <p className="mt-2 text-sm text-destructive">{errors.role_ids}</p>}
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Departments</CardTitle>
                                <CardDescription>
                                    Assign departments to this user
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                {departments.length === 0 ? (
                                    <p className="text-sm text-muted-foreground">No departments available.</p>
                                ) : (
                                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
                                        {departments.map((department) => (
                                            <div key={department.id} className="flex items-start space-x-2">
                                                <Checkbox
                                                    id={`department-${department.id}`}
                                                    checked={selectedDepartments.includes(department.id)}
                                                    onCheckedChange={() => handleDepartmentChange(department.id)}
                                                    className="mt-1"
                                                />
                                                <Label
                                                    htmlFor={`department-${department.id}`}
                                                    className="text-sm font-medium"
                                                >
                                                    {department.name}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                )}
                                {errors.department_ids && <p className="mt-2 text-sm text-destructive">{errors.department_ids}</p>}
                            </CardContent>
                        </Card>

                        <div className="mt-6 flex justify-end space-x-2">
                            <Button
                                variant="destructive"
                                type="button"
                                onClick={() => window.history.back()}
                                disabled={processing}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={processing}
                            >
                                Update User
                            </Button>
                        </div>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}