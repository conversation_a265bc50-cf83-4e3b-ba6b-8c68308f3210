import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import PasswordGenerator from '@/components/PasswordGenerator';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { useState } from 'react';
import { validatePassword, getPasswordValidationMessage } from '@/utils/passwordValidation';

// interface UsersCreateProps {
//     isPlatformAdmin: boolean;
//     organizations?: Organization[];
//     organization?: Organization;
//     roles: Role[];
//     departments: Department[];
// }

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Users',
        href: '/users',
    },
    {
        title: 'Create',
        href: '/users/create',
    },
];

type Role = {
    id: number;
    name: string;
};

type Branch = {
    id: number;
    name: string;
};

type Department = {
    id: number;
    name: string;
};

export default function UsersCreate({roles, departments, branches}: { roles: Role[], departments?: Department[], branches?: Branch[] }) {
    // Define the form data type
    type UserFormData = {
        first_name: string;
        last_name: string;
        email: string;
        phone: string;
        username: string;
        password: string;
        status: string;
        role_ids: string[];
        is_platform_admin: boolean;
        department_ids: string[];
        branch_ids: string[];
    };

    const { data, setData, post, processing, errors } = useForm<UserFormData>({
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        username: '',
        password: '',
        is_platform_admin: false,
        status: 'active',
        role_ids: [],
        department_ids: [],
        branch_ids: [],
    });

    const [passwordError, setPasswordError] = useState('');

    // Real-time password validation
    const handlePasswordChange = (password: string) => {
        setData('password', password);
        if (password) {
            const message = getPasswordValidationMessage(password);
            setPasswordError(message);
        } else {
            setPasswordError('');
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Validate password before submission
        if (data.password) {
            const passwordValidation = validatePassword(data.password);
            if (!passwordValidation.isValid) {
                setPasswordError(getPasswordValidationMessage(data.password));
                return;
            }
        }

        post('/users', {
            onSuccess: () => {
                // User created successfully, form will redirect
                setPasswordError('');
            },
            onError: (errors) => {
                console.error('Form submission failed:', errors);
            }
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create User" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-foreground/90 text-2xl font-bold">Create New User</h1>
                </div>

                <form onSubmit={handleSubmit} className="mx-auto w-full max-w-3xl">
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>User Information</CardTitle>
                                <CardDescription>Enter the details for the new user</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6 p-6">
                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="first_name" className="text-sm font-medium">
                                            First Name
                                        </Label>
                                        <Input
                                            id="first_name"
                                            value={data.first_name}
                                            onChange={(e) => setData('first_name', e.target.value)}
                                        />
                                        {errors.first_name && <p className="text-sm text-destructive">{errors.first_name}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="last_name" className="text-sm font-medium">
                                            Last Name
                                        </Label>
                                        <Input
                                            id="last_name"
                                            value={data.last_name}
                                            onChange={(e) => setData('last_name', e.target.value)}
                                        />
                                        {errors.last_name && <p className="text-sm text-destructive">{errors.last_name}</p>}
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="email" className="text-sm font-medium">
                                            Email
                                        </Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                        />
                                        {errors.email && <p className="text-sm text-destructive">{errors.email}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="phone" className="text-sm font-medium">
                                            Phone
                                        </Label>
                                        <Input
                                            id="phone"
                                            value={data.phone}
                                            onChange={(e) => setData('phone', e.target.value)}
                                        />
                                        {errors.phone && <p className="text-sm text-destructive">{errors.phone}</p>}
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="username" className="text-sm font-medium">
                                            Username
                                        </Label>
                                        <Input
                                            id="username"
                                            value={data.username}
                                            onChange={(e) => setData('username', e.target.value)}
                                        />
                                        {errors.username && <p className="text-sm text-destructive">{errors.username}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <PasswordGenerator
                                            value={data.password}
                                            onChange={handlePasswordChange}
                                            disabled={processing}
                                            error={errors.password || passwordError}
                                        />

                                        {/* Password requirements display */}
                                        {data.password && (
                                            <div className="text-sm text-muted-foreground space-y-1 mt-2">
                                                <p className="font-medium">Password must contain:</p>
                                                <ul className="list-disc list-inside space-y-0.5 ml-2 text-xs">
                                                    <li>At least 8 characters</li>
                                                    <li>At least one uppercase letter (A-Z)</li>
                                                    <li>At least one lowercase letter (a-z)</li>
                                                    <li>At least one number (0-9)</li>
                                                    <li>At least one special character (!@#$%^&*()_+-=[]{}|;:,.{'<>'}?)</li>
                                                    <li>Not a commonly used password</li>
                                                </ul>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="status" className="text-sm font-medium">
                                        Status
                                    </Label>
                                    <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="active">Active</SelectItem>
                                            <SelectItem value="invited">Invited</SelectItem>
                                            <SelectItem value="inactive">Inactive</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.status && <p className="text-sm text-destructive">{errors.status}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="role_ids" className="text-sm font-medium">
                                        Role
                                    </Label>
                                    <Select
                                        value={data.role_ids.length > 0 ? data.role_ids[0].toString() : ''}
                                        onValueChange={(value) => setData('role_ids', [value])}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select role" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {roles.map((role) => (
                                                <SelectItem key={role.id} value={role.id.toString()}>
                                                    {role.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.role_ids && <p className="text-sm text-destructive">{errors.role_ids}</p>}
                                </div>

                                {branches && branches.length > 0 && (
                                    <div className="mt-4 space-y-2">
                                        <Label htmlFor="branch_ids" className="text-sm font-medium">
                                            Branch
                                        </Label>
                                        <Select
                                            value={data.branch_ids.length > 0 ? data.branch_ids[0] : ''}
                                            onValueChange={(value) => setData('branch_ids', [value])}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select branch" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {branches.map((branch) => (
                                                    <SelectItem key={branch.id} value={branch.id.toString()}>
                                                        {branch.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.branch_ids && <p className="text-sm text-destructive">{errors.branch_ids}</p>}
                                    </div>
                                )}

                                {departments && departments.length > 0 && (
                                    <div className="mt-4 space-y-2">
                                        <Label htmlFor="department_ids" className="text-sm font-medium">
                                            Department
                                        </Label>
                                        <Select
                                            value={data.department_ids.length > 0 ? data.department_ids[0] : ''}
                                            onValueChange={(value) => setData('department_ids', [value])}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select department" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {departments.map((dept) => (
                                                    <SelectItem key={dept.id} value={dept.id.toString()}>
                                                        {dept.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.department_ids && <p className="text-sm text-destructive">{errors.department_ids}</p>}
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        <div className="mt-6 flex justify-end space-x-2">
                            <Button
                                variant="destructive"
                                type="button"
                                onClick={() => window.history.back()}
                                disabled={processing}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={processing}
                            >
                                Create User
                            </Button>
                        </div>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
