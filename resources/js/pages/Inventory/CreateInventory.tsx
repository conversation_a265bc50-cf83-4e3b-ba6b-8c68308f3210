import React, { useState } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Package, Save } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import UnitSelector from '@/components/UnitSelector';
import QuantityInput from '@/components/QuantityInput';
import { PageProps } from '@inertiajs/core';

interface Branch {
    id: number;
    name: string;
    organization_id: number;
}

interface CreateInventoryPageProps extends PageProps {
    branches: Branch[];
    auth: {
        user: {
            id: number;
            name: string;
            email: string;
            permissions?: string[];
        };
    };
    errors?: Record<string, string>;
    [key: string]: unknown; // Index signature to satisfy PageProps constraint
}

interface InventoryFormData {
    branch_id: string;
    sku: string;
    name: string;
    description: string;
    unit_of_measure: string;
    quantity_on_hand: string;
    reorder_level: string;
}

type UnitType = 'discrete' | 'continuous' | 'custom';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Inventory Management', href: '/inventory' },
    { title: 'Add Item', href: '/inventory/create' },
];

export default function CreateInventory() {
    const { branches, auth, errors } = usePage<CreateInventoryPageProps>().props;
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [unitType, setUnitType] = useState<UnitType>('custom');
    const [formData, setFormData] = useState<InventoryFormData>({
        branch_id: '',
        sku: '',
        name: '',
        description: '',
        unit_of_measure: '',
        quantity_on_hand: '0',
        reorder_level: '0',
    });

    const user = auth.user;
    const userPermissions = user.permissions || [];
    const canManageInventory = userPermissions.includes('manage-inventory') || userPermissions.includes('store-keep');

    const handleInputChange = (field: keyof InventoryFormData, value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!canManageInventory) {
            return;
        }

        setIsSubmitting(true);

        const submitData = {
            ...formData,
            branch_id: formData.branch_id || null,
            quantity_on_hand: parseFloat(formData.quantity_on_hand) || 0,
            reorder_level: parseFloat(formData.reorder_level) || 0,
        };

        router.post('/inventory', submitData, {
            onError: () => {
                window.showToast?.({
                    title: 'Error',
                    message: 'Please check the form for errors',
                    type: 'error'
                });
            },
            onFinish: () => {
                setIsSubmitting(false);
            }
        });
    };

    if (!canManageInventory) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Add Inventory Item" />
                <div className="flex h-full flex-1 flex-col items-center justify-center p-4">
                    <div className="text-center">
                        <Package className="mx-auto h-12 w-12 text-muted-foreground" />
                        <h3 className="mt-2 text-sm font-semibold text-foreground">Access Denied</h3>
                        <p className="mt-1 text-sm text-muted-foreground">
                            You don't have permission to create inventory items.
                        </p>
                        <Button variant="outline" asChild className="mt-4">
                            <Link href="/inventory">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Inventory
                            </Link>
                        </Button>
                    </div>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Add Inventory Item" />

            {/* Header */}
            <div className="flex items-center justify-between mb-6">
                <Button variant="outline" asChild>
                    <Link href="/inventory">
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back to Inventory
                    </Link>
                </Button>
            </div>

            {/* Form */}
            <Card className="w-full max-w-4xl mx-auto mb-4">
                    <CardHeader>
                        <CardTitle className="text-2xl">Add Inventory Item</CardTitle>
                        <CardDescription>
                            Create a new inventory item for your organization
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6 pt-6">
                        <form id="inventory-form" onSubmit={handleSubmit} className="space-y-6">
                                {/* Branch Selection */}
                                <div className="space-y-2">
                                    <Label htmlFor="branch_id">Branch (Optional)</Label>
                                    <Select 
                                        value={formData.branch_id} 
                                        onValueChange={(value) => handleInputChange('branch_id', value)}
                                    >
                                        <SelectTrigger className={errors.branch_id ? 'border-destructive' : ''}>
                                            <SelectValue placeholder="Select a branch (optional)" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {branches.map((branch) => (
                                                <SelectItem key={branch.id} value={branch.id.toString()}>
                                                    {branch.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.branch_id && (
                                        <p className="text-sm text-destructive">{errors.branch_id}</p>
                                    )}
                                </div>

                                {/* SKU */}
                                <div className="space-y-2">
                                    <Label htmlFor="sku">
                                        SKU <span className="text-destructive">*</span>
                                    </Label>
                                    <Input
                                        id="sku"
                                        type="text"
                                        value={formData.sku}
                                        onChange={(e) => handleInputChange('sku', e.target.value)}
                                        placeholder="e.g., A4-PAPER-001"
                                        className={errors.sku ? 'border-destructive' : ''}
                                        required
                                    />
                                    {errors.sku && (
                                        <p className="text-sm text-destructive">{errors.sku}</p>
                                    )}
                                </div>

                                {/* Name */}
                                <div className="space-y-2">
                                    <Label htmlFor="name">
                                        Item Name <span className="text-destructive">*</span>
                                    </Label>
                                    <Input
                                        id="name"
                                        type="text"
                                        value={formData.name}
                                        onChange={(e) => handleInputChange('name', e.target.value)}
                                        placeholder="e.g., A4 Ream of Paper"
                                        className={errors.name ? 'border-destructive' : ''}
                                        required
                                    />
                                    {errors.name && (
                                        <p className="text-sm text-destructive">{errors.name}</p>
                                    )}
                                </div>

                                {/* Description */}
                                <div className="space-y-2">
                                    <Label htmlFor="description">Description</Label>
                                    <Textarea
                                        id="description"
                                        value={formData.description}
                                        onChange={(e) => handleInputChange('description', e.target.value)}
                                        placeholder="e.g., 80gsm white paper, 500 sheets"
                                        className={errors.description ? 'border-destructive' : ''}
                                        rows={3}
                                    />
                                    {errors.description && (
                                        <p className="text-sm text-destructive">{errors.description}</p>
                                    )}
                                </div>

                                {/* Unit of Measure */}
                                <UnitSelector
                                    value={formData.unit_of_measure}
                                    onChange={(value) => handleInputChange('unit_of_measure', value)}
                                    onUnitTypeChange={setUnitType}
                                    error={errors.unit_of_measure}
                                    required
                                />

                                {/* Stock Levels */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <QuantityInput
                                        id="quantity_on_hand"
                                        label="Initial Quantity"
                                        value={formData.quantity_on_hand}
                                        onChange={(value) => handleInputChange('quantity_on_hand', value)}
                                        unitType={unitType}
                                        error={errors.quantity_on_hand}
                                    />

                                    <QuantityInput
                                        id="reorder_level"
                                        label="Reorder Level"
                                        value={formData.reorder_level}
                                        onChange={(value) => handleInputChange('reorder_level', value)}
                                        unitType={unitType}
                                        error={errors.reorder_level}
                                    />
                                </div>
                            </form>
                        </CardContent>
                        <CardFooter className="flex justify-between">
                            <Button type="button" variant="outline" asChild>
                                <Link href="/inventory">Cancel</Link>
                            </Button>
                            <Button type="submit" disabled={isSubmitting} form="inventory-form">
                                {isSubmitting ? (
                                    <>
                                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                                        Creating...
                                    </>
                                ) : (
                                    <>
                                        <Save className="mr-2 h-4 w-4" />
                                        Create Item
                                    </>
                                )}
                            </Button>
                        </CardFooter>
                    </Card>
        </AppLayout>
    );
}
