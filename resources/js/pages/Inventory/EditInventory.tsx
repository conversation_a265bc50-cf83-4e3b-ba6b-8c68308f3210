import React, { useState, useEffect } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Package, Save } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import UnitSelector from '@/components/UnitSelector';
import QuantityInput from '@/components/QuantityInput';
import { InventoryItem } from '@/types/store-requisitions';
import { PageProps } from '@inertiajs/core';

interface Branch {
    id: number;
    name: string;
    organization_id: number;
}

interface EditInventoryPageProps extends PageProps {
    inventoryItem: InventoryItem;
    branches: Branch[];
    auth: {
        user: {
            id: number;
            name: string;
            email: string;
            permissions?: string[];
        };
    };
    errors?: Record<string, string>;
    [key: string]: unknown; // Index signature to satisfy PageProps constraint
}

interface InventoryFormData {
    branch_id: string;
    sku: string;
    name: string;
    description: string;
    unit_of_measure: string;
    reorder_level: string;
}

type UnitType = 'discrete' | 'continuous' | 'custom';

export default function EditInventory() {
    const { inventoryItem, branches, auth, errors } = usePage<EditInventoryPageProps>().props;
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [unitType, setUnitType] = useState<UnitType>('custom');
    const [formData, setFormData] = useState<InventoryFormData>({
        branch_id: inventoryItem.branch?.id?.toString() || '',
        sku: inventoryItem.sku,
        name: inventoryItem.name,
        description: inventoryItem.description || '',
        unit_of_measure: inventoryItem.unit_of_measure,
        reorder_level: inventoryItem.reorder_level.toString(),
    });

    // Initialize unit type based on existing unit of measure
    useEffect(() => {
        const determineUnitType = async () => {
            try {
                const response = await fetch('/inventory/unit-types');
                if (response.ok) {
                    const data = await response.json();
                    const lowerUnit = inventoryItem.unit_of_measure.toLowerCase().trim();
                    
                    if (data.discrete_units?.includes(lowerUnit)) {
                        setUnitType('discrete');
                    } else if (data.continuous_units?.includes(lowerUnit)) {
                        setUnitType('continuous');
                    } else {
                        setUnitType('custom');
                    }
                }
            } catch (error) {
                console.error('Failed to determine unit type:', error);
                setUnitType('custom');
            }
        };

        if (inventoryItem.unit_of_measure) {
            determineUnitType();
        }
    }, [inventoryItem.unit_of_measure]);

    const user = auth.user;
    const userPermissions = user.permissions || [];
    const canManageInventory = userPermissions.includes('manage-inventory') || userPermissions.includes('store-keep');

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Inventory Management', href: '/inventory' },
        { title: `Edit ${inventoryItem.name}`, href: `/inventory/${inventoryItem.id}/edit` },
    ];

    const handleInputChange = (field: keyof InventoryFormData, value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!canManageInventory) {
            return;
        }

        setIsSubmitting(true);

        const submitData = {
            ...formData,
            branch_id: formData.branch_id === 'none' ? null : formData.branch_id || null,
            reorder_level: parseFloat(formData.reorder_level) || 0,
        };

        router.put(`/inventory/${inventoryItem.id}`, submitData, {
            onError: () => {
                window.showToast?.({
                    title: 'Error',
                    message: 'Please check the form for errors',
                    type: 'error'
                });
            },
            onFinish: () => {
                setIsSubmitting(false);
            }
        });
    };

    if (!canManageInventory) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title={`Edit ${inventoryItem.name}`} />
                <div className="flex h-full flex-1 flex-col items-center justify-center p-4">
                    <div className="text-center">
                        <Package className="mx-auto h-12 w-12 text-muted-foreground" />
                        <h3 className="mt-2 text-sm font-semibold text-foreground">Access Denied</h3>
                        <p className="mt-1 text-sm text-muted-foreground">
                            You don't have permission to edit inventory items.
                        </p>
                        <Button variant="outline" asChild className="mt-4">
                            <Link href="/inventory">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Inventory
                            </Link>
                        </Button>
                    </div>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit ${inventoryItem.name}`} />

            {/* Header */}
            <div className="flex items-center justify-between mb-6">
                <Button variant="outline" asChild>
                    <Link href="/inventory">
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back to Inventory
                    </Link>
                </Button>
            </div>

            {/* Form */}
            <Card className="w-full max-w-4xl mx-auto mb-4">
                    <CardHeader>
                        <CardTitle className="text-2xl">Edit Inventory Item</CardTitle>
                        <CardDescription>
                            Update the details for {inventoryItem.name}
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6 pt-6">
                        <form id="inventory-edit-form" onSubmit={handleSubmit} className="space-y-6">
                                {/* Branch Selection */}
                                <div className="space-y-2">
                                    <Label htmlFor="branch_id">Branch (Optional)</Label>
                                    <Select 
                                        value={formData.branch_id} 
                                        onValueChange={(value) => handleInputChange('branch_id', value)}
                                    >
                                        <SelectTrigger className={errors.branch_id ? 'border-destructive' : ''}>
                                            <SelectValue placeholder="Select a branch (optional)" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="none">No specific branch</SelectItem>
                                            {branches.map((branch) => (
                                                <SelectItem key={branch.id} value={branch.id.toString()}>
                                                    {branch.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.branch_id && (
                                        <p className="text-sm text-destructive">{errors.branch_id}</p>
                                    )}
                                </div>

                                {/* SKU */}
                                <div className="space-y-2">
                                    <Label htmlFor="sku">
                                        SKU <span className="text-destructive">*</span>
                                    </Label>
                                    <Input
                                        id="sku"
                                        type="text"
                                        value={formData.sku}
                                        onChange={(e) => handleInputChange('sku', e.target.value)}
                                        className={errors.sku ? 'border-destructive' : ''}
                                        required
                                    />
                                    {errors.sku && (
                                        <p className="text-sm text-destructive">{errors.sku}</p>
                                    )}
                                </div>

                                {/* Name */}
                                <div className="space-y-2">
                                    <Label htmlFor="name">
                                        Item Name <span className="text-destructive">*</span>
                                    </Label>
                                    <Input
                                        id="name"
                                        type="text"
                                        value={formData.name}
                                        onChange={(e) => handleInputChange('name', e.target.value)}
                                        className={errors.name ? 'border-destructive' : ''}
                                        required
                                    />
                                    {errors.name && (
                                        <p className="text-sm text-destructive">{errors.name}</p>
                                    )}
                                </div>

                                {/* Description */}
                                <div className="space-y-2">
                                    <Label htmlFor="description">Description</Label>
                                    <Textarea
                                        id="description"
                                        value={formData.description}
                                        onChange={(e) => handleInputChange('description', e.target.value)}
                                        className={errors.description ? 'border-destructive' : ''}
                                        rows={3}
                                    />
                                    {errors.description && (
                                        <p className="text-sm text-destructive">{errors.description}</p>
                                    )}
                                </div>

                                {/* Unit of Measure */}
                                <UnitSelector
                                    value={formData.unit_of_measure}
                                    onChange={(value) => handleInputChange('unit_of_measure', value)}
                                    onUnitTypeChange={setUnitType}
                                    error={errors.unit_of_measure}
                                    required
                                />

                                {/* Current Stock (Read-only) */}
                                <div className="space-y-2">
                                    <Label>Current Stock</Label>
                                    <div className="p-3 bg-muted rounded-md">
                                        <p className="text-sm">
                                            {inventoryItem.quantity_on_hand} {inventoryItem.unit_of_measure}
                                        </p>
                                        <p className="text-xs text-muted-foreground mt-1">
                                            Use stock adjustment or goods receipt to modify stock levels
                                        </p>
                                    </div>
                                </div>

                                {/* Reorder Level */}
                                <QuantityInput
                                    id="reorder_level"
                                    label="Reorder Level"
                                    value={formData.reorder_level}
                                    onChange={(value) => handleInputChange('reorder_level', value)}
                                    unitType={unitType}
                                    error={errors.reorder_level}
                                />
                            </form>
                        </CardContent>
                        <CardFooter className="flex justify-between">
                            <Button type="button" variant="outline" asChild>
                                <Link href="/inventory">Cancel</Link>
                            </Button>
                            <Button type="submit" disabled={isSubmitting} form="inventory-edit-form">
                                {isSubmitting ? (
                                    <>
                                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                                        Updating...
                                    </>
                                ) : (
                                    <>
                                        <Save className="mr-2 h-4 w-4" />
                                        Update Item
                                    </>
                                )}
                            </Button>
                        </CardFooter>
                    </Card>
        </AppLayout>
    );
}
