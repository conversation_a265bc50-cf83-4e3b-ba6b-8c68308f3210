import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type User } from '@/types';
import { Head, useForm } from '@inertiajs/react';

interface DepartmentsEditProps {
    department: {
        id: number;
        name: string;
        organization_id: number;
        branch_id: number;
        hod_user_id: number | null;
        organization: {
            id: number;
            name: string;
        };
        branch: {
            id: number;
            name: string;
        };
        headOfDepartment?: {
            id: number;
            first_name: string;
            last_name: string;
        } | null;
    };
    isPlatformAdmin: boolean;
    organizations?: {
        id: number;
        name: string;
    }[];
    organization?: {
        id: number;
        name: string;
    };
    branches: {
        id: number;
        name: string;
        organization_id: number;
    }[];
    users: User[];
}

export default function DepartmentsEdit({ department, isPlatformAdmin, organizations, branches, users }: DepartmentsEditProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Departments',
            href: '/departments',
        },
        {
            title: department.name,
            href: `/departments/${department.id}`,
        },
        {
            title: 'Edit',
            href: `/departments/${department.id}/edit`,
        },
    ];

    const { data, setData, put, processing, errors } = useForm({
        name: department.name,
        organization_id: department.organization_id.toString(),
        branch_id: department.branch_id.toString(),
        hod_user_id: department.hod_user_id ? department.hod_user_id.toString() : '',
    });

    const filteredBranches = branches.filter(
        (branch) => branch.organization_id === (data.organization_id ? parseInt(data.organization_id.toString()) : null),
    );

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(`/departments/${department.id}`);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit Department: ${department.name}`} />
            <div className="dark:bg-background/100 flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center justify-between">
                    <h1 className="text-background/100 text-foreground/100 text-2xl font-bold">Edit Department: {department.name}</h1>
                </div>

                <form onSubmit={handleSubmit}>
                    <Card>
                        <CardHeader>
                            <CardTitle>Department Information</CardTitle>
                            <CardDescription>Update the department's details</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="space-y-2">
                                <Label htmlFor="name">Department Name</Label>
                                <Input id="name" value={data.name} onChange={(e) => setData('name', e.target.value)} />
                                {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                            </div>

                            {isPlatformAdmin && organizations && (
                                <div className="space-y-2">
                                    <Label htmlFor="organization_id">Organization</Label>
                                    <Select
                                        value={data.organization_id.toString()}
                                        onValueChange={(value) => {
                                            setData('organization_id', value);
                                            setData('branch_id', '');
                                        }}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select organization" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {organizations.map((org) => (
                                                <SelectItem key={org.id} value={org.id.toString()}>
                                                    {org.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.organization_id && <p className="text-sm text-red-500">{errors.organization_id}</p>}
                                </div>
                            )}

                            <div className="space-y-2">
                                <Label htmlFor="branch_id">Branch</Label>
                                <Select
                                    value={data.branch_id.toString()}
                                    onValueChange={(value) => setData('branch_id', value)}
                                    disabled={!data.organization_id || filteredBranches.length === 0}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select branch" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {filteredBranches.map((branch) => (
                                            <SelectItem key={branch.id} value={branch.id.toString()}>
                                                {branch.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.branch_id && <p className="text-sm text-red-500">{errors.branch_id}</p>}
                                {data.organization_id && filteredBranches.length === 0 && (
                                    <p className="text-sm text-amber-500">
                                        No branches available for the selected organization. Please create a branch first.
                                    </p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="hod_user_id">Head of Department (Optional)</Label>
                                <Select value={data.hod_user_id.toString()} onValueChange={(value) => setData('hod_user_id', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select head of department" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="0">None</SelectItem>
                                        {users.map((user) => (
                                            <SelectItem key={user.id} value={user.id.toString()}>
                                                {user.first_name} {user.last_name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.hod_user_id && <p className="text-sm text-red-500">{errors.hod_user_id}</p>}
                            </div>

                            <div className="flex justify-end space-x-2">
                                <Button variant="destructive" type="button" onClick={() => window.history.back()}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    Update Department
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </form>
            </div>
        </AppLayout>
    );
}
