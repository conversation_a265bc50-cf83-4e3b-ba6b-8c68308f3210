import { getStatusBadge } from '@/components/RequisitionStatusBadge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Pagination } from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { Edit, Eye, FileText, PlusCircle } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Requisition {
    id: number;
    requisition_number: string;
    purpose: string;
    total_amount: number;
    status: string;
    created_at: string;
}

interface Department {
    id: number;
    name: string;
}

interface HistoryProps {
    requisitions: {
        data: Requisition[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        links: {
            url: string | null;
            label: string;
            active: boolean;
        }[];
    };
    department: Department | null;
    userDepartments: Department[];
    filters: {
        department: string | null;
    };
}

export default function History({ requisitions, department, userDepartments = [], filters }: HistoryProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Requisitions',
            href: '/requisitions/history',
        },
    ];

    // Format currency
    const formatCurrency = (amount: number) => {
        return `KSH ${new Intl.NumberFormat('en-KE', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(amount)}`;
    };

    // Initialize selected department state
    const [selectedDepartment, setSelectedDepartment] = useState<string>(filters.department || department?.id?.toString() || 'all');

    // Update selected department when props change
    useEffect(() => {
        if (filters.department) {
            setSelectedDepartment(filters.department);
        } else if (department?.id) {
            setSelectedDepartment(department.id.toString());
        } else {
            setSelectedDepartment('all');
        }
    }, [filters.department, department]);

    // Handle department change
    const handleDepartmentChange = (departmentId: string) => {
        setSelectedDepartment(departmentId);

        // Save to localStorage for persistence across pages
        if (departmentId !== 'all') {
            localStorage.setItem('selectedDepartment', departmentId);
        } else {
            localStorage.removeItem('selectedDepartment');
        }

        // Navigate to history with the selected department
        router.get(
            '/requisitions/history',
            {
                department: departmentId === 'all' ? null : departmentId,
            },
            {
                preserveScroll: true,
            },
        );
    };
    
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Requisition History" />
            <div className="dark:bg-background/100 flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center justify-between">
                    <h1 className="text-foreground/100 text-background/100 text-2xl font-bold">Requisition History</h1>
                    <Button asChild className="group relative w-10 overflow-hidden transition-all duration-300 ease-in-out hover:w-auto lg:w-auto">
                        <Link href={department ? `/requisitions` : '/requisitions'}>
                            <PlusCircle className="h-4 w-4 flex-shrink-0" />
                            <span className="absolute left-10 whitespace-nowrap opacity-0 transition-all duration-300 ease-in-out group-hover:opacity-100 lg:relative lg:left-0 lg:ml-2 lg:opacity-100">
                                Create Requisition
                            </span>
                        </Link>
                    </Button>
                </div>

                {/* Department Switcher */}
                {userDepartments.length > 1 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Department Filter</CardTitle>
                            <CardDescription>View requisitions for a specific department</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center gap-4">
                                <Select value={selectedDepartment} onValueChange={handleDepartmentChange}>
                                    <SelectTrigger className="w-[250px]">
                                        <SelectValue placeholder="Select department" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Departments</SelectItem>
                                        {userDepartments.map((dept) => (
                                            <SelectItem key={dept.id} value={dept.id.toString()}>
                                                {dept.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>

                                {selectedDepartment !== 'all' && (
                                    <Button variant="outline" onClick={() => handleDepartmentChange('all')} size="sm">
                                        Clear Filter
                                    </Button>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                )}

                <Card>
                    <CardHeader>
                        <CardTitle>Your Requisitions</CardTitle>
                        <CardDescription>
                            {department ? `Requisition requests for ${department.name}` : 'All requisition requests you have submitted'}
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {requisitions.data.length === 0 ? (
                            <div className="flex flex-col items-center justify-center py-8 text-center">
                                <FileText className="text-muted-foreground mb-4 h-12 w-12" />
                                <h3 className="text-lg font-medium">No requisitions found</h3>
                                <p className="text-muted-foreground text-sm">
                                    {department
                                        ? `You haven't created any requisitions in ${department.name} yet.`
                                        : 'Create your first requisition to see it here.'}
                                </p>
                                <Button
                                    className="group relative mt-4 w-10 overflow-hidden transition-all duration-300 ease-in-out hover:w-auto lg:w-auto"
                                    asChild
                                >
                                    <Link href={department ? `/requisitions/create?department=${department.id}` : '/requisitions/create'}>
                                        <PlusCircle className="h-4 w-4 flex-shrink-0" />
                                        <span className="absolute left-10 whitespace-nowrap opacity-0 transition-all duration-300 ease-in-out group-hover:opacity-100 lg:relative lg:left-0 lg:ml-2 lg:opacity-100">
                                            Create Requisition
                                        </span>
                                    </Link>
                                </Button>
                            </div>
                        ) : (
                            <>
                                <div className="overflow-x-auto">
                                    <table className="w-full">
                                        <thead>
                                            <tr className="border-b">
                                                <th className="px-2 py-3 text-left">Requisition #</th>
                                                <th className="px-2 py-3 text-left">Date</th>
                                                <th className="px-2 py-3 text-left">Purpose</th>
                                                <th className="px-2 py-3 text-right">Amount</th>
                                                <th className="px-2 py-3 text-center">Status</th>
                                                <th className="px-2 py-3 text-right">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {requisitions.data.map((requisition) => (
                                                <tr key={requisition.id} className="border-b">
                                                    <td className="px-2 py-3">{requisition.requisition_number}</td>
                                                    <td className="px-2 py-3">{new Date(requisition.created_at).toLocaleDateString()}</td>
                                                    <td className="px-2 py-3">{requisition.purpose}</td>
                                                    <td className="px-2 py-3 text-right">{formatCurrency(requisition.total_amount)}</td>
                                                    <td className="px-2 py-3 text-center">{getStatusBadge(requisition.status)}</td>
                                                    <td className="px-2 py-3 text-right">
                                                        <div className="flex justify-end space-x-2">
                                                            <Button variant="outline" size="sm" asChild>
                                                                <Link href={`/requisitions/${requisition.id}`}>
                                                                    <Eye className="mr-1 h-4 w-4" />
                                                                    View
                                                                </Link>
                                                            </Button>
                                                            {requisition.status === 'rejected' && (
                                                                <Button variant="secondary" size="sm" asChild>
                                                                    <Link href={`/requisitions/${requisition.id}/edit-rejected`}>
                                                                        <Edit className="mr-1 h-4 w-4" />
                                                                        Edit
                                                                    </Link>
                                                                </Button>
                                                            )}
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>

                                {/* Pagination */}
                                {requisitions.last_page > 1 && (
                                    <div className="mt-4 flex justify-center">
                                        <Pagination>
                                            <Pagination.Prev href={requisitions.links[0].url || '#'} disabled={requisitions.current_page === 1} />

                                            {requisitions.links.slice(1, -1).map((link, i) => (
                                                <Pagination.Item key={i} href={link.url || '#'} isActive={link.active}>
                                                    {link.label}
                                                </Pagination.Item>
                                            ))}

                                            <Pagination.Next
                                                href={requisitions.links[requisitions.links.length - 1].url || '#'}
                                                disabled={requisitions.current_page === requisitions.last_page}
                                            />
                                        </Pagination>
                                    </div>
                                )}
                            </>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
