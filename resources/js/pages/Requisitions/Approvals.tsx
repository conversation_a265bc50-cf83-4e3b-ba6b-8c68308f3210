import { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import { Link } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Pagination } from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AttachmentsList } from '@/components/AttachmentsList';
import { ArrowLeft, CheckCircle, XCircle, Search, User, FileText } from 'lucide-react';
import { MobileTable, MobileTableHeader, MobileTableBody, MobileTableRow, MobileTableHead, MobileTableCell } from '@/components/ui/mobile-table';

interface BreadcrumbItem {
  title: string;
  href: string;
}

interface RequisitionItem {
  id: number;
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  chart_of_account_id: number;
}

interface Attachment {
  id: number;
  original_name: string;
  file_size: number;
  mime_type: string; // changed from number to string for compatibility
  description?: string;
  is_evidence: boolean;
  uploaded_at_step?: string;
  created_at: string;
  uploader: {
    id: number;
    first_name: string;
    last_name: string;
  };
}

interface Requisition {
  id: number;
  requisition_number: string;
  purpose: string;
  notes?: string;
  total_amount: number;
  status: string;
  created_at: string;
  requester_user_id: number;
  department_id: number;
  requester?: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
  };
  department?: {
    id: number;
    name: string;
  };
  items?: RequisitionItem[];
  attachments?: Attachment[];
}

interface Department {
  id: number;
  name: string;
}

interface ChartOfAccount {
  id: number;
  name: string;
  account_number: string;
  account_type: string;
}

interface ApprovalsProps {
  requisitions: {
    data: Requisition[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: {
      url: string | null;
      label: string;
      active: boolean;
    }[];
  };
  department: Department;
  userDepartments: Department[];
  selectedRequisition: Requisition | null;
  chartOfAccounts: Record<number, ChartOfAccount>;
  filters: {
    department: string | null;
    search: string | null;
    status: string | null;
    sort: string | null;
    direction: string | null;
  };
}

export default function Approvals({ requisitions, department, userDepartments, selectedRequisition, chartOfAccounts, filters }: ApprovalsProps) {
  //   const { auth } = usePage().props as any;
  const [searchQuery, setSearchQuery] = useState(filters.search || '');
  const [selectedStatus, setSelectedStatus] = useState(filters.status || 'pending_approval');
  const [selectedDepartmentId, setSelectedDepartmentId] = useState(filters.department || department?.id?.toString() || '');

  // Dialog state for approval/rejection
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogAction, setDialogAction] = useState<'approve' | 'reject' | null>(null);
  const [comments, setComments] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle search
  const handleSearch = () => {
    router.get(
      '/requisitions/approvals',
      {
        department: selectedDepartmentId,
        search: searchQuery,
        status: selectedStatus,
      },
      {
        preserveState: true,
        preserveScroll: true,
      },
    );
  };

  // Handle status change
  const handleStatusChange = (status: string) => {
    setSelectedStatus(status);
    router.get(
      '/requisitions/approvals',
      {
        department: selectedDepartmentId,
        search: searchQuery,
        status,
      },
      {
        preserveState: true,
        preserveScroll: true,
      },
    );
  };

  // Handle department change
  const handleDepartmentChange = (departmentId: string) => {
    setSelectedDepartmentId(departmentId);
    router.get(
      '/requisitions/approvals',
      {
        department: departmentId,
        search: searchQuery,
        status: selectedStatus,
      },
      {
        preserveState: true,
        preserveScroll: true,
      },
    );
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return `KSH ${new Intl.NumberFormat('en-KE', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount)}`;
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending_approval':
        return (
          <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-300 dark:bg-amber-900/30 dark:text-amber-200 dark:border-amber-700">
            Pending
          </Badge>
        );
      case 'approved':
        return (
          <Badge variant="outline" className="bg-primary/10 text-primary border-primary/30 dark:bg-primary/20 dark:text-primary dark:border-primary/50">
            Approved
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="outline" className="bg-destructive/10 text-destructive border-destructive/30 dark:bg-destructive/20 dark:text-destructive dark:border-destructive/50">
            Rejected
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-muted text-muted-foreground border-border">
            {status}
          </Badge>
        );
    }
  };

  // Open approval dialog
  const openApprovalDialog = () => {
    setDialogAction('approve');
    setComments('');
    setIsDialogOpen(true);
  };

  // Open rejection dialog
  const openRejectionDialog = () => {
    setDialogAction('reject');
    setComments('');
    setIsDialogOpen(true);
  };

  // Handle approval/rejection submission
  const handleSubmitDecision = () => {
    if (!selectedRequisition || !dialogAction) return;

    setIsSubmitting(true);
    const action = dialogAction === 'approve' ? 'approve' : 'reject';

    router.post(`/requisitions/${selectedRequisition.id}/${action}`, {
      comments: comments
    }, {
      onSuccess: () => {
        setIsDialogOpen(false);
        setComments('');
        window.showToast?.({
          title: 'Success',
          message: `Requisition ${dialogAction === 'approve' ? 'approved' : 'rejected'}`,
          type: 'success'
        });

        // Trigger immediate notification check
        if (window.enhancedPollingService) {
          window.enhancedPollingService.triggerImmediateCheck();
        }

        // Server already handles the redirect, no need for additional client-side redirect
      },
      onError: () => {
        // Errors handled by UI
        window.showToast?.({
          title: 'Error',
          message: `Failed to ${dialogAction} requisition`,
          type: 'error'
        });
      },
      onFinish: () => {
        setIsSubmitting(false);
      }
    });
  };

  // Define breadcrumbs
  const breadcrumbs: BreadcrumbItem[] = [
    {
      title: 'Dashboard',
      href: '/dashboard',
    },
    {
      title: 'Requisitions',
      href: '/requisitions/approvals',
    },
    {
      title: selectedRequisition ? 'Review Requisition' : 'Pending Approvals',
      href: '/requisitions/approvals',
    },
  ];

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={selectedRequisition ? `Review Requisition ${selectedRequisition.requisition_number}` : "Requisition Approvals"} />

      <div className="flex h-full flex-1 flex-col gap-4 p-4">
        {selectedRequisition ? (
          <>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={() => router.get('/requisitions/approvals', { department: selectedDepartmentId })}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to List
                </Button>
                <h1 className="text-foreground/100 text-2xl font-bold ml-2">Review Requisition {selectedRequisition.requisition_number}</h1>
                {getStatusBadge(selectedRequisition.status)}
              </div>

              {selectedRequisition.status === 'pending_approval' && (
                <div className="flex gap-2">
                  <Button variant="destructive" onClick={openRejectionDialog}>
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject
                  </Button>
                  <Button onClick={openApprovalDialog}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Approve
                  </Button>
                </div>
              )}
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Requisition Details</CardTitle>
                <CardDescription>
                  Created on {formatDate(selectedRequisition.created_at)} • Department: {selectedRequisition.department?.name}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Purpose</h3>
                    <p className="text-base">{selectedRequisition.purpose}</p>

                    {selectedRequisition.notes && (
                      <div className="mt-4">
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Notes</h3>
                        <p className="text-base">{selectedRequisition.notes}</p>
                      </div>
                    )}

                    <div className="mt-4">
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Requester</h3>
                      <div className="flex items-center">
                        <div className="rounded-full bg-primary/10 p-2 mr-2">
                          <User className="h-4 w-4 text-primary" />
                        </div>
                        {selectedRequisition.requester ? (
                          <p className="text-base">
                            {selectedRequisition.requester.first_name} {selectedRequisition.requester.last_name} ({selectedRequisition.requester.email})
                          </p>
                        ) : (
                          <p className="text-base">Unknown</p>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Status:</span>
                      <span>{getStatusBadge(selectedRequisition.status)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Department:</span>
                      <span>{selectedRequisition.department?.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Date Created:</span>
                      <span>{formatDate(selectedRequisition.created_at)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Total Amount:</span>
                      <span className="font-medium">{formatCurrency(selectedRequisition.total_amount)}</span>
                    </div>
                  </div>
                </div>

                <Separator className="my-6" />

                <h3 className="text-lg font-medium mb-4">Requisition Items</h3>
                <div className="overflow-x-auto">
                  <MobileTable>
                    <MobileTableHeader>
                      <tr className="border-b">
                        <MobileTableHead>Description</MobileTableHead>
                        <MobileTableHead>Category</MobileTableHead>
                        <MobileTableHead className="text-right">Quantity</MobileTableHead>
                        <MobileTableHead className="text-right">Unit Price</MobileTableHead>
                        <MobileTableHead className="text-right">Total</MobileTableHead>
                      </tr>
                    </MobileTableHeader>
                    <MobileTableBody>
                      {selectedRequisition.items?.map((item) => (
                        <MobileTableRow key={item.id}
                          primaryContent={
                            <>
                              <div className="font-medium">{item.description}</div>
                              <div className="text-xs text-muted-foreground">{chartOfAccounts[item.chart_of_account_id]?.name || `Account #${item.chart_of_account_id}`}</div>
                            </>
                          }
                          expandedContent={
                            <div className="flex flex-col gap-1">
                              <MobileTableCell label="Quantity" className="text-right">{item.quantity}</MobileTableCell>
                              <MobileTableCell label="Unit Price" className="text-right">{formatCurrency(item.unit_price)}</MobileTableCell>
                              <MobileTableCell label="Total" className="text-right">{formatCurrency(item.total_price)}</MobileTableCell>
                            </div>
                          }
                        >
                          <MobileTableCell>{item.description}</MobileTableCell>
                          <MobileTableCell>{chartOfAccounts[item.chart_of_account_id]?.name || `Account #${item.chart_of_account_id}`}</MobileTableCell>
                          <MobileTableCell className="text-right">{item.quantity}</MobileTableCell>
                          <MobileTableCell className="text-right">{formatCurrency(item.unit_price)}</MobileTableCell>
                          <MobileTableCell className="text-right">{formatCurrency(item.total_price)}</MobileTableCell>
                        </MobileTableRow>
                      ))}
                    </MobileTableBody>
                    <tfoot>
                      <tr className="font-medium">
                        <td colSpan={4} className="py-3 px-2 text-right">Total:</td>
                        <td className="py-3 px-2 text-right">{formatCurrency(selectedRequisition.total_amount)}</td>
                      </tr>
                    </tfoot>
                  </MobileTable>
                </div>

                {/* Attachments Section - View Only for Approvers */}
                {(selectedRequisition.attachments && selectedRequisition.attachments.length > 0) && (
                  <>
                    <Separator className="my-6" />
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Supporting Documents</h3>
                      <AttachmentsList
                        attachments={selectedRequisition.attachments ?? []}
                        canDelete={false}
                        className="w-full"
                      />
                    </div>
                  </>
                )}
              </CardContent>
              <CardFooter className="flex justify-between border-t p-4 bg-muted/20">
                <Button variant="outline" onClick={() => router.get('/requisitions/approvals', { department: selectedDepartmentId })}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to List
                </Button>

                {selectedRequisition.status === 'pending_approval' && (
                  <div className="flex gap-2">
                    <Button variant="destructive" onClick={openRejectionDialog}>
                      <XCircle className="mr-2 h-4 w-4" />
                      Reject
                    </Button>
                    <Button onClick={openApprovalDialog}>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Approve
                    </Button>
                  </div>
                )}
              </CardFooter>
            </Card>
          </>
        ) : (
          // List view of requisitions
          <>
            <div className="flex items-center justify-between">
              <h1 className="text-foreground/100 text-2xl font-bold">Requisition Approvals</h1>
            </div>

            {/* Filters */}
            <Card>
              <CardHeader>
                <CardTitle>Filters</CardTitle>
                <CardDescription>Filter requisitions by various criteria</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Search */}
                  <div className="flex items-center space-x-2">
                    <Input
                      placeholder="Search requisitions..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full"
                    />
                    <Button variant="outline" onClick={handleSearch}>
                      <Search className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Status filter */}
                  <Select value={selectedStatus} onValueChange={handleStatusChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending_approval">Pending Approval</SelectItem>
                      <SelectItem value="approved">Approved</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                      <SelectItem value="all">All Statuses</SelectItem>
                    </SelectContent>
                  </Select>

                  {/* Department filter - only show if user is HOD for multiple departments */}
                  {userDepartments.length > 1 && (
                    <Select value={selectedDepartmentId} onValueChange={handleDepartmentChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select department" />
                      </SelectTrigger>
                      <SelectContent>
                        {userDepartments.map(dept => (
                          <SelectItem key={dept.id} value={dept.id.toString()}>
                            {dept.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Requisitions List */}
            <Card>
              <CardHeader>
                <CardTitle>
                  {selectedStatus === 'pending_approval' ? 'Pending Approvals' :
                    selectedStatus === 'approved' ? 'Approved Requisitions' :
                      selectedStatus === 'rejected' ? 'Rejected Requisitions' : 'All Requisitions'}
                </CardTitle>
                <CardDescription>
                  {department ? `Requisitions from ${department.name} department` : 'All department requisitions'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {requisitions.data.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    {selectedStatus === 'pending_approval' ? (
                      <>
                        <CheckCircle className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium">No pending requisitions</h3>
                        <p className="text-sm text-muted-foreground">
                          All requisitions have been processed.
                        </p>
                      </>
                    ) : selectedStatus === 'approved' ? (
                      <>
                        <CheckCircle className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium">No approved requisitions</h3>
                        <p className="text-sm text-muted-foreground">
                          You haven't approved any requisitions yet.
                        </p>
                      </>
                    ) : selectedStatus === 'rejected' ? (
                      <>
                        <XCircle className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium">No rejected requisitions</h3>
                        <p className="text-sm text-muted-foreground">
                          You haven't rejected any requisitions yet.
                        </p>
                      </>
                    ) : (
                      <>
                        <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium">No requisitions found</h3>
                        <p className="text-sm text-muted-foreground">
                          Try adjusting your filters to see more results.
                        </p>
                      </>
                    )}
                  </div>
                ) : (
                  <>
                    <MobileTable>
                      <MobileTableHeader>
                        <tr className="border-b">
                          <MobileTableHead>Requisition #</MobileTableHead>
                          <MobileTableHead>Requester</MobileTableHead>
                          <MobileTableHead>Purpose</MobileTableHead>
                          <MobileTableHead className="text-right">Amount</MobileTableHead>
                          <MobileTableHead className="text-center">Date</MobileTableHead>
                          <MobileTableHead className="text-center">Status</MobileTableHead>
                          <MobileTableHead className="text-right">Actions</MobileTableHead>
                        </tr>
                      </MobileTableHeader>
                      <MobileTableBody>
                        {requisitions.data.map((requisition) => (
                          <MobileTableRow
                            key={requisition.id}
                            primaryContent={
                              <>
                                <div className="flex items-center justify-between">
                                  <span className="font-medium">{requisition.requisition_number}</span>
                                  {getStatusBadge(requisition.status)}
                                </div>
                                <div className="text-sm text-muted-foreground">{requisition.purpose}</div>
                                <div className="flex justify-between mt-2">
                                  <span className="text-xs">{requisition.requester ? `${requisition.requester.first_name} ${requisition.requester.last_name}` : 'Unknown'}</span>
                                  <span className="text-xs">{formatCurrency(requisition.total_amount)}</span>
                                </div>
                              </>
                            }
                            expandedContent={
                              <div className="space-y-2">
                                <MobileTableCell label="Date">{formatDate(requisition.created_at)}</MobileTableCell>
                                <MobileTableCell label="Status">{getStatusBadge(requisition.status)}</MobileTableCell>
                                <MobileTableCell label="Actions">
                                  <Button variant="outline" size="sm" asChild>
                                    <Link href={`/requisitions/approvals?department=${selectedDepartmentId}&requisition=${requisition.id}`}>
                                      {requisition.status === 'pending_approval' ? 'Review' : 'View'}
                                    </Link>
                                  </Button>
                                </MobileTableCell>
                              </div>
                            }
                          >
                            <MobileTableCell>{requisition.requisition_number}</MobileTableCell>
                            <MobileTableCell>{requisition.requester ? `${requisition.requester.first_name} ${requisition.requester.last_name}` : 'Unknown'}</MobileTableCell>
                            <MobileTableCell>{requisition.purpose}</MobileTableCell>
                            <MobileTableCell className="text-right">{formatCurrency(requisition.total_amount)}</MobileTableCell>
                            <MobileTableCell className="text-center">{formatDate(requisition.created_at)}</MobileTableCell>
                            <MobileTableCell className="text-center">{getStatusBadge(requisition.status)}</MobileTableCell>
                            <MobileTableCell className="text-right">
                              <Button variant="outline" size="sm" asChild>
                                <Link href={`/requisitions/approvals?department=${selectedDepartmentId}&requisition=${requisition.id}`}>
                                  {requisition.status === 'pending_approval' ? 'Review' : 'View'}
                                </Link>
                              </Button>
                            </MobileTableCell>
                          </MobileTableRow>
                        ))}
                      </MobileTableBody>
                    </MobileTable>
                  </>
                )}

                {/* Pagination */}
                {requisitions.last_page > 1 && (
                  <div className="mt-4 flex justify-center">
                    <Pagination>
                      <Pagination.Prev href={requisitions.links[0].url || '#'} disabled={requisitions.current_page === 1} />

                      {requisitions.links.slice(1, -1).map((link, i) => (
                        <Pagination.Item key={i} href={link.url || '#'} isActive={link.active}>
                          {link.label}
                        </Pagination.Item>
                      ))}

                      <Pagination.Next
                        href={requisitions.links[requisitions.links.length - 1].url || '#'}
                        disabled={requisitions.current_page === requisitions.last_page}
                      />
                    </Pagination>
                  </div>
                )}
              </CardContent>
            </Card>
          </>
        )}
      </div>

      {/* Approval/Rejection Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{dialogAction === 'approve' ? 'Approve Requisition' : 'Reject Requisition'}</DialogTitle>
            <DialogDescription>
              {dialogAction === 'approve'
                ? 'Are you sure you want to approve this requisition?'
                : 'Are you sure you want to reject this requisition?'}
            </DialogDescription>
          </DialogHeader>

          {selectedRequisition && (
            <div className="py-4">
              <div className="mb-4">
                <p className="text-muted-foreground text-sm font-medium">Requisition Number:</p>
                <p className='text-foreground/90'>{selectedRequisition.requisition_number}</p>
              </div>
              <div className="mb-4">
                <p className="text-muted-foreground text-sm font-medium">Purpose:</p>
                <p className='text-foreground/90'>{selectedRequisition.purpose}</p>
              </div>
              <div className="mb-4">
                <p className="text-muted-foreground text-sm font-medium">Amount:</p>
                <p className='text-foreground/90'>{formatCurrency(selectedRequisition.total_amount)}</p>
              </div>
              <div className="mb-4">
                <p className="text-muted-foreground text-sm font-medium">Comments:</p>
                <Textarea
                  placeholder={dialogAction === 'approve' ? 'Add any approval comments ' : 'Reason for rejection (required)'}
                  value={comments}
                  onChange={(e) => setComments(e.target.value)}
                  className="mt-2 text-foreground/90"
                  required={dialogAction === 'reject'}
                />
                {dialogAction === 'reject' && !comments.trim() && (
                  <p className="mt-1 text-sm text-destructive">Please provide a reason for rejection</p>
                )}
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button
              variant={dialogAction === 'approve' ? 'default' : 'destructive'}
              onClick={handleSubmitDecision}
              disabled={(dialogAction === 'reject' && !comments.trim()) || isSubmitting}
            >
              {isSubmitting ? 'Processing...' : dialogAction === 'approve' ? 'Approve' : 'Reject'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
}
