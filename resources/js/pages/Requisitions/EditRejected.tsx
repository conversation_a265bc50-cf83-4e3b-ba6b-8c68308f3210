import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { formatCurrency } from '@/lib/utils';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { InfoIcon, PlusCircle, Trash2 } from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface ChartOfAccount {
    id: number;
    name: string;
    code?: string;
    account_type: string;
}

interface RequisitionItem {
    id: number;
    requisition_id: number;
    chart_of_account_id: number;
    description: string;
    quantity: number;
    unit_price: number;
    total_price: number;
}

interface Department {
    id: number;
    name: string;
}

interface Requisition {
    id: number;
    requisition_number: string;
    department_id: number;
    department: Department;
    purpose: string;
    notes: string | null;
    total_amount: number;
    status: string;
    items: RequisitionItem[];
}

interface EditRejectedProps {
    requisition: Requisition;
    rejectionComments: string | null;
    chartOfAccounts: ChartOfAccount[];
    userDepartments: Department[];
}

export default function EditRejected({ requisition, rejectionComments, chartOfAccounts }: EditRejectedProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Requisitions',
            href: '/requisitions/history',
        },
        {
            title: 'Edit Rejected Requisition',
            href: `/requisitions/${requisition.id}/edit-rejected`,
        },
    ];

    // Initialize form with requisition data
    const { data, setData, post, processing, errors } = useForm({
        purpose: requisition.purpose,
        notes: requisition.notes || '',
        comments: '',
        requisition_items: requisition.items.map((item) => ({
            description: item.description,
            quantity: item.quantity,
            unit_price: item.unit_price,
            chart_of_account_id: item.chart_of_account_id,
        })),
    });

    // Helper function to get error for dynamic keys
    const getError = (key: string): string | undefined => {
        return (errors as Record<string, string>)[key];
    };

    // Calculate total amount
    const [totalAmount, setTotalAmount] = useState<number>(0);

    useEffect(() => {
        const total = data.requisition_items.reduce((sum, item) => {
            return sum + Number(item.quantity) * Number(item.unit_price);
        }, 0);
        setTotalAmount(total);
    }, [data.requisition_items]);

    // Add a new item
    const addItem = () => {
        setData('requisition_items', [
            ...data.requisition_items,
            {
                description: '',
                quantity: 1,
                unit_price: 0,
                chart_of_account_id: chartOfAccounts[0]?.id || 0,
            },
        ]);
    };

    // Remove an item
    const removeItem = (index: number) => {
        const updatedItems = [...data.requisition_items];
        updatedItems.splice(index, 1);
        setData('requisition_items', updatedItems);
    };

    // Update an item field
    const updateItem = (index: number, field: string, value: string | number) => {
        const updatedItems = [...data.requisition_items];
        updatedItems[index] = {
            ...updatedItems[index],
            [field]: value,
        };
        setData('requisition_items', updatedItems);
    };

    // Handle form submission
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('requisitions.update-rejected', requisition.id));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Edit Rejected Requisition" />
            <div className="dark:bg-background/100 flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center justify-between">
                    <h1 className="text-foreground/100 text-background/100 text-2xl font-bold">Edit Rejected Requisition</h1>
                </div>

                {rejectionComments && (
                    <Alert className="border-red-200 bg-red-50">
                        <InfoIcon className="h-4 w-4 text-red-500" />
                        <AlertTitle className="text-red-700">Rejection Reason</AlertTitle>
                        <AlertDescription className="text-red-600">{rejectionComments}</AlertDescription>
                    </Alert>
                )}

                <form onSubmit={handleSubmit}>
                    <Card>
                        <CardHeader>
                            <CardTitle>Requisition Details</CardTitle>
                            <CardDescription>Edit the requisition details based on the rejection feedback</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2">
                                <div>
                                    <Label htmlFor="requisition_number">Requisition Number</Label>
                                    <Input id="requisition_number" value={requisition.requisition_number} disabled />
                                </div>
                                <div>
                                    <Label htmlFor="department">Department</Label>
                                    <Input id="department" value={requisition.department.name} disabled />
                                </div>
                            </div>

                            <div className="mb-6">
                                <Label htmlFor="purpose">
                                    Purpose <span className="text-red-500">*</span>
                                </Label>
                                <Textarea
                                    id="purpose"
                                    value={data.purpose}
                                    onChange={(e) => setData('purpose', e.target.value)}
                                    rows={3}
                                    className={errors.purpose ? 'border-red-500' : ''}
                                />
                                {errors.purpose && <p className="mt-1 text-sm text-red-500">{errors.purpose}</p>}
                            </div>

                            <div className="mb-6">
                                <Label htmlFor="notes">Additional Notes</Label>
                                <Textarea id="notes" value={data.notes} onChange={(e) => setData('notes', e.target.value)} rows={2} />
                            </div>

                            <div className="mb-6">
                                <div className="mb-2 flex items-center justify-between">
                                    <Label>
                                        Items <span className="text-red-500">*</span>
                                    </Label>
                                    <Button type="button" variant="outline" size="sm" onClick={addItem}>
                                        <PlusCircle className="mr-1 h-4 w-4" />
                                        Add Item
                                    </Button>
                                </div>

                                {data.requisition_items.length === 0 ? (
                                    <div className="rounded-md border py-4 text-center">
                                        <p className="text-gray-500">No items added yet. Click "Add Item" to add your first item.</p>
                                    </div>
                                ) : (
                                    <div className="overflow-x-auto">
                                        <table className="w-full border-collapse">
                                            <thead>
                                                <tr className="border-b">
                                                    <th className="px-2 py-2 text-left">Category</th>
                                                    <th className="px-2 py-2 text-left">Description</th>
                                                    <th className="px-2 py-2 text-right">Quantity</th>
                                                    <th className="px-2 py-2 text-right">Unit Price</th>
                                                    <th className="px-2 py-2 text-right">Total</th>
                                                    <th className="px-2 py-2 text-center">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {data.requisition_items.map((item, index) => (
                                                    <tr key={index} className="border-b">
                                                        <td className="px-2 py-2">
                                                            <Select
                                                                value={item.chart_of_account_id.toString()}
                                                                onValueChange={(value) => updateItem(index, 'chart_of_account_id', parseInt(value))}
                                                            >
                                                                <SelectTrigger
                                                                    className={
                                                                        getError(`requisition_items.${index}.chart_of_account_id`)
                                                                            ? 'border-red-500'
                                                                            : ''
                                                                    }
                                                                >
                                                                    <SelectValue placeholder="Select category" />
                                                                </SelectTrigger>
                                                                <SelectContent>
                                                                    {chartOfAccounts.map((coa) => (
                                                                        <SelectItem key={coa.id} value={coa.id.toString()}>
                                                                            {coa.name}
                                                                        </SelectItem>
                                                                    ))}
                                                                </SelectContent>
                                                            </Select>
                                                            {getError(`requisition_items.${index}.chart_of_account_id`) && (
                                                                <p className="mt-1 text-sm text-red-500">
                                                                    {getError(`requisition_items.${index}.chart_of_account_id`)}
                                                                </p>
                                                            )}
                                                        </td>
                                                        <td className="px-2 py-2">
                                                            <Input
                                                                value={item.description}
                                                                onChange={(e) => updateItem(index, 'description', e.target.value)}
                                                                className={getError(`requisition_items.${index}.description`) ? 'border-red-500' : ''}
                                                            />
                                                            {getError(`requisition_items.${index}.description`) && (
                                                                <p className="mt-1 text-sm text-red-500">
                                                                    {getError(`requisition_items.${index}.description`)}
                                                                </p>
                                                            )}
                                                        </td>
                                                        <td className="px-2 py-2">
                                                            <Input
                                                                type="number"
                                                                min="1"
                                                                value={item.quantity}
                                                                onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 0)}
                                                                className={`text-right ${getError(`requisition_items.${index}.quantity`) ? 'border-red-500' : ''}`}
                                                            />
                                                            {getError(`requisition_items.${index}.quantity`) && (
                                                                <p className="mt-1 text-sm text-red-500">
                                                                    {getError(`requisition_items.${index}.quantity`)}
                                                                </p>
                                                            )}
                                                        </td>
                                                        <td className="px-2 py-2">
                                                            <Input
                                                                type="number"
                                                                min="0"
                                                                step="0.01"
                                                                value={item.unit_price}
                                                                onChange={(e) => updateItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                                                                className={`text-right ${getError(`requisition_items.${index}.unit_price`) ? 'border-red-500' : ''}`}
                                                            />
                                                            {getError(`requisition_items.${index}.unit_price`) && (
                                                                <p className="mt-1 text-sm text-red-500">
                                                                    {getError(`requisition_items.${index}.unit_price`)}
                                                                </p>
                                                            )}
                                                        </td>
                                                        <td className="px-2 py-2 text-right">{formatCurrency(item.quantity * item.unit_price)}</td>
                                                        <td className="px-2 py-2 text-center">
                                                            <Button
                                                                type="button"
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() => removeItem(index)}
                                                                disabled={data.requisition_items.length === 1}
                                                            >
                                                                <Trash2 className="h-4 w-4 text-red-500" />
                                                            </Button>
                                                        </td>
                                                    </tr>
                                                ))}
                                                <tr className="font-bold">
                                                    <td colSpan={4} className="px-2 py-2 text-right">
                                                        Total:
                                                    </td>
                                                    <td className="px-2 py-2 text-right">{formatCurrency(totalAmount)}</td>
                                                    <td></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                )}
                                {errors.requisition_items && <p className="mt-1 text-sm text-red-500">{errors.requisition_items}</p>}
                            </div>

                            <div className="mb-6">
                                <Label htmlFor="comments">
                                    Resubmission Comments <span className="text-red-500">*</span>
                                </Label>
                                <Textarea
                                    id="comments"
                                    value={data.comments}
                                    onChange={(e) => setData('comments', e.target.value)}
                                    rows={3}
                                    placeholder="Explain what changes you've made to address the rejection reasons"
                                    className={errors.comments ? 'border-red-500' : ''}
                                />
                                {errors.comments && <p className="mt-1 text-sm text-red-500">{errors.comments}</p>}
                            </div>

                            <div className="flex justify-end gap-2">
                                <Button type="button" variant="destructive" onClick={() => window.history.back()}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    Resubmit Requisition
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </form>
            </div>
        </AppLayout>
    );
}
