import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Pagination } from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Edit, Eye, Loader2, MoreHorizontal, PlusCircle, Search, Trash } from 'lucide-react';
import { useState } from 'react';

interface ChartOfAccount {
    id: number;
    organization_id: number;
    branch_id: number | null;
    code: string | null;
    name: string;
    description: string | null;
    spending_limit: number | null;
    limit_period: string | null;
    is_active: boolean;
    parent_id: number | null;
    account_type: string;
    created_at: string;
    updated_at: string;
    organization?: {
        id: number;
        name: string;
    };
    branch?: {
        id: number;
        name: string;
    };
    parent?: {
        id: number;
        name: string;
    };
}

interface Organization {
    id: number;
    name: string;
}

interface Props {
    chartOfAccounts: {
        data?: ChartOfAccount[];
        links?: {
            url: string | null;
            label: string;
            active: boolean;
        }[];
        meta?: {
            current_page: number;
            from: number;
            last_page: number;
            links: {
                url: string | null;
                label: string;
                active: boolean;
            }[];
            path: string;
            per_page: number;
            to: number;
            total: number;
        };
    };
    organization?: Organization;
    filters: {
        search?: string;
        account_type?: string;
        is_active?: boolean;
        sort?: string;
        direction?: string;
    };
    isPlatformAdmin: boolean;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Chart of Accounts', href: '/chart-of-accounts' },
];

export default function ChartOfAccountsIndex({ chartOfAccounts, organization, filters, isPlatformAdmin }: Props) {
    const { processing } = usePage<{ processing?: boolean }>().props;
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [accountType, setAccountType] = useState(filters.account_type || 'all');
    const [isActive, setIsActive] = useState(filters.is_active === undefined ? 'all' : filters.is_active ? 'true' : 'false');
    const [perPage, setPerPage] = useState(chartOfAccounts.meta?.per_page?.toString() || '15');

    const handleSearch = () => {
        router.get(
            '/chart-of-accounts',
            {
                search: searchQuery,
                account_type: accountType === 'all' ? undefined : accountType,
                is_active: isActive === 'all' ? undefined : isActive === 'true',
                per_page: perPage,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handlePerPageChange = (value: string) => {
        setPerPage(value);
        router.get(
            '/chart-of-accounts',
            {
                search: searchQuery,
                account_type: accountType === 'all' ? undefined : accountType,
                is_active: isActive === 'all' ? undefined : isActive === 'true',
                per_page: value,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleDelete = (id: number) => {
        if (confirm('Are you sure you want to delete this chart of account?')) {
            router.delete(`/chart-of-accounts/${id}`);
        }
    };

    const getAccountTypeLabel = (type: string) => {
        switch (type) {
            case 'asset':
                return (
                    <Badge variant="outline" className="border-blue-200 bg-blue-50 text-blue-700">
                        Asset
                    </Badge>
                );
            case 'liability':
                return (
                    <Badge variant="outline" className="border-red-200 bg-red-50 text-red-700">
                        Liability
                    </Badge>
                );
            case 'equity':
                return (
                    <Badge variant="outline" className="border-purple-200 bg-purple-50 text-purple-700">
                        Equity
                    </Badge>
                );
            case 'revenue':
                return (
                    <Badge variant="outline" className="border-green-200 bg-green-50 text-green-700">
                        Revenue
                    </Badge>
                );
            case 'expense':
                return (
                    <Badge variant="outline" className="border-amber-200 bg-amber-50 text-amber-700">
                        Expense
                    </Badge>
                );
            default:
                return <Badge variant="outline">{type}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Chart of Accounts" />
            <div className="dark:bg-background/100 flex h-full flex-1 flex-col gap-4 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-foreground/100 text-background/100 text-2xl font-bold">Chart of Accounts</h1>
                        <p className="text-muted-foreground">
                            {isPlatformAdmin
                                ? 'Manage chart of accounts across all organizations'
                                : `Manage chart of accounts for ${organization?.name}`}
                        </p>
                    </div>
                    <Button asChild className="group relative w-10 overflow-hidden transition-all duration-300 ease-in-out hover:w-auto lg:w-auto">
                        <Link href="/chart-of-accounts/create">
                            <PlusCircle className="h-4 w-4 flex-shrink-0" />
                            <span className="absolute left-10 whitespace-nowrap opacity-0 transition-all duration-300 ease-in-out group-hover:opacity-100 lg:relative lg:left-0 lg:ml-2 lg:opacity-100">
                                New Account
                            </span>
                        </Link>
                    </Button>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filters</CardTitle>
                        <CardDescription>Filter chart of accounts by various criteria</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-col gap-4 md:flex-row">
                            <div className="flex-1">
                                <Input
                                    placeholder="Search by name or code"
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="w-full"
                                />
                            </div>
                            <div className="w-full md:w-64">
                                <Select value={accountType} onValueChange={setAccountType}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Account Type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Types</SelectItem>
                                        <SelectItem value="asset">Asset</SelectItem>
                                        <SelectItem value="liability">Liability</SelectItem>
                                        <SelectItem value="equity">Equity</SelectItem>
                                        <SelectItem value="revenue">Revenue</SelectItem>
                                        <SelectItem value="expense">Expense</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="w-full md:w-64">
                                <Select value={isActive} onValueChange={setIsActive}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Status</SelectItem>
                                        <SelectItem value="true">Active</SelectItem>
                                        <SelectItem value="false">Inactive</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="w-full md:w-32">
                                <Select value={perPage} onValueChange={handlePerPageChange}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Per Page" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="10">10</SelectItem>
                                        <SelectItem value="15">15</SelectItem>
                                        <SelectItem value="25">25</SelectItem>
                                        <SelectItem value="50">50</SelectItem>
                                        <SelectItem value="100">100</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <Button onClick={handleSearch} disabled={processing} className="w-full md:w-auto">
                                {processing ? (
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                ) : (
                                    <Search className="mr-2 h-4 w-4" />
                                )}
                                Search
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Chart of Accounts Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Chart of Accounts</CardTitle>
                        <CardDescription>
                            {chartOfAccounts.meta ? (
                                <>
                                    Showing {chartOfAccounts.meta.from || 0} to {chartOfAccounts.meta.to || 0} of {chartOfAccounts.meta.total} accounts
                                    {chartOfAccounts.meta.last_page > 1 && (
                                        <span className="ml-2">
                                            (Page {chartOfAccounts.meta.current_page} of {chartOfAccounts.meta.last_page})
                                        </span>
                                    )}
                                </>
                            ) : (
                                'No accounts found'
                            )}
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Code</TableHead>
                                    <TableHead>Name</TableHead>
                                    <TableHead>Type</TableHead>
                                    <TableHead>Parent</TableHead>
                                    {isPlatformAdmin && <TableHead>Organization</TableHead>}
                                    <TableHead>Status</TableHead>
                                    <TableHead className="text-right">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {!chartOfAccounts.data || chartOfAccounts.data.length === 0 ? (
                                    <TableRow>
                                        <TableCell colSpan={isPlatformAdmin ? 7 : 6} className="text-muted-foreground py-8 text-center">
                                            No chart of accounts found. Create your first account to get started.
                                        </TableCell>
                                    </TableRow>
                                ) : (
                                    chartOfAccounts.data.map((account) => (
                                        <TableRow key={account.id}>
                                            <TableCell className="font-mono">{account.code || '-'}</TableCell>
                                            <TableCell className="font-medium">{account.name}</TableCell>
                                            <TableCell>{getAccountTypeLabel(account.account_type)}</TableCell>
                                            <TableCell>{account.parent?.name || '-'}</TableCell>
                                            {isPlatformAdmin && <TableCell>{account.organization?.name}</TableCell>}
                                            <TableCell>
                                                {account.is_active ? (
                                                    <Badge
                                                        variant="outline"
                                                        className="dark:text-secondary-foreground/100 border-green-200 bg-green-50 text-green-700 dark:border-green-600 dark:bg-green-900/20"
                                                    >
                                                        Active
                                                    </Badge>
                                                ) : (
                                                    <Badge
                                                        variant="outline"
                                                        className="dark:text-secondary-foreground/100 border-gray-200 bg-gray-50 text-gray-700 dark:border-gray-600 dark:bg-gray-900/20"
                                                    >
                                                        Inactive
                                                    </Badge>
                                                )}
                                            </TableCell>
                                            <TableCell className="text-right">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" size="icon">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                            <span className="sr-only">Actions</span>
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem asChild>
                                                            <Link href={`/chart-of-accounts/${account.id}`}>
                                                                <Eye className="mr-2 h-4 w-4" />
                                                                View
                                                            </Link>
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem asChild>
                                                            <Link href={`/chart-of-accounts/${account.id}/edit`}>
                                                                <Edit className="mr-2 h-4 w-4" />
                                                                Edit
                                                            </Link>
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem onClick={() => handleDelete(account.id)}>
                                                            <Trash className="mr-2 h-4 w-4" />
                                                            Delete
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                )}
                            </TableBody>
                        </Table>

                        {/* Pagination */}
                        {chartOfAccounts.meta && chartOfAccounts.meta.last_page > 1 && (
                            <div className="mt-4 flex items-center justify-center">
                                <Pagination>
                                    {chartOfAccounts.links &&
                                        chartOfAccounts.links.map((link, i) => (
                                            <Button
                                                key={i}
                                                variant={link.active ? 'default' : 'outline'}
                                                disabled={!link.url}
                                                onClick={() => {
                                                    if (link.url) {
                                                        // Preserve all current filters when navigating
                                                        router.get(link.url, {}, {
                                                            preserveState: true,
                                                            preserveScroll: true,
                                                            only: ['chartOfAccounts'],
                                                        });
                                                    }
                                                }}
                                                className="mx-1"
                                            >
                                                <span dangerouslySetInnerHTML={{ __html: link.label }} />
                                            </Button>
                                        ))}
                                </Pagination>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
