import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm, router } from '@inertiajs/react';
import React, { useEffect, useState } from 'react';

interface Organization {
    id: number;
    name: string;
}

interface Branch {
    id: number;
    name: string;
    organization_id: number;
}

interface ParentAccount {
    id: number;
    name: string;
    code?: string;
}

interface ChartOfAccount {
    id: number;
    organization_id: number;
    branch_id: number | null;
    code: string | null;
    name: string;
    description: string | null;
    spending_limit: number | null;
    limit_period: string | null;
    is_active: boolean;
    parent_id: number | null;
    account_type: string;
}

interface Props {
    chartOfAccount: ChartOfAccount;
    organizations?: Organization[];
    branches: Branch[];
    parentAccounts: ParentAccount[];
    isPlatformAdmin: boolean;
}

export default function EditChartOfAccount({ chartOfAccount, organizations, branches, parentAccounts, isPlatformAdmin }: Props) {
    const [filteredBranches, setFilteredBranches] = useState<Branch[]>(branches);

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Chart of Accounts', href: '/chart-of-accounts' },
        { title: chartOfAccount.name, href: `/chart-of-accounts/${chartOfAccount.id}` },
        { title: 'Edit', href: `/chart-of-accounts/${chartOfAccount.id}/edit` },
    ];

    const { data, setData, processing, errors } = useForm({
        organization_id: chartOfAccount.organization_id.toString(),
        branch_id: chartOfAccount.branch_id ? chartOfAccount.branch_id.toString() : 'none',
        code: chartOfAccount.code || '',
        name: chartOfAccount.name,
        description: chartOfAccount.description || '',
        spending_limit: chartOfAccount.spending_limit ? chartOfAccount.spending_limit.toString() : '',
        limit_period: chartOfAccount.limit_period || 'none',
        is_active: chartOfAccount.is_active,
        parent_id: chartOfAccount.parent_id ? chartOfAccount.parent_id.toString() : 'none',
        // account_type removed - will be determined from parent account
    });

    useEffect(() => {
        // Filter branches by selected organization on component mount
        if (data.organization_id) {
            const orgId = parseInt(data.organization_id);
            const filtered = branches.filter((branch) => branch.organization_id === orgId);
            setFilteredBranches(filtered);
        }
    }, [data.organization_id, branches]);

    const handleOrganizationChange = (value: string) => {
        setData('organization_id', value);
        setData('branch_id', ''); // Reset branch when organization changes

        // Filter branches by selected organization
        if (value) {
            const orgId = parseInt(value);
            const filtered = branches.filter((branch) => branch.organization_id === orgId);
            setFilteredBranches(filtered);
        } else {
            setFilteredBranches([]);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Prepare the data for submission with proper transformations
        const submitData = {
            organization_id: data.organization_id,
            branch_id: data.branch_id === 'none' ? '' : data.branch_id,
            code: data.code,
            name: data.name,
            description: data.description,
            spending_limit: data.spending_limit || '', // Allow empty spending limit
            limit_period: data.limit_period === 'none' ? '' : data.limit_period,
            is_active: data.is_active,
            parent_id: data.parent_id === 'none' ? '' : data.parent_id,
        };

        // Ensure limit_period is one of the allowed values or empty
        const allowedPeriods = ['daily', 'weekly', 'monthly', 'quarterly', 'annually'];
        if (submitData.limit_period && !allowedPeriods.includes(submitData.limit_period)) {
            submitData.limit_period = '';
        }

        // Use router.put to send the transformed data
        router.put(`/chart-of-accounts/${chartOfAccount.id}`, submitData);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit Chart of Account: ${chartOfAccount.name}`} />
            <div className="dark:bg-background/100 flex h-full flex-1 flex-col gap-4 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-foreground/100 text-background/100 text-2xl font-bold">Edit Chart of Account</h1>
                        <p className="text-muted-foreground">Update the details for {chartOfAccount.name}</p>
                    </div>
                </div>

                <form onSubmit={handleSubmit}>
                    <Card>
                        <CardHeader>
                            <CardTitle>Account Information</CardTitle>
                            <CardDescription>Edit the details for this chart of account</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* Organization Selection (for platform admin only) */}
                            {isPlatformAdmin && (
                                <div className="space-y-2">
                                    <Label htmlFor="organization_id" className={errors.organization_id ? 'text-destructive' : ''}>
                                        Organization <span className="text-destructive">*</span>
                                    </Label>
                                    <Select value={data.organization_id.toString()} onValueChange={handleOrganizationChange}>
                                        <SelectTrigger id="organization_id" className={errors.organization_id ? 'border-destructive' : ''}>
                                            <SelectValue placeholder="Select organization" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {organizations?.map((org) => (
                                                <SelectItem key={org.id} value={org.id.toString()}>
                                                    {org.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.organization_id && <p className="text-destructive text-sm">{errors.organization_id}</p>}
                                </div>
                            )}

                            {/* Branch Selection */}
                            <div className="space-y-2">
                                <Label htmlFor="branch_id" className={errors.branch_id ? 'text-destructive' : ''}>
                                    Branch <span className="text-muted-foreground">(Optional)</span>
                                </Label>
                                <Select value={data.branch_id.toString()} onValueChange={(value) => setData('branch_id', value)}>
                                    <SelectTrigger id="branch_id" className={errors.branch_id ? 'border-destructive' : ''}>
                                        <SelectValue placeholder="Select branch " />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="none">None</SelectItem>
                                        {filteredBranches.map((branch) => (
                                            <SelectItem key={branch.id} value={branch.id.toString()}>
                                                {branch.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <p className="text-muted-foreground text-xs">
                                    Chart of accounts are primarily at the organization level. Branch is optional.
                                </p>
                                {errors.branch_id && <p className="text-destructive text-sm">{errors.branch_id}</p>}
                            </div>

                            {/* Parent Account */}
                            <div className="space-y-2">
                                <Label htmlFor="parent_id" className={errors.parent_id ? 'text-destructive' : ''}>
                                    Parent Account
                                </Label>
                                <Select value={data.parent_id.toString()} onValueChange={(value) => setData('parent_id', value)}>
                                    <SelectTrigger id="parent_id" className={errors.parent_id ? 'border-destructive' : ''}>
                                        <SelectValue placeholder="Select parent account " />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="none">None (Top-level Account)</SelectItem>
                                        {parentAccounts.map((account) => (
                                            <SelectItem key={account.id} value={account.id.toString()}>
                                                {account.code ? `${account.code} - ${account.name}` : account.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.parent_id && <p className="text-destructive text-sm">{errors.parent_id}</p>}
                            </div>

                            {/* Account Code (read-only) */}
                            <div className="space-y-2">
                                <Label htmlFor="code" className={errors.code ? 'text-destructive' : ''}>
                                    Account Code
                                </Label>
                                <Input id="code" value={data.code} readOnly disabled className="bg-muted" />
                                <p className="text-muted-foreground text-xs">Account code is auto-generated and cannot be changed</p>
                            </div>

                            {/* Account Name */}
                            <div className="space-y-2">
                                <Label htmlFor="name" className={errors.name ? 'text-destructive' : ''}>
                                    Account Name <span className="text-destructive">*</span>
                                </Label>
                                <Input
                                    id="name"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    placeholder="e.g., Office Supplies"
                                    className={errors.name ? 'border-destructive' : ''}
                                />
                                {errors.name && <p className="text-destructive text-sm">{errors.name}</p>}
                            </div>

                            {/* Description */}
                            <div className="space-y-2">
                                <Label htmlFor="description" className={errors.description ? 'text-destructive' : ''}>
                                    Description
                                </Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Enter a description for this account"
                                    className={errors.description ? 'border-destructive' : ''}
                                />
                                {errors.description && <p className="text-destructive text-sm">{errors.description}</p>}
                            </div>

                            {/* Spending Limit */}
                            <div className="space-y-2">
                                <Label htmlFor="spending_limit" className={errors.spending_limit ? 'text-destructive' : ''}>
                                    Spending Limit <span className="text-muted-foreground">(Optional)</span>
                                </Label>
                                <Input
                                    id="spending_limit"
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    value={data.spending_limit}
                                    onChange={(e) => setData('spending_limit', e.target.value)}
                                    placeholder="e.g., 5000.00"
                                    className={errors.spending_limit ? 'border-destructive' : ''}
                                />
                                {errors.spending_limit && <p className="text-destructive text-sm">{errors.spending_limit}</p>}
                            </div>

                            {/* Limit Period */}
                            <div className="space-y-2">
                                <Label htmlFor="limit_period" className={errors.limit_period ? 'text-destructive' : ''}>
                                    Limit Period <span className="text-muted-foreground">(Optional)</span>
                                </Label>
                                <Select value={data.limit_period} onValueChange={(value) => setData('limit_period', value)}>
                                    <SelectTrigger id="limit_period" className={errors.limit_period ? 'border-destructive' : ''}>
                                        <SelectValue placeholder="Select limit period " />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="none">None</SelectItem>
                                        <SelectItem value="daily">Daily</SelectItem>
                                        <SelectItem value="weekly">Weekly</SelectItem>
                                        <SelectItem value="monthly">Monthly</SelectItem>
                                        <SelectItem value="quarterly">Quarterly</SelectItem>
                                        <SelectItem value="annually">Annually</SelectItem>
                                    </SelectContent>
                                </Select>
                                {errors.limit_period && <p className="text-destructive text-sm">{errors.limit_period}</p>}
                            </div>

                            {/* Active Status */}
                            <div className="flex items-center justify-between space-y-0">
                                <Label htmlFor="is_active" className={errors.is_active ? 'text-destructive' : ''}>
                                    Active
                                </Label>
                                <Switch id="is_active" checked={data.is_active} onCheckedChange={(checked) => setData('is_active', checked)} />
                                {errors.is_active && <p className="text-destructive text-sm">{errors.is_active}</p>}
                            </div>
                        </CardContent>
                        <CardFooter className="flex justify-between">
                            <Button variant="destructive" type="button" onClick={() => window.history.back()}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={processing}>
                                {processing ? 'Saving...' : 'Save Changes'}
                            </Button>
                        </CardFooter>
                    </Card>
                </form>
            </div>
        </AppLayout>
    );
}
