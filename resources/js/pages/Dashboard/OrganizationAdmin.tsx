import DashboardSwitcher from '@/components/DashboardSwitcher';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useInitials } from '@/hooks/use-initials';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type User } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { BarChart3, Briefcase, GitBranch, ListOrdered, UserCog, Users } from 'lucide-react';
import { useState } from 'react';

interface OrganizationAdminProps {
    stats: {
        branches: number;
        departments: number;
        users: number;
        roles: number;
        workflows: number;
        chartOfAccounts: number;
    };
    organization: {
        id: number;
        name: string;
        contact_email: string;
        contact_phone: string;
        status: string;
    };
    recentDepartments: {
        id: number;
        name: string;
        branch_id: number;
        hod_user_id: number | null;
        created_at: string;
    }[];
    recentUsers: User[];
    availableContexts?: Array<{
        type: 'role' | 'department';
        key: string;
        label: string;
        organization: { id: number; name: string } | null;
        department: { id: number; name: string } | null;
        priority: number;
        role_id?: number;
        department_id?: number;
    }>;
    cashFloats: {
        id: number;
        name: string;
    }[];
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

export default function DashboardOrganizationAdmin({
    stats,
    organization,
    recentDepartments,
    recentUsers,
    availableContexts,
    
}: OrganizationAdminProps) {
    const getInitials = useInitials();
    const [showAllManagement, setShowAllManagement] = useState(false);
    
    
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Organization Admin Dashboard" />
            <div className="dark:bg-background/100 flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-foreground text-2xl font-bold">{organization.name} Dashboard</h1>
                        <p className="text-muted-foreground">Organization Admin Panel</p>
                    </div>
                    <div className="flex gap-3">
                        <Button className="bg-primary/100 hover:bg-primary/70 font-medium text-white shadow-md transition-all hover:shadow-lg" asChild>
                            <Link href="/departments/create">
                                <span className="sm:hidden">+ Department</span>
                                <span className="hidden sm:inline">New Department</span>
                            </Link>
                        </Button>
                        <Button className="bg-primary/100 hover:bg-primary/90 font-medium text-white shadow-md transition-all hover:shadow-lg" asChild>
                            <Link href="/users/create">
                                <span className="sm:hidden">+ User</span>
                                <span className="hidden sm:inline">New User</span>
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Dashboard Switcher */}
                {availableContexts && availableContexts.length > 1 && (
                    <div className="w-full max-w-sm">
                        <DashboardSwitcher availableContexts={availableContexts} />
                    </div>
                )}

                {/* Stats Cards - Mobile Optimized */}
                <div className="grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-6 md:gap-4">
                    <Card className="p-4 shadow-md transition-shadow hover:shadow-lg">
                        <div className="flex items-center justify-between">
                            <div className="flex flex-col">
                                <div className="text-foreground text-xl font-bold md:text-2xl">{stats.branches}</div>
                                <div className="text-muted-foreground text-xs font-medium md:text-sm">Branches</div>
                            </div>
                            <div className="bg-primary/10 rounded-full p-2">
                                <GitBranch className="text-primary h-5 w-5 md:h-6 md:w-6" />
                            </div>
                        </div>
                    </Card>
                    <Card className="p-4 shadow-md transition-shadow hover:shadow-lg">
                        <div className="flex items-center justify-between">
                            <div className="flex flex-col">
                                <div className="text-foreground text-xl font-bold md:text-2xl">{stats.departments}</div>
                                <div className="text-muted-foreground text-xs font-medium md:text-sm">Departments</div>
                            </div>
                            <div className="bg-primary/10 rounded-full p-2">
                                <Briefcase className="text-primary h-5 w-5 md:h-6 md:w-6" />
                            </div>
                        </div>
                    </Card>
                    <Card className="p-4 shadow-md transition-shadow hover:shadow-lg">
                        <div className="flex items-center justify-between">
                            <div className="flex flex-col">
                                <div className="text-foreground text-xl font-bold md:text-2xl">{stats.users}</div>
                                <div className="text-muted-foreground text-xs font-medium md:text-sm">Users</div>
                            </div>
                            <div className="bg-primary/10 rounded-full p-2">
                                <Users className="text-primary h-5 w-5 md:h-6 md:w-6" />
                            </div>
                        </div>
                    </Card>
                    <Card className="p-4 shadow-md transition-shadow hover:shadow-lg">
                        <div className="flex items-center justify-between">
                            <div className="flex flex-col">
                                <div className="text-foreground text-xl font-bold md:text-2xl">{stats.roles}</div>
                                <div className="text-muted-foreground text-xs font-medium md:text-sm">Roles</div>
                            </div>
                            <div className="bg-primary/10 rounded-full p-2">
                                <UserCog className="text-primary h-5 w-5 md:h-6 md:w-6" />
                            </div>
                        </div>
                    </Card>
                    <Card className="p-4 shadow-md transition-shadow hover:shadow-lg">
                        <div className="flex items-center justify-between">
                            <div className="flex flex-col">
                                <div className="text-foreground text-xl font-bold md:text-2xl">{stats.workflows}</div>
                                <div className="text-muted-foreground text-xs font-medium md:text-sm">Workflows</div>
                            </div>
                            <div className="bg-primary/10 rounded-full p-2">
                                <ListOrdered className="text-primary h-5 w-5 md:h-6 md:w-6" />
                            </div>
                        </div>
                    </Card>
                    <Card className="p-4 shadow-md transition-shadow hover:shadow-lg">
                        <div className="flex items-center justify-between">
                            <div className="flex flex-col">
                                <div className="text-foreground text-xl font-bold md:text-2xl">{stats.chartOfAccounts}</div>
                                <div className="text-muted-foreground text-xs font-medium md:text-sm">Accounts</div>
                            </div>
                            <div className="bg-primary/10 rounded-full p-2">
                                <BarChart3 className="text-primary h-5 w-5 md:h-6 md:w-6" />
                            </div>
                        </div>
                    </Card>
                </div>

                {/* Recent Departments and Users */}
                <div className="grid gap-4 md:grid-cols-2">
                    <Card className="shadow-md transition-shadow hover:shadow-lg">
                        <CardHeader>
                            <CardTitle className="text-foreground flex items-center gap-2">
                                <Briefcase className="text-primary h-5 w-5" />
                                Recent Departments
                            </CardTitle>
                            <CardDescription>Recently added departments</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentDepartments.map((dept) => (
                                    <div
                                        key={dept.id}
                                        className="bg-muted/30 hover:bg-muted/50 flex items-center gap-4 rounded-lg p-3 transition-colors"
                                    >
                                        <div className="bg-primary/15 flex h-10 w-10 items-center justify-center rounded-full">
                                            <Briefcase className="text-primary h-5 w-5" />
                                        </div>
                                        <div className="flex-1 space-y-1">
                                            <p className="text-foreground text-sm leading-none font-semibold">{dept.name}</p>
                                            <p className="text-muted-foreground text-xs">{dept.hod_user_id ? 'Has HOD' : 'No HOD assigned'}</p>
                                        </div>
                                        <Button variant="default" size="sm" className="bg-primary/100hover:bg-primary/90 font-medium text-white" asChild>
                                            <Link href={`/departments/${dept.id}/edit`}>Manage</Link>
                                        </Button>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="shadow-md transition-shadow hover:shadow-lg">
                        <CardHeader>
                            <CardTitle className="text-foreground flex items-center gap-2">
                                <Users className="text-primary h-5 w-5" />
                                Recent Users
                            </CardTitle>
                            <CardDescription>Recently added users to your organization</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentUsers.map((user) => (
                                    <div
                                        key={user.id}
                                        className="bg-muted/30 hover:bg-muted/50 flex items-center gap-4 rounded-lg p-3 transition-colors"
                                    >
                                        <Avatar className="border-primary/20 h-10 w-10 border-2">
                                            <AvatarImage src={user.avatar || undefined} alt={`${user.first_name} ${user.last_name}`} />
                                            <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                                                {getInitials(`${user.first_name} ${user.last_name}`)}
                                            </AvatarFallback>
                                        </Avatar>
                                        <div className="flex-1 space-y-1">
                                            <p className="text-foreground text-sm leading-none font-semibold">
                                                {user.first_name} {user.last_name}
                                            </p>
                                            <p className="text-muted-foreground text-xs">{user.email}</p>
                                        </div>
                                        <Button variant="default" size="sm" className="bg-primary/100hover:bg-primary/90 font-medium text-white" asChild>
                                            <Link href={`/users/${user.id}/edit`}>Edit</Link>
                                        </Button>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Organization Management */}
                <Card className="shadow-md transition-shadow hover:shadow-lg">
                    <CardHeader>
                        <CardTitle className="text-foreground flex items-center gap-2">
                            <UserCog className="text-primary h-5 w-5" />
                            Organization Management
                        </CardTitle>
                        <CardDescription>Manage your organization settings and configurations</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {/* Mobile view - show only first two items with show more button */}
                        <div className="md:hidden">
                            <div className="space-y-3">
                                <Button
                                    variant="outline"
                                    className="bg-primary/5 border-primary/30 hover:bg-primary/10 hover:border-primary/50 h-16 w-full flex-row justify-start gap-4"
                                    asChild
                                >
                                    <Link href="/branches">
                                        <div className="bg-primary/15 rounded-full p-2">
                                            <GitBranch className="text-primary h-5 w-5" />
                                        </div>
                                        <span className="text-foreground font-medium">Branches</span>
                                    </Link>
                                </Button>
                                <Button
                                    variant="outline"
                                    className="bg-primary/5 border-primary/30 hover:bg-primary/10 hover:border-primary/50 h-16 w-full flex-row justify-start gap-4"
                                    asChild
                                >
                                    <Link href="/departments">
                                        <div className="bg-primary/15 rounded-full p-2">
                                            <Briefcase className="text-primary h-5 w-5" />
                                        </div>
                                        <span className="text-foreground font-medium">Departments</span>
                                    </Link>
                                </Button>
                                {showAllManagement && (
                                    <>
                                        <Button
                                            variant="outline"
                                            className="bg-primary/5 border-primary/30 hover:bg-primary/10 hover:border-primary/50 h-16 w-full flex-row justify-start gap-4"
                                            asChild
                                        >
                                            <Link href="/users">
                                                <div className="bg-primary/15 rounded-full p-2">
                                                    <Users className="text-primary h-5 w-5" />
                                                </div>
                                                <span className="text-foreground font-medium">Users</span>
                                            </Link>
                                        </Button>
                                        <Button
                                            variant="outline"
                                            className="bg-primary/5 border-primary/30 hover:bg-primary/10 hover:border-primary/50 h-16 w-full flex-row justify-start gap-4"
                                            asChild
                                        >
                                            <Link href="/roles">
                                                <div className="bg-primary/15 rounded-full p-2">
                                                    <UserCog className="text-primary h-5 w-5" />
                                                </div>
                                                <span className="text-foreground font-medium">Roles</span>
                                            </Link>
                                        </Button>
                                        <Button
                                            variant="outline"
                                            className="bg-primary/5 border-primary/30 hover:bg-primary/10 hover:border-primary/50 h-16 w-full flex-row justify-start gap-4"
                                            asChild
                                        >
                                            <Link href={`/approval-workflows?organization_id=${organization.id}`}>
                                                <div className="bg-primary/15 rounded-full p-2">
                                                    <ListOrdered className="text-primary h-5 w-5" />
                                                </div>
                                                <span className="text-foreground font-medium">Workflows</span>
                                            </Link>
                                        </Button>
                                        <Button
                                            variant="outline"
                                            className="bg-primary/5 border-primary/30 hover:bg-primary/10 hover:border-primary/50 h-16 w-full flex-row justify-start gap-4"
                                            asChild
                                        >
                                            <Link href="/chart-of-accounts">
                                                <div className="bg-primary/15 rounded-full p-2">
                                                    <BarChart3 className="text-primary h-5 w-5" />
                                                </div>
                                                <span className="text-foreground font-medium">Chart of Accounts</span>
                                            </Link>
                                        </Button>
                                    </>
                                )}
                                <Button
                                    variant="ghost"
                                    className="text-primary hover:text-primary/80 hover:bg-primary/5 w-full text-sm font-medium"
                                    onClick={() => setShowAllManagement(!showAllManagement)}
                                >
                                    {showAllManagement ? 'Show Less' : 'Show More'}
                                </Button>
                            </div>
                        </div>

                        {/* Desktop view - show all items in grid */}
                        <div className="hidden md:grid md:grid-cols-3 md:gap-4 lg:grid-cols-6">
                            <Button
                                variant="outline"
                                className="bg-primary/5 border-primary/30 hover:bg-primary/10 hover:border-primary/50 h-28 flex-col gap-3 transition-all hover:shadow-md"
                                asChild
                            >
                                <Link href="/branches">
                                    <div className="bg-primary/15 rounded-full p-3">
                                        <GitBranch className="text-primary h-6 w-6" />
                                    </div>
                                    <span className="text-foreground text-center font-medium">Manage Branches</span>
                                </Link>
                            </Button>
                            <Button
                                variant="outline"
                                className="bg-primary/5 border-primary/30 hover:bg-primary/10 hover:border-primary/50 h-28 flex-col gap-3 transition-all hover:shadow-md"
                                asChild
                            >
                                <Link href="/departments">
                                    <div className="bg-primary/15 rounded-full p-3">
                                        <Briefcase className="text-primary h-6 w-6" />
                                    </div>
                                    <span className="text-foreground text-center font-medium">Manage Departments</span>
                                </Link>
                            </Button>
                            <Button
                                variant="outline"
                                className="bg-primary/5 border-primary/30 hover:bg-primary/10 hover:border-primary/50 h-28 flex-col gap-3 transition-all hover:shadow-md"
                                asChild
                            >
                                <Link href="/users">
                                    <div className="bg-primary/15 rounded-full p-3">
                                        <Users className="text-primary h-6 w-6" />
                                    </div>
                                    <span className="text-foreground text-center font-medium">Manage Users</span>
                                </Link>
                            </Button>
                            <Button
                                variant="outline"
                                className="bg-primary/5 border-primary/30 hover:bg-primary/10 hover:border-primary/50 h-28 flex-col gap-3 transition-all hover:shadow-md"
                                asChild
                            >
                                <Link href="/roles">
                                    <div className="bg-primary/15 rounded-full p-3">
                                        <UserCog className="text-primary h-6 w-6" />
                                    </div>
                                    <span className="text-foreground text-center font-medium">Manage Roles</span>
                                </Link>
                            </Button>
                            <Button
                                variant="outline"
                                className="bg-primary/5 border-primary/30 hover:bg-primary/10 hover:border-primary/50 h-28 flex-col gap-3 transition-all hover:shadow-md"
                                asChild
                            >
                                <Link href={`/approval-workflows?organization_id=${organization.id}`}>
                                    <div className="bg-primary/15 rounded-full p-3">
                                        <ListOrdered className="text-primary h-6 w-6" />
                                    </div>
                                    <span className="text-foreground text-center font-medium">Manage Workflows</span>
                                </Link>
                            </Button>
                            <Button
                                variant="outline"
                                className="bg-primary/5 border-primary/30 hover:bg-primary/10 hover:border-primary/50 h-28 flex-col gap-3 transition-all hover:shadow-md"
                                asChild
                            >
                                <Link href="/chart-of-accounts">
                                    <div className="bg-primary/15 rounded-full p-3">
                                        <BarChart3 className="text-primary h-6 w-6" />
                                    </div>
                                    <span className="text-foreground text-center font-medium">Chart of Accounts</span>
                                </Link>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
