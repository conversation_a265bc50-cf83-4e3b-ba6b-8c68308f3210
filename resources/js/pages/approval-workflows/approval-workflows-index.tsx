import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { Building2, PlusCircle, ChevronDown, FileText, Settings, Trash2 } from 'lucide-react';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';

interface Workflow {
    id: number;
    name: string;
    is_default: boolean;
    organization: { id: number; name: string };
    branch?: { id: number; name: string } | null;
    department?: { id: number; name: string } | null;
}

interface Props {
    workflows: Workflow[];
    isPlatformAdmin: boolean;
    organizationId?: number;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Approval Workflows',
        href: '/approval-workflows',
    },
];

export default function ApprovalWorkflowsIndex({ workflows, organizationId }: Props) {
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [workflowToDelete, setWorkflowToDelete] = useState<Workflow | null>(null);

    const openDeleteDialog = (workflow: Workflow) => {
        setWorkflowToDelete(workflow);
        setIsDeleteDialogOpen(true);
    };

    const handleDelete = () => {
        if (workflowToDelete) {
            router.delete(route('approval-workflows.destroy', { id: workflowToDelete.id }), {
                onSuccess: () => {
                    setIsDeleteDialogOpen(false);
                    setWorkflowToDelete(null);
                    // Optionally show a toast notification
                    window.showToast?.({
                        title: 'Success',
                        message: `Workflow '${workflowToDelete.name}' deleted successfully.`,
                        type: 'success',
                    });
                },
                onError: (errors) => {
                    console.error('Error deleting workflow:', errors);
                    setIsDeleteDialogOpen(false);
                    setWorkflowToDelete(null);
                    window.showToast?.({
                        title: 'Error',
                        message: 'Failed to delete workflow.',
                        type: 'error',
                    });
                },
            });
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Approval Workflows" />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-background/100 text-foreground/100 text-2xl font-bold md:text-3xl">Approval Workflows</h1>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button className="group relative overflow-hidden transition-all duration-300 ease-in-out">
                                <PlusCircle className="h-4 w-4 flex-shrink-0" />
                                <span className="ml-2 whitespace-nowrap">
                                    Create Workflow
                                </span>
                                <ChevronDown className="ml-2 h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-56">
                            <DropdownMenuItem asChild>
                                <Link href={route('workflow-templates.index', organizationId ? { organization_id: organizationId } : {})}>
                                    <FileText className="mr-2 h-4 w-4" />
                                    <span>Use Template</span>
                                </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                                <Link href={route('approval-workflows.create', organizationId ? { organization_id: organizationId } : {})}>
                                    <Settings className="mr-2 h-4 w-4" />
                                    <span>Create Custom</span>
                                </Link>
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>All Workflows</CardTitle>
                        <CardDescription>Manage approval workflows for your organization</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {workflows.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-8">
                                    <Building2 className="text-muted-foreground h-12 w-12" />
                                    <h3 className="mt-4 text-lg font-medium">No workflows found</h3>
                                    <p className="text-muted-foreground text-sm">Get started by creating a new workflow.</p>
                                </div>
                            ) : (
                                <div className="overflow-x-auto">
                                    <table className="min-w-full">
                                        <thead>
                                            <tr className="border-b">
                                                <th className="text-muted-foreground h-12 px-4 text-left align-middle font-medium">Name</th>
                                                <th className="text-muted-foreground h-12 px-4 text-left align-middle font-medium">Organization</th>
                                                <th className="text-muted-foreground h-12 px-4 text-left align-middle font-medium">Branch</th>
                                                <th className="text-muted-foreground h-12 px-4 text-left align-middle font-medium">Department</th>
                                                <th className="text-muted-foreground h-12 px-4 text-left align-middle font-medium">Status</th>
                                                <th className="text-muted-foreground h-12 px-4 text-right align-middle font-medium">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {workflows.map((workflow) => (
                                                <tr
                                                    key={workflow.id}
                                                    className="hover:bg-muted/50 dark:hover:bg-accent/70 data-[state=selected]:bg-muted border-b transition-colors"
                                                >
                                                    <td className="p-4 align-middle">
                                                        <div className="text-sm font-medium">{workflow.name}</div>
                                                    </td>
                                                    <td className="p-4 align-middle">
                                                        <div className="text-sm">{workflow.organization.name}</div>
                                                    </td>
                                                    <td className="p-4 align-middle">
                                                        <div className="text-sm">{workflow.branch?.name || 'All Branches'}</div>
                                                    </td>
                                                    <td className="p-4 align-middle">
                                                        <div className="text-sm">{workflow.department?.name || 'All Departments'}</div>
                                                    </td>
                                                    <td className="p-4 align-middle">
                                                        {workflow.is_default && (
                                                            <Badge
                                                                variant="default"
                                                                className="bg-green-100 text-green-800 dark:border-green-600 dark:bg-green-900/20"
                                                            >
                                                                Default
                                                            </Badge>
                                                        )}
                                                    </td>
                                                    <td className="p-4 text-right align-middle">
                                                        <div className="flex justify-end gap-2">
                                                            <Button variant="default" size="sm" asChild>
                                                                <Link
                                                                    href={route('approval-workflows.show', {
                                                                        id: workflow.id,
                                                                        organization_id: organizationId,
                                                                    })}
                                                                >
                                                                    View
                                                                </Link>
                                                            </Button>
                                                            <Button variant="default" size="sm" asChild>
                                                                <Link
                                                                    href={route('approval-workflows.edit', {
                                                                        id: workflow.id,
                                                                        organization_id: organizationId,
                                                                    })}
                                                                >
                                                                    Edit
                                                                </Link>
                                                            </Button>
                                                            <Button
                                                                variant="destructive"
                                                                size="sm"
                                                                onClick={() => openDeleteDialog(workflow)}
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                                Delete
                                                            </Button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the
                            <span className="font-semibold"> {workflowToDelete?.name} </span>
                            workflow and remove its data from our servers.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
