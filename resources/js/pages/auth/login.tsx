import { motion } from 'framer-motion';
import type React from 'react';

import { useForm } from '@inertiajs/react';
import { Eye, EyeOff, LoaderCircle } from 'lucide-react';
import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import InputError from '@/components/ui/input-error';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';
import { Link } from '@inertiajs/react';
import LoginBranding from '../auth/branding/login-branding';

type LoginForm = {
    email: string;
    password: string;
    remember: boolean;
};

interface LoginProps {
    status?: string;
    canResetPassword: boolean;
}

export default function Login({ status, canResetPassword }: LoginProps) {
    const { data, setData, errors, post, reset } = useForm<Required<LoginForm>>({
        email: '',
        password: '',
        remember: false,
    });

    const [Email, setEmail] = useState('');
    const [Password, setPassword] = useState('');
    const [Remember, setRemember] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');

    useEffect(() => {
        setData({
            email: Email,
            password: Password,
            remember: Remember,
        });
    }, [Email, Password, Remember, setData]);

    const submit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');

        if (!data.email || !data.password) {
            setError('Please fill in all fields');
            return;
        }

        setIsLoading(true);

        await new Promise((resolve) => setTimeout(resolve, 1500));

        setIsLoading(false);

        post(route('login'), {
            onFinish: () => reset('password'),
        });
    };

    const inputVariants = {
        focus: { scale: 1.02, transition: { duration: 0.2 } },
        blur: { scale: 1, transition: { duration: 0.2 } },
    };

    return (
        <div className="min-h-screen max-w-full overflow-x-hidden overflow-y-auto">
            <AuthLayout
                title="Sign in to Sippar"
                description="Enter your credentials to access your account (Users & Suppliers)"
                reverseLayout={true}
                brandingComponent={<LoginBranding />}
                showBranding={true}
                useAcrylicBackground={true}
            >
                {error && (
                    <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-destructive/10 text-destructive mb-4 rounded-md p-3 text-sm"
                    >
                        {error}
                    </motion.div>
                )}

                <form className="space-y-6 w-full max-w-md mx-auto px-2 sm:px-4 md:px-0" onSubmit={submit}>
                    {/* Email Field */}
                    <div className="space-y-2">
                        <Label htmlFor="email">Email address</Label>
                        <motion.div whileFocus="focus" variants={inputVariants}>
                            <Input
                                id="email"
                                type="email"
                                required
                                autoFocus
                                autoComplete="email"
                                value={data.email}
                                className="text-secondary-foreground/80 focus:border-primary/50 focus:ring-primary/20 w-full bg-transparent/50 pr-10 font-semibold outline-none focus:ring"
                                disabled={isLoading}
                                onChange={(e) => {
                                    setEmail(e.target.value);
                                    setData('email', e.target.value);
                                }}
                                placeholder="<EMAIL>"
                            />
                            <InputError message={errors.email} />
                        </motion.div>
                    </div>

                    {/* Password Field */}
                    <div className="space-y-2">
                        <div className="flex items-center justify-between">
                            <Label htmlFor="password">Password</Label>
                            {canResetPassword && (
                                <Link
                                    href={route('password.request')}
                                    className="text-primary/100 hover:text-primary/70 text-sm underline"
                                >
                                    Forgot password?
                                </Link>
                            )}
                        </div>
                        <div className="relative">
                            <motion.div whileFocus="focus" variants={inputVariants}>
                                <Input
                                    id="password"
                                    type={showPassword ? 'text' : 'password'}
                                    required
                                    autoComplete="current-password"
                                    value={data.password}
                                    onChange={(e) => {
                                        setPassword(e.target.value);
                                        setData('password', e.target.value);
                                    }}
                                    placeholder="••••••••"
                                    className="text-secondary-foreground/80 focus:border-primary/50 focus:ring-primary/20 w-full bg-transparent/50 pr-10 font-semibold outline-none focus:ring"
                                />
                                <InputError message={errors.password} />
                            </motion.div>
                            <button
                                type="button"
                                onClick={() => setShowPassword(!showPassword)}
                                className="text-secondary-foreground hover:text-foreground absolute top-1/2 right-3 -translate-y-1/2"
                            >
                                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </button>
                        </div>
                    </div>

                    {/* Remember Me */}
                    <div className="flex items-center space-x-2">
                        <Checkbox
                            id="remember"
                            name="remember"
                            checked={data.remember}
                            onClick={() => {
                                setRemember(!data.remember);
                                setData('remember', !data.remember);
                            }}
                            disabled={isLoading}
                            className='border-accent/80'
                        />
                        <Label htmlFor="remember">Remember me</Label>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-between gap-4">
                        <Button type="submit" className="w-full" disabled={isLoading}>
                            {isLoading ? (
                                <>
                                    <LoaderCircle className="mr-2 h-4 w-4 animate-spin" /> Signing in...
                                </>
                            ) : (
                                'Sign in'
                            )}
                        </Button>
                    </div>
                </form>

                {/* Register Link */}
                <div className="text-foreground/100 text-center text-sm mt-4 px-2">
                    Don’t have an account?{' '}
                    <Link
                        href={route('register')}
                        className="text-primary/100 hover:text-primary/70 font-medium hover:underline"
                    >
                        Create account
                    </Link>
                    <br />
                    <Link
                        href={route('suppliers.create')}
                        className="text-primary/100 hover:text-primary/70 font-medium hover:underline"
                    >
                        Register As Supplier
                    </Link>
                </div>

                {/* Decorative circles */}
                <div className="bg-foreground absolute top-0 right-0 h-40 w-40 sm:h-64 sm:w-64 translate-x-1/2 -translate-y-1/2 rounded-full opacity-10"></div>
                <div className="bg-foreground absolute bottom-0 left-0 h-52 w-52 sm:h-80 sm:w-80 -translate-x-1/2 translate-y-1/2 rounded-full opacity-10"></div>

                {status && <div className="text-primary-600 mb-4 text-center text-sm font-medium">{status}</div>}
            </AuthLayout>
        </div>
    );
}
