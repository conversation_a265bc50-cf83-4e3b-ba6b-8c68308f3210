import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import InputError from '@/components/ui/input-error';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';
import { getPasswordConfirmationMessage, getPasswordValidationMessage, validatePassword } from '@/utils/passwordValidation';
import { useForm } from '@inertiajs/react';
import { motion } from 'framer-motion';
import { LoaderCircle } from 'lucide-react';
import { useEffect, useState } from 'react';
import RegisterBranding from '../auth/branding/register-branding';
import PasswordToggle from './passwordToggle';

type RegisterForm = {
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    password: string;
    password_confirmation: string;
    org_name: string;
};

export default function Register() {
    const { data, setData, errors, post, reset } = useForm<Required<RegisterForm>>({
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        org_name: '',
        password: '',
        password_confirmation: '',
    });

    //{  progress bar }//
    const [progress, setProgress] = useState(0);

    useEffect(() => {
        const fields = Object.values(data);
        const filled = fields.filter((value) => value.trim() !== '').length;
        const calculateProgress = Math.round((filled / 7) * 100);
        setProgress(calculateProgress);
    }, [data]);

    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const [passwordError, setPasswordError] = useState('');
    const [confirmPasswordError, setConfirmPasswordError] = useState('');

    const validate = () => {
        if (!data.first_name || !data.last_name || !data.email || !data.phone || !data.org_name || !data.password || !data.password_confirmation) {
            setError('Please fill in all required fields');
            return false;
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
            setError('Please enter a valid email address');
            return false;
        }

        // Validate password strength
        const passwordValidation = validatePassword(data.password);
        if (!passwordValidation.isValid) {
            setError(getPasswordValidationMessage(data.password));
            return false;
        }

        if (data.password !== data.password_confirmation) {
            setError('Passwords do not match');
            return false;
        }

        setError('');
        return true;
    };

    // Real-time password validation
    const handlePasswordChange = (password: string) => {
        setData('password', password);
        if (password) {
            const message = getPasswordValidationMessage(password);
            setPasswordError(message);
        } else {
            setPasswordError('');
        }

        // Also validate confirmation if it exists
        if (data.password_confirmation) {
            const confirmMessage = getPasswordConfirmationMessage(password, data.password_confirmation);
            setConfirmPasswordError(confirmMessage);
        }
    };

    // Real-time password confirmation validation
    const handlePasswordConfirmationChange = (confirmation: string) => {
        setData('password_confirmation', confirmation);
        if (confirmation) {
            const message = getPasswordConfirmationMessage(data.password, confirmation);
            setConfirmPasswordError(message);
        } else {
            setConfirmPasswordError('');
        }
    };

    const submit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);

        if (!validate()) {
            setIsLoading(false);
            return;
        }
        post(route('register'), {
            onFinish: () => {
                setIsLoading(false);
                reset('password', 'password_confirmation');
                setPasswordError('');
                setConfirmPasswordError('');
            },
            onError: () => {
                setIsLoading(false);
            },
        });
    };

    const inputVariants = {
        focus: { scale: 1.02, transition: { duration: 0.2 } },
        blur: { scale: 1, transition: { duration: 0.2 } },
    };

    return (
        <div className="min-h-screen max-w-full overflow-x-hidden overflow-y-auto">
            <AuthLayout
                title="Create your Sippar Account"
                description="Get started with financial management made simple"
                reverseLayout={false}
                brandingComponent={<RegisterBranding />}
                showBranding={true}
                useAcrylicBackground={true}
            >
                <div className="bg-background mb-6 h-1 w-full overflow-hidden rounded-full">
                    <motion.div
                        className="bg-primary/100 h-full"
                        initial="initial"
                        animate={{ width: `${progress}%` }}
                        transition={{ duration: 0.3 }}
                    ></motion.div>
                </div>

                {error && (
                    <motion.div
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        className="bg-destructive/10 text-background dark:text-foreground/70 dark:bg-destructive/20 mb-4 rounded-md p-3 text-sm"
                    >
                        {' '}
                        {error}{' '}
                    </motion.div>
                )}

                <form className="flex flex-col gap-4 lg:gap-6 w-full max-w-md mx-auto px-2 sm:px-4 md:px-0" onSubmit={submit}>
                    <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        transition={{ duration: 0.3 }}
                        className="space-y-3 lg:space-y-4"
                    >
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div className="space-y-1 lg:space-y-2">
                                <Label htmlFor="first_name">
                                    First Name <span className="text-destructive text-lg">*</span>
                                </Label>
                                <motion.div whileFocus="focus" variants={inputVariants}>
                                    <Input
                                        id="first_name"
                                        type="text"
                                        required
                                        tabIndex={1}
                                        value={data.first_name}
                                        onChange={(e) => setData('first_name', e.target.value)}
                                        disabled={isLoading}
                                        placeholder="First Name"
                                        className="text-secondary-foreground/80 border-muted-foreground/90 focus:border-primary/50 focus:ring-primary/20 w-full bg-transparent/50 font-semibold outline-none focus:ring"
                                    />
                                    <InputError message={errors.first_name} className="text-destructive mt-1" />
                                </motion.div>
                            </div>

                            <div className="space-y-1 lg:space-y-2">
                                <Label htmlFor="last_name">
                                    Last Name <span className="text-destructive text-lg">*</span>
                                </Label>
                                <motion.div whileFocus="focus" variants={inputVariants}>
                                    <Input
                                        id="last_name"
                                        type="text"
                                        required
                                        tabIndex={2}
                                        value={data.last_name}
                                        onChange={(e) => setData('last_name', e.target.value)}
                                        disabled={isLoading}
                                        placeholder="Last Name"
                                        className="text-secondary-foreground/80 border-muted-foreground/90 focus:border-primary/50 focus:ring-primary/20 w-full bg-transparent/50 font-semibold outline-none focus:ring"
                                    />
                                    <InputError message={errors.last_name} className="text-destructive mt-1" />
                                </motion.div>
                            </div>
                        </div>

                        <div className="space-y-1 lg:space-y-2">
                            <Label htmlFor="email">
                                Email address <span className="text-destructive text-lg">*</span>
                            </Label>
                            <motion.div whileFocus="focus" variants={inputVariants}>
                                <Input
                                    id="email"
                                    type="email"
                                    required
                                    tabIndex={3}
                                    autoComplete="email"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    disabled={isLoading}
                                    placeholder="<EMAIL>"
                                    className="text-secondary-foreground/80 border-muted-foreground/90 focus:border-primary/50 focus:ring-primary/20 w-full bg-transparent/50 font-semibold outline-none focus:ring"
                                />
                                <InputError message={errors.email} className="text-destructive mt-1" />
                            </motion.div>
                        </div>
                        <div className="space-y-1 lg:space-y-2">
                            <Label htmlFor="phone">
                                Phone <span className="text-destructive text-lg">*</span>
                            </Label>
                            <motion.div whileFocus="focus" variants={inputVariants}>
                                <Input
                                    id="phone"
                                    type="text"
                                    tabIndex={4}
                                    value={data.phone}
                                    onChange={(e) => setData('phone', e.target.value)}
                                    disabled={isLoading}
                                    placeholder="Phone number"
                                    className="text-secondary-foreground/80 border-muted-foreground/90 focus:border-primary/50 focus:ring-primary/20 w-full bg-transparent/50 font-semibold outline-none focus:ring"
                                />
                                <InputError message={errors.phone} className="text-destructive mt-1" />
                            </motion.div>
                        </div>

                        <div className="space-y-1 lg:space-y-2">
                            <Label htmlFor="org_name">
                                Organization Name <span className="text-destructive text-lg">*</span>
                            </Label>
                            <motion.div whileFocus="focus" variants={inputVariants}>
                                <Input
                                    id="org_name"
                                    value={data.org_name}
                                    onChange={(e) => setData('org_name', e.target.value)}
                                    required
                                    tabIndex={5}
                                    disabled={isLoading}
                                    placeholder="Organization name"
                                    className="text-secondary-foreground/80 border-muted-foreground/90 focus:border-primary/50 focus:ring-primary/20 w-full bg-transparent/50 font-semibold outline-none focus:ring"
                                />
                                <InputError message={errors.org_name} className="text-destructive mt-1" />
                            </motion.div>
                        </div>

                        <div className="space-y-1 lg:space-y-2">
                            <Label htmlFor="password">
                                Password <span className="text-destructive text-lg">*</span>
                            </Label>
                            <motion.div whileFocus="focus" variants={inputVariants}>
                                <div className="relative">
                                    <Input
                                        id="password"
                                        type={showPassword ? 'text' : 'password'}
                                        required
                                        tabIndex={6}
                                        autoComplete="new-password"
                                        value={data.password}
                                        onChange={(e) => handlePasswordChange(e.target.value)}
                                        disabled={isLoading}
                                        className="text-secondary-foreground/80 border-muted-foreground/90 focus:border-primary/50 focus:ring-primary/20 w-full bg-transparent/50 pr-10 font-semibold outline-none focus:ring"
                                        placeholder="••••••••"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowPassword(!showPassword)}
                                        className="absolute inset-y-0 right-0 flex items-center px-3 focus:outline-none"
                                    >
                                        <PasswordToggle show={showPassword} toggle={() => setShowPassword(!showPassword)} />
                                    </button>
                                </div>
                                <InputError message={errors.password || passwordError} className="text-destructive mt-1" />
                            </motion.div>
                        </div>

                        <div className="space-y-1 lg:space-y-2">
                            <Label htmlFor="password_confirmation">
                                Confirm Password <span className="text-destructive text-lg">*</span>
                            </Label>
                            <motion.div whileFocus="focus" variants={inputVariants}>
                                <div className="relative">
                                    <Input
                                        id="password_confirmation"
                                        type={showConfirmPassword ? 'text' : 'password'}
                                        required
                                        tabIndex={7}
                                        autoComplete="new-password"
                                        value={data.password_confirmation}
                                        onChange={(e) => handlePasswordConfirmationChange(e.target.value)}
                                        className="text-secondary-foreground/80 border-muted-foreground/90 focus:border-primary/50 focus:ring-primary/20 w-full bg-transparent/50 pr-10 font-semibold outline-none focus:ring"
                                        disabled={isLoading}
                                        placeholder="••••••••"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                        className="absolute inset-y-0 right-0 flex items-center px-3 focus:outline-none"
                                    >
                                        <PasswordToggle show={showConfirmPassword} toggle={() => setShowConfirmPassword(!showConfirmPassword)} />
                                    </button>
                                </div>
                                <InputError message={errors.password_confirmation || confirmPasswordError} className="text-destructive mt-1" />
                            </motion.div>
                        </div>
                    </motion.div>

                    <div className="flex justify-between gap-4">
                        <Button type="submit" className="w-full" disabled={isLoading}>
                            {isLoading ? (
                                <>
                                    <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                                    Processing...
                                </>
                            ) : (
                                'Create Account'
                            )}
                        </Button>
                    </div>
                    <div className="text-foreground/100 text-center text-sm mt-4 px-2">
                        Already have an account?{' '}
                        <TextLink
                            href={route('login')}
                            className="text-primary/100 dark:text-shadow-primary/80 hover:text-primary-700 font-medium hover:underline"
                        >
                            Sign in
                        </TextLink>
                    </div>
                </form>

                {/* Decorative circles */}
                <div className="bg-foreground absolute top-0 right-0 h-40 w-40 sm:h-64 sm:w-64 translate-x-1/2 -translate-y-1/2 rounded-full opacity-10"></div>
                <div className="bg-foreground absolute bottom-0 left-0 h-52 w-52 sm:h-80 sm:w-80 -translate-x-1/2 translate-y-1/2 rounded-full opacity-10"></div>
            </AuthLayout>
        </div>
    );
}
