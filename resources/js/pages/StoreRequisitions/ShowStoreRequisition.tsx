import { Head } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { PageProps } from '@inertiajs/core';
import { Link, usePage, router } from '@inertiajs/react';
import { ArrowLeft, Package, User, Calendar, FileText, Send, CheckCircle, XCircle } from "lucide-react";
import { useState, useMemo } from 'react';
import AppLayout from '@/layouts/app-layout';
import { StoreRequisition, StoreRequisitionItem, InventoryItem, StoreRequisitionHistoryEntry } from '@/types/store-requisitions';
import { canEditStoreRequisition, getStoreRequisitionEditRoute } from '@/utils/store-requisitions';
import { ApprovalDialog } from './ApprovalDialog';
import { useStoreRequisitionApproval } from '@/hooks/use-store-requisition-approval';
import { StoreRequisitionHistory } from '@/components/StoreRequisitions/StoreRequisitionHistory';
import { AttachmentsList } from '@/components/AttachmentsList';
import { FileUpload } from '@/components/FileUpload';
import { badgeVariants, Badge } from '@/components/ui/badge';
import { VariantProps } from 'class-variance-authority';

const getInventoryItem = (item: StoreRequisitionItem): InventoryItem | undefined => {
    return item.inventoryItem || (item as StoreRequisitionItem & { inventory_item?: InventoryItem }).inventory_item;
};

interface ShowStoreRequisitionPageProps extends PageProps {
    storeRequisition: StoreRequisition;
    histories: StoreRequisitionHistoryEntry[];
    user: {
        id: number;
        name: string;
        email: string;
        permissions: string[];
        roles?: string[];
    };
    canEdit: boolean;
    canAttachFiles: boolean;
    canApprove: boolean;
    canIssue: boolean;
    attachmentUIState: {
        isUploading: boolean;
        uploadProgress: number;
        error: string | null;
    };
}

export default function ShowStoreRequisition() {
    const {
        storeRequisition,
        histories,
        user,
        canAttachFiles,
        
    } = usePage<ShowStoreRequisitionPageProps>().props;

    const breadcrumbs = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Store Requisitions',
            href: '/store-requisitions',
        },
        {
            title: `Requisition #${storeRequisition.id}`,
            href: `/store-requisitions/${storeRequisition.id}`,
        },
    ];

    const canEdit = canEditStoreRequisition(storeRequisition, user.id);
    const canSubmit = storeRequisition.status === 'draft' && storeRequisition.requester_user_id === user.id;
    const [isSubmitting, setIsSubmitting] = useState(false);

    const [selectedRequisition, setSelectedRequisition] = useState<StoreRequisition | null>(null);
    const [dialogAction, setDialogAction] = useState<'approve' | 'reject' | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const { approveRequisition, rejectRequisition, isSubmitting: isApprovalSubmitting } = useStoreRequisitionApproval({
        onSuccess: () => {
            setIsDialogOpen(false);
            setSelectedRequisition(null);
            setDialogAction(null);
            router.reload();
        }
    });

    const canApprove = useMemo(() => {
        if (storeRequisition.status !== 'pending_approval' || storeRequisition.requester_user_id === user.id) {
            return false;
        }
        const requesterPermissions = storeRequisition.requester?.permissions;
        const requesterIsStoreKeeper = Array.isArray(requesterPermissions)
            ? requesterPermissions.includes('store-keep')
            : false;

        if (requesterIsStoreKeeper) {
            return user.roles?.includes('Finance Manager') || user.roles?.includes('Organization Admin');
        } else {
            return user.permissions.includes('store-keep');
        }
    }, [storeRequisition, user]);

    const openApprovalDialog = () => {
        setSelectedRequisition(storeRequisition);
        setDialogAction('approve');
        setIsDialogOpen(true);
    };

    const openRejectionDialog = () => {
        setSelectedRequisition(storeRequisition);
        setDialogAction('reject');
        setIsDialogOpen(true);
    };

    const handleApprovalSubmit = (comments?: string) => {
        if (!selectedRequisition || !dialogAction) return;

        if (dialogAction === 'approve') {
            approveRequisition(selectedRequisition.id, comments);
        } else if (dialogAction === 'reject') {
            rejectRequisition(selectedRequisition.id, comments || '');
        }
    };

    const handleSubmit = () => {
        if (isSubmitting) return;

        setIsSubmitting(true);
        router.post(`/store-requisitions/${storeRequisition.id}/submit`, {}, {
            onSuccess: () => {
                setIsSubmitting(false);
            },
            onError: () => {
                setIsSubmitting(false);
            }
        });
    };
    
    const getStatusBadge = (status: string) => {
        const statusConfig: Record<string, { variant: VariantProps<typeof badgeVariants>['variant']; label: string }> = {
            draft: { variant: 'secondary', label: 'Draft' },
            submitted: { variant: 'pending', label: 'Pending Approval' },
            pending_approval: { variant: 'pending', label: 'Pending Approval' },
            approved: { variant: 'approved', label: 'Approved' },
            rejected: { variant: 'rejected', label: 'Rejected' },
            returned_for_revision: { variant: 'outline', label: 'Returned for Revision' },
            issued: { variant: 'approved', label: 'Issued' },
            partially_issued: { variant: 'secondary', label: 'Partially Issued' },
        };

        const config = statusConfig[status] ?? {
            variant: 'default',
            label: status.charAt(0).toUpperCase() + status.slice(1).replace(/_/g, ' '),
        };

        return <Badge variant={config.variant}>{config.label}</Badge>;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Store Requisition #${storeRequisition.id}`} />
            
            <div className="bg-background/90 flex h-full flex-1 flex-col gap-4 sm:gap-6 p-3 sm:p-4 lg:p-6">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4">
                        <Button variant="outline" size="sm" className="w-fit" asChild>
                            <Link href="/store-requisitions">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                <span className="hidden sm:inline">Back to Store Requisitions</span>
                                <span className="sm:hidden">Back</span>
                            </Link>
                        </Button>
                        <div className="min-w-0 flex-1">
                            <h1 className="text-xl sm:text-2xl font-bold text-foreground truncate">
                                Store Requisition #{storeRequisition.id}
                            </h1>
                            <p className="text-sm sm:text-base text-muted-foreground">
                                View store requisition details and status
                            </p>
                        </div>
                    </div>

                    <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-2">
                        {getStatusBadge(storeRequisition.status)}

                        <div className="flex flex-col gap-2 sm:flex-row sm:gap-2">
                            {canSubmit && (
                                <Button
                                    onClick={handleSubmit}
                                    disabled={isSubmitting}
                                    className="gap-2 text-sm"
                                    size="sm"
                                >
                                    <Send className="h-4 w-4" />
                                    <span className="hidden sm:inline">{isSubmitting ? 'Submitting...' : 'Submit for Approval'}</span>
                                    <span className="sm:hidden">{isSubmitting ? 'Submitting...' : 'Submit'}</span>
                                </Button>
                            )}
                            {canEdit && (
                                <Button asChild variant="outline" size="sm" className="text-sm">
                                    <Link href={getStoreRequisitionEditRoute(storeRequisition)}>
                                        Edit
                                    </Link>
                                </Button>
                            )}
                            {canApprove && (
                                <div className="flex gap-2">
                                    <Button
                                        onClick={openApprovalDialog}
                                        disabled={isApprovalSubmitting}
                                        className="gap-2 text-sm flex-1 sm:flex-none"
                                        size="sm"
                                    >
                                        <CheckCircle className="h-4 w-4" />
                                        Approve
                                    </Button>
                                    <Button
                                        variant="outline"
                                        onClick={openRejectionDialog}
                                        disabled={isApprovalSubmitting}
                                        className="gap-2 text-sm flex-1 sm:flex-none"
                                        size="sm"
                                    >
                                        <XCircle className="h-4 w-4" />
                                        Reject
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                <Tabs defaultValue="details" className="space-y-4 sm:space-y-6">
                    <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="details" className="text-sm">
                            <span className="hidden sm:inline">Requisition Details</span>
                            <span className="sm:hidden">Details</span>
                        </TabsTrigger>
                        <TabsTrigger value="attachments" className="text-sm">Attachments</TabsTrigger>
                        {histories.length > 0 && (
                            <TabsTrigger value="history" className="text-sm">History</TabsTrigger>
                        )}
                    </TabsList>

                    <TabsContent value="details" className="space-y-4 sm:space-y-6">
                        <div className="grid gap-4 sm:gap-6 lg:grid-cols-2">
                            <Card>
                                <CardHeader className="pb-3 sm:pb-6">
                                    <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                                        <FileText className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                        <span className="truncate">Requisition Details</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3 sm:space-y-4">
                                    <div>
                                        <label className="text-xs sm:text-sm font-medium text-muted-foreground">Purpose</label>
                                        <p className="mt-1 text-sm sm:text-base break-words">{storeRequisition.purpose}</p>
                                    </div>

                                    <Separator />

                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                        <div>
                                            <label className="text-xs sm:text-sm font-medium text-muted-foreground">Department</label>
                                            <p className="mt-1 text-sm sm:text-base truncate">{storeRequisition.department?.name}</p>
                                        </div>
                                        <div>
                                            <label className="text-xs sm:text-sm font-medium text-muted-foreground">Branch</label>
                                            <p className="mt-1 text-sm sm:text-base truncate">{storeRequisition.branch?.name}</p>
                                        </div>
                                    </div>

                                    <Separator />

                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                        <div>
                                            <label className="text-xs sm:text-sm font-medium text-muted-foreground">Requested Date</label>
                                            <p className="mt-1 flex items-center gap-2 text-sm sm:text-base">
                                                <Calendar className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                                <span className="truncate">
                                                    {storeRequisition.requested_at ? new Date(storeRequisition.requested_at).toLocaleDateString() : 'Not requested'}
                                                </span>
                                            </p>
                                        </div>
                                        {storeRequisition.approved_at && (
                                            <div>
                                                <label className="text-xs sm:text-sm font-medium text-muted-foreground">Approved Date</label>
                                                <p className="mt-1 flex items-center gap-2 text-sm sm:text-base">
                                                    <Calendar className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                                    <span className="truncate">
                                                        {new Date(storeRequisition.approved_at).toLocaleDateString()}
                                                    </span>
                                                </p>
                                            </div>
                                        )}
                                    </div>

                                    {storeRequisition.issued_at && (
                                        <div>
                                            <label className="text-xs sm:text-sm font-medium text-muted-foreground">Issued Date</label>
                                            <p className="mt-1 flex items-center gap-2 text-sm sm:text-base">
                                                <Calendar className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                                <span className="truncate">
                                                    {new Date(storeRequisition.issued_at).toLocaleDateString()}
                                                </span>
                                            </p>
                                        </div>
                                    )}

                                    {storeRequisition.rejection_reason && (
                                        <>
                                            <Separator />
                                            <div>
                                                <label className="text-xs sm:text-sm font-medium text-muted-foreground">Rejection Reason</label>
                                                <p className="mt-1 text-sm sm:text-base text-destructive break-words">{storeRequisition.rejection_reason}</p>
                                            </div>
                                        </>
                                    )}
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="pb-3 sm:pb-6">
                                    <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                                        <User className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                        <span className="truncate">People Involved</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3 sm:space-y-4">
                                    <div>
                                        <label className="text-xs sm:text-sm font-medium text-muted-foreground">Requester</label>
                                        <p className="mt-1 text-sm sm:text-base font-medium truncate">
                                            {storeRequisition.requester?.first_name} {storeRequisition.requester?.last_name}
                                        </p>
                                        <p className="text-xs sm:text-sm text-muted-foreground truncate">
                                            {storeRequisition.requester?.email}
                                        </p>
                                    </div>

                                    {storeRequisition.approver && (
                                        <>
                                            <Separator />
                                            <div>
                                                <label className="text-xs sm:text-sm font-medium text-muted-foreground">Approver</label>
                                                <p className="mt-1 text-sm sm:text-base font-medium truncate">
                                                    {storeRequisition.approver.first_name} {storeRequisition.approver.last_name}
                                                </p>
                                                <p className="text-xs sm:text-sm text-muted-foreground truncate">
                                                    {storeRequisition.approver.email}
                                                </p>
                                            </div>
                                        </>
                                    )}

                                    {storeRequisition.issuer && (
                                        <>
                                            <Separator />
                                            <div>
                                                <label className="text-xs sm:text-sm font-medium text-muted-foreground">Issuer</label>
                                                <p className="mt-1 text-sm sm:text-base font-medium truncate">
                                                    {storeRequisition.issuer.first_name} {storeRequisition.issuer.last_name}
                                                </p>
                                                <p className="text-xs sm:text-sm text-muted-foreground truncate">
                                                    {storeRequisition.issuer.email}
                                                </p>
                                            </div>
                                        </>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        <Card>
                            <CardHeader className="pb-3 sm:pb-6">
                                <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                                    <Package className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                    <span className="truncate">Requested Items ({storeRequisition.items?.length || 0})</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="overflow-x-auto">
                                    <table className="w-full border-collapse bg-background/100 shadow-sm">
                                        <thead>
                                            <tr className="bg-muted-foreground/40">
                                                <th className="border px-4 py-2 text-left text-foreground/100">Item</th>
                                                <th className="border px-4 py-2 text-left text-foreground/100">SKU</th>
                                                <th className="border px-4 py-2 text-center text-foreground/100">Requested</th>
                                                <th className="border px-4 py-2 text-center text-foreground/100">Issued</th>
                                                <th className="border px-4 py-2 text-center text-foreground/100">Unit</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {storeRequisition.items &&
                                                storeRequisition.items.map((item, index) => {
                                                    const inventoryItem = getInventoryItem(item);
                                                    return (
                                                        <tr key={index} className={index % 2 === 0 ? 'bg-background/70' : 'bg-background/90'}>
                                                            <td className="border px-4 py-2 text-foreground/90 font-medium">
                                                                {inventoryItem?.name}
                                                            </td>
                                                            <td className="border px-4 py-2 text-foreground/90 font-mono text-sm">
                                                                {inventoryItem?.sku}
                                                            </td>
                                                            <td className="border px-4 py-2 text-center text-foreground/90">
                                                                {item.quantity_requested}
                                                            </td>
                                                            <td className="border px-4 py-2 text-center text-purple-600 font-medium">
                                                                {item.quantity_issued || 0}
                                                            </td>
                                                            <td className="border px-4 py-2 text-center text-foreground/90">
                                                                {inventoryItem?.unit_of_measure}
                                                            </td>
                                                        </tr>
                                                    )
                                                })}
                                            {(!storeRequisition.items || storeRequisition.items.length === 0) && (
                                                <tr>
                                                    <td colSpan={5} className="border px-4 py-2 text-center text-gray-500">
                                                        No items found
                                                    </td>
                                                </tr>
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="attachments" className="space-y-4 sm:space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Attachments</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                                    <AttachmentsList 
                                        attachments={storeRequisition.attachments || []} 
                                        canDelete={canAttachFiles} 
                                        className="w-full" 
                                    />
                                    {canAttachFiles && (
                                        <FileUpload
                                            entityType="store_requisition"
                                            entityId={storeRequisition.id}
                                            uploadedAtStep={storeRequisition.status}
                                            className="w-full"
                                        />
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="history" className="space-y-4 sm:space-y-6">
                        <StoreRequisitionHistory histories={histories} />
                    </TabsContent>
                </Tabs>
            </div>

            {selectedRequisition && dialogAction && (
                <ApprovalDialog
                    requisition={selectedRequisition}
                    action={dialogAction}
                    isOpen={isDialogOpen}
                    onClose={() => setIsDialogOpen(false)}
                    onSubmit={handleApprovalSubmit}
                    isSubmitting={isApprovalSubmitting}
                />
            )}
        </AppLayout>
    );
}