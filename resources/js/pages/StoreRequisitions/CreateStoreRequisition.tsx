import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useState, useRef } from 'react';
import { Head } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { PageProps } from '@inertiajs/core';
import { router, usePage } from '@inertiajs/react';
import { LoaderCircle, Upload, File, X, Paperclip } from "lucide-react";
import AppLayout from '@/layouts/app-layout';
import StoreRequisitionItemsManager from '@/components/StoreRequisitions/StoreRequisitionItemsManager';
import { useStoreRequisitionForm } from '@/hooks/use-store-requisition-form';
import { InventoryItem, StoreRequisitionItemFormData } from '@/types/store-requisitions';
import { cn } from '@/lib/utils';


type Department = {
    id: number;
    name: string;
    branch_id: number;
    organization_id: number;
};

interface CreateStoreRequisitionPageProps extends PageProps {
    inventory_items: InventoryItem[];
    user_branch_id?: number;
    user_department_id?: number;
    user_departments: Department[];
    department_id?: number;
    department_name?: string;
}

// File attachment interface
interface FileAttachment {
    file: File;
    description: string;
}

const CreateStoreRequisition = () => {
    const {
        inventory_items = [],
        user_branch_id,
        user_department_id,
        user_departments = [],
        department_id,
        department_name,
    } = usePage<CreateStoreRequisitionPageProps>().props;

    // File attachment state
    const [attachments, setAttachments] = useState<FileAttachment[]>([]);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Function to handle department change (similar to petty cash requisition)
    const handleDepartmentChange = (departmentId: string) => {
        // Use Inertia router to navigate to the same page with a different department parameter
        router.get('/store-requisitions/create', { department: departmentId });
    };

    // Determine the correct branch and department IDs
    const selectedDepartment = department_id
        ? user_departments.find(d => d.id === department_id)
        : user_departments.find(d => d.id === user_department_id) || user_departments[0];

    const initialBranchId = selectedDepartment?.branch_id || user_branch_id || (user_departments[0]?.branch_id);
    const initialDepartmentId = selectedDepartment?.id || user_department_id || (user_departments[0]?.id);

    // Initialize the store requisition form hook
    const {
        data,
        setData,
        items,
        addItem,
        removeItem,
        updateItem,
        errors,
        processing,
    } = useStoreRequisitionForm({
        initialData: {
            branch_id: initialBranchId,
            department_id: initialDepartmentId,
            purpose: '',
            items: [{ inventory_item_id: null, quantity_requested: 1 }]
        },
        onSuccess: () => {
            // Success message will be handled by the server redirect
            // No need to show toast or redirect here since server handles it
        },
        onError: (errors) => {

            // Extract error messages
            const errorMessages: string[] = [];
            if (typeof errors === 'object' && errors !== null) {
                for (const [key, value] of Object.entries(errors)) {
                    if (typeof value === 'string') {
                        errorMessages.push(`${key}: ${value}`);
                    } else if (Array.isArray(value)) {
                        errorMessages.push(`${key}: ${(value as string[]).join(', ')}`);
                    }
                }
            }

            const message = errorMessages.length > 0
                ? errorMessages.join('\n')
                : 'Submission failed. Please check the form and try again.';

            window.showToast?.({
                title: 'Validation Error',
                message: message,
                type: 'error'
            });
        }
    });



    // Handle item changes using the hook's updateItem function
    const handleItemChange = (index: number, field: keyof StoreRequisitionItemFormData, value: string) => {
        if (field === 'quantity_requested') {
            // Convert to number, default to 1 if invalid
            const numValue = parseFloat(value) || 1;
            updateItem(index, field, numValue);
        } else if (field === 'inventory_item_id') {
            updateItem(index, field, value ? Number(value) : null);
        }
    };

    // Handle remove item with toast notification
    const handleRemoveItem = (index: number) => {
        if (items.length === 1) {
            window.showToast?.({
                title: 'Validation Error',
                message: 'At least one item required',
                type: 'error'
            });
            return;
        }
        removeItem(index);
    };

// Replace your handleSubmit and handleSaveAsDraft functions with these:

    const handleSubmit: FormEventHandler = (e) => {
        e.preventDefault();
        
        // If your backend expects FormData, use this approach:
        const formData = new FormData();
        
        // Add regular form data
        formData.append('branch_id', data.branch_id?.toString() || '');
        formData.append('department_id', data.department_id?.toString() || '');
        formData.append('purpose', data.purpose);
        
        // Add items
        data.items.forEach((item, index) => {
            if (item.inventory_item_id) {
                formData.append(`items[${index}][inventory_item_id]`, item.inventory_item_id.toString());
            }
            formData.append(`items[${index}][quantity_requested]`, item.quantity_requested.toString());
        });
        
        // Add attachments
        attachments.forEach((attachment, index) => {
            formData.append(`attachments[${index}]`, attachment.file);
            if (attachment.description) {
                formData.append(`attachment_descriptions[${index}]`, attachment.description);
            }
        });
        
        // Use router.post directly instead of the hook's submit method
        router.post('/store-requisitions', formData, {
            forceFormData: true,
            onSuccess: () => {
                // Handle success
                window.showToast?.({
                    title: 'Success',
                    message: 'Store requisition submitted successfully',
                    type: 'success'
                });
            },
            onError: (errors) => {
                // Your existing error handling code
                const errorMessages: string[] = [];
                if (typeof errors === 'object' && errors !== null) {
                    for (const [key, value] of Object.entries(errors)) {
                        if (typeof value === 'string') {
                            errorMessages.push(`${key}: ${value}`);
                        } else if (Array.isArray(value)) {
                            errorMessages.push(`${key}: ${(value as string[]).join(', ')}`);
                        }
                    }
                }

                const message = errorMessages.length > 0
                    ? errorMessages.join('\n')
                    : 'Submission failed. Please check the form and try again.';

                window.showToast?.({
                    title: 'Validation Error',
                    message: message,
                    type: 'error'
                });
            }
        });
    };

    const handleSaveAsDraft = () => {
        // Same as handleSubmit but add draft status
        const formData = new FormData();
        
        formData.append('branch_id', data.branch_id?.toString() || '');
        formData.append('department_id', data.department_id?.toString() || '');
        formData.append('purpose', data.purpose);
        formData.append('status', 'draft'); // Mark as draft
        
        // Add items
        data.items.forEach((item, index) => {
            if (item.inventory_item_id) {
                formData.append(`items[${index}][inventory_item_id]`, item.inventory_item_id.toString());
            }
            formData.append(`items[${index}][quantity_requested]`, item.quantity_requested.toString());
        });
        
        // Add attachments
        attachments.forEach((attachment, index) => {
            formData.append(`attachments[${index}]`, attachment.file);
            if (attachment.description) {
                formData.append(`attachment_descriptions[${index}]`, attachment.description);
            }
        });
        
        router.post('/store-requisitions', formData, {
            forceFormData: true,
            onSuccess: () => {
                window.showToast?.({
                    title: 'Success',
                    message: 'Store requisition saved as draft',
                    type: 'success'
                });
            },
            onError: () => {
                // Same error handling as above
            }
        });
    };

    // File attachment handlers
    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        if (files.length === 0) return;
        
        const newAttachments = files.map(file => ({
            file,
            description: ''
        }));
        
        setAttachments(prev => [...prev, ...newAttachments]);
        
    };

    const removeAttachment = (index: number) => {
        setAttachments(prev => prev.filter((_, i) => i !== index));
    };

    const updateAttachmentDescription = (index: number, description: string) => {
        setAttachments(prev => prev.map((attachment, i) =>
            i === index ? { ...attachment, description } : attachment
        ));
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };



    const breadcrumbs = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Store Requisitions',
            href: '/store-requisitions',
        },
        {
            title: 'Create Store Requisition',
            href: '/store-requisitions/create',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Store Requisition" />

            <div className="space-y-4 sm:space-y-6 p-3 sm:p-4 lg:p-6">
                {/* Department Information Card - Mobile Responsive */}
                <Card className="w-full max-w-4xl mx-auto">
                    <CardHeader className="pb-3 sm:pb-6">
                        <CardTitle className="text-lg sm:text-xl lg:text-2xl">Department Information</CardTitle>
                        <CardDescription className="text-sm sm:text-base">
                            This store requisition will be created for the following department
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="pt-0">
                        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                            <div className="min-w-0 flex-1">
                                <p className="text-xs sm:text-sm text-muted-foreground">Department:</p>
                                <p className="text-base sm:text-lg font-medium truncate">{department_name || 'Not selected'}</p>
                            </div>

                            {user_departments.length > 1 && (
                                <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                                    <Badge variant="secondary" className="w-fit text-xs sm:text-sm">
                                        {user_departments.length} departments available
                                    </Badge>
                                    <Select value={department_id?.toString() || ''} onValueChange={handleDepartmentChange}>
                                        <SelectTrigger className="w-full sm:w-[200px]">
                                            <SelectValue placeholder="Switch department" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {user_departments.map((dept) => (
                                                <SelectItem key={dept.id} value={dept.id.toString()}>
                                                    {dept.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Main Form Card - Mobile Responsive */}
                <Card className="mx-auto w-full max-w-4xl">
                    <CardHeader className="pb-3 sm:pb-6">
                        <CardTitle className="text-lg sm:text-xl lg:text-2xl">Store Requisition Form</CardTitle>
                        <CardDescription className="text-sm sm:text-base">
                            Complete the form below to submit a new store requisition request
                        </CardDescription>
                    </CardHeader>

                    <form onSubmit={handleSubmit}>
                        <CardContent className="space-y-4 sm:space-y-6 pt-0">
                            {/* Purpose Section */}
                            <div className="space-y-2">
                                <Label htmlFor="purpose" className="text-sm sm:text-base font-medium">
                                    Purpose <span className="text-destructive">*</span>
                                </Label>
                                <Textarea
                                    id="purpose"
                                    value={data.purpose}
                                    onChange={(e) => setData('purpose', e.target.value)}
                                    placeholder="Briefly describe the purpose of this store requisition"
                                    className={cn(
                                        "min-h-[80px] sm:min-h-[100px] text-sm sm:text-base",
                                        errors.purpose ? 'border-destructive' : ''
                                    )}
                                    rows={3}
                                    autoFocus
                                />
                                {errors.purpose && <p className="text-xs sm:text-sm text-destructive">{errors.purpose}</p>}
                            </div>

                            {/* Items Section */}
                            <div className="space-y-3 sm:space-y-4">
                                <div className="flex items-center gap-2">
                                    <h3 className="text-base sm:text-lg font-medium">Requested Items</h3>
                                    <Badge variant="outline" className="text-xs">
                                        {items.length} item{items.length !== 1 ? 's' : ''}
                                    </Badge>
                                </div>
                                <StoreRequisitionItemsManager
                                    items={items}
                                    inventoryItems={inventory_items}
                                    errors={errors}
                                    onItemChange={handleItemChange}
                                    onAddItem={addItem}
                                    onRemoveItem={handleRemoveItem}
                                />
                            </div>

                            {/* Attachments Section */}
                            <div className="space-y-3 sm:space-y-4">
                                <div className="flex items-center gap-2">
                                    <h3 className="text-base sm:text-lg font-medium">Attachments</h3>
                                    <Badge variant="secondary" className="text-xs">
                                        Optional
                                    </Badge>
                                    {attachments.length > 0 && (
                                        <Badge variant="outline" className="text-xs">
                                            {attachments.length} file{attachments.length !== 1 ? 's' : ''}
                                        </Badge>
                                    )}
                                </div>

                                {/* File Upload Area */}
                                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 sm:p-6 transition-colors hover:border-muted-foreground/50">
                                    <div className="text-center">
                                        <Upload className="mx-auto h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground mb-3 sm:mb-4" />
                                        <div className="space-y-2">
                                            <p className="text-sm sm:text-base font-medium">Upload supporting documents</p>
                                            <p className="text-xs sm:text-sm text-muted-foreground">
                                                PDF, DOC, DOCX, XLS, XLSX, JPG, PNG (Max 10MB each)
                                            </p>
                                        </div>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            className="mt-3 sm:mt-4"
                                            onClick={() => fileInputRef.current?.click()}
                                        >
                                            <Paperclip className="mr-2 h-4 w-4" />
                                            Choose Files
                                        </Button>
                                        <Input
                                            ref={fileInputRef}
                                            type="file"
                                            multiple
                                            accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                                            onChange={handleFileSelect}
                                            className="hidden"
                                        />
                                    </div>
                                </div>

                                {/* Attached Files List */}
                                {attachments.length > 0 && (
                                    <div className="space-y-3">
                                        <p className="text-sm font-medium text-muted-foreground">Attached Files:</p>
                                        <div className="space-y-2">
                                            {attachments.map((attachment, index) => (
                                                <div key={index} className="border rounded-lg p-3 sm:p-4 bg-muted/30">
                                                    <div className="flex items-start gap-3">
                                                        <File className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                                                        <div className="flex-1 min-w-0 space-y-2">
                                                            <div className="flex items-center justify-between gap-2">
                                                                <div className="min-w-0 flex-1">
                                                                    <p className="text-sm font-medium truncate">{attachment.file.name}</p>
                                                                    <p className="text-xs text-muted-foreground">
                                                                        {formatFileSize(attachment.file.size)}
                                                                    </p>
                                                                </div>
                                                                <Button
                                                                    type="button"
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => removeAttachment(index)}
                                                                    className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                                                                >
                                                                    <X className="h-4 w-4" />
                                                                </Button>
                                                            </div>
                                                            <Textarea
                                                                placeholder="Add a description for this file (optional)"
                                                                value={attachment.description}
                                                                onChange={(e) => updateAttachmentDescription(index, e.target.value)}
                                                                className="text-sm resize-none"
                                                                rows={2}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </CardContent>

                        <CardFooter className="flex flex-col gap-3 sm:flex-row sm:justify-between pt-4 sm:pt-6">
                            <Button
                                type="button"
                                variant="outline"
                                disabled={processing}
                                onClick={() => router.visit('/store-requisitions')}
                                className="w-full sm:w-auto hover:bg-muted hover:text-foreground transition-colors"
                            >
                                Cancel
                            </Button>
                            <div className="flex flex-col gap-2 sm:flex-row sm:gap-2">
                                <Button
                                    type="button"
                                    variant="secondary"
                                    onClick={handleSaveAsDraft}
                                    disabled={processing}
                                    className="w-full sm:w-auto hover:bg-muted hover:text-foreground transition-colors"
                                >
                                    {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                                    <span className="hidden sm:inline">Save as Draft</span>
                                    <span className="sm:hidden">Save Draft</span>
                                </Button>
                                <Button type="submit" disabled={processing} className="w-full sm:w-auto">
                                    {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                                    <span className="hidden sm:inline">Submit Store Requisition</span>
                                    <span className="sm:hidden">Submit</span>
                                </Button>
                            </div>
                        </CardFooter>
                    </form>
                </Card>
            </div>
        </AppLayout>
    );
};

export default CreateStoreRequisition;
