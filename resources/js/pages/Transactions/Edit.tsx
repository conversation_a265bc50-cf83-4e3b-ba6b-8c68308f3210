import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { formatCurrency } from '@/lib/utils';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save, X, AlertTriangle } from 'lucide-react';
import React from 'react';

interface PaymentMethod {
    id: string;
    name: string;
}

interface User {
    id: number;
    first_name: string;
    last_name: string;
    full_name: string;
    email: string;
}

interface Requisition {
    id: number;
    requisition_number: string;
    purpose: string;
    requester: User;
}

interface Transaction {
    id: number;
    requisition_id: number;
    status: string;
    total_amount: number;
    payment_method?: string;
    account_details?: string;
    disbursement_transaction_id?: string;
    transaction_cost?: number;
    description?: string;
    requisition?: Requisition;
}

interface EditProps {
    transaction: Transaction;
    paymentMethods: PaymentMethod[];
}

export default function Edit({ transaction, paymentMethods }: EditProps) {
    // Initialize form hook first (before any conditional returns)
    const { data, setData, put, processing, errors } = useForm({
        payment_method: transaction?.payment_method || '',
        account_details: transaction?.account_details || '',
        disbursement_transaction_id: transaction?.disbursement_transaction_id || '',
        transaction_cost: transaction?.transaction_cost?.toString() || '',
        description: transaction?.description || '',
        comments: '',
    });

    // Validate essential data after hooks are initialized
    if (!transaction || !transaction.id) {
        return (
            <AppLayout breadcrumbs={[]}>
                <Head title="Transaction Not Found" />
                <div className="flex h-full flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
                            <h2 className="text-xl font-semibold mb-2">Transaction Not Found</h2>
                            <p className="text-muted-foreground mb-4">The transaction data could not be loaded.</p>
                            <Button asChild>
                                <Link href="/transactions">
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Back to Transactions
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                </div>
            </AppLayout>
        );
    }

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Transactions',
            href: '/transactions',
        },
        {
            title: `Transaction #${transaction.id}`,
            href: `/transactions/${transaction.id}`,
        },
        {
            title: 'Edit',
            href: `/transactions/${transaction.id}/edit`,
        },
    ];

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(`/transactions/${transaction.id}`, {
            onSuccess: () => {
                // Redirect will be handled by the controller
            },
        });
    };

    const getPaymentMethodFields = () => {
        switch (data.payment_method) {
            case 'bank':
                return (
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="account_details">Bank Account Details</Label>
                            <Textarea
                                id="account_details"
                                placeholder="Enter bank account details (Account Name, Account Number, Bank Name, Branch, etc.)"
                                value={data.account_details}
                                onChange={(e) => setData('account_details', e.target.value)}
                                rows={4}
                            />
                            {errors.account_details && (
                                <p className="text-sm text-destructive mt-1">{errors.account_details}</p>
                            )}
                        </div>
                    </div>
                );
            case 'mpesa':
                return (
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="account_details">M-Pesa Details</Label>
                            <Textarea
                                id="account_details"
                                placeholder="Enter M-Pesa details (Phone Number, Account Name, etc.)"
                                value={data.account_details}
                                onChange={(e) => setData('account_details', e.target.value)}
                                rows={3}
                            />
                            {errors.account_details && (
                                <p className="text-sm text-destructive mt-1">{errors.account_details}</p>
                            )}
                        </div>
                    </div>
                );
            case 'cash':
                return (
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="account_details">Cash Payment Details</Label>
                            <Textarea
                                id="account_details"
                                placeholder="Enter cash payment details (Recipient name, collection method, etc.)"
                                value={data.account_details}
                                onChange={(e) => setData('account_details', e.target.value)}
                                rows={3}
                            />
                            {errors.account_details && (
                                <p className="text-sm text-destructive mt-1">{errors.account_details}</p>
                            )}
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit Transaction #${transaction.id}`} />

            <div className="flex h-full flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="outline" size="icon" asChild>
                            <Link href={`/transactions/${transaction.id}`}>
                                <ArrowLeft className="h-4 w-4" />
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold">Edit Transaction #{transaction.id}</h1>
                            {transaction.requisition && (
                                <p className="text-muted-foreground">
                                    Requisition: {transaction.requisition.requisition_number} - {transaction.requisition.purpose}
                                </p>
                            )}
                        </div>
                    </div>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                    {/* Transaction Details */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Transaction Information</CardTitle>
                            <CardDescription>
                                Basic transaction details that cannot be modified
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {transaction.requisition ? (
                                <>
                                    <div>
                                        <Label>Requisition Number</Label>
                                        <p className="text-sm font-medium">{transaction.requisition.requisition_number}</p>
                                    </div>
                                    <div>
                                        <Label>Purpose</Label>
                                        <p className="text-sm">{transaction.requisition.purpose}</p>
                                    </div>
                                    <div>
                                        <Label>Requester</Label>
                                        <p className="text-sm">
                                            {transaction.requisition.requester
                                                ? `${transaction.requisition.requester.first_name} ${transaction.requisition.requester.last_name}`.trim()
                                                : 'N/A'
                                            }
                                        </p>
                                    </div>
                                </>
                            ) : (
                                <div>
                                    <Label>Requisition</Label>
                                    <p className="text-sm text-muted-foreground">No requisition data available</p>
                                </div>
                            )}
                            <div>
                                <Label>Total Amount</Label>
                                <p className="text-sm font-medium">{formatCurrency(transaction.total_amount)}</p>
                            </div>
                            <div>
                                <Label>Status</Label>
                                <p className="text-sm capitalize">{transaction.status}</p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Edit Form */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Payment Details</CardTitle>
                            <CardDescription>
                                Update payment method and disbursement information
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div>
                                    <Label htmlFor="payment_method">Payment Method</Label>
                                    <Select
                                        value={data.payment_method}
                                        onValueChange={(value) => setData('payment_method', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select payment method" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {paymentMethods && paymentMethods.length > 0 ? (
                                                paymentMethods.map((method) => (
                                                    <SelectItem key={method.id} value={method.id}>
                                                        {method.name}
                                                    </SelectItem>
                                                ))
                                            ) : (
                                                <SelectItem value="" disabled>
                                                    No payment methods available
                                                </SelectItem>
                                            )}
                                        </SelectContent>
                                    </Select>
                                    {errors.payment_method && (
                                        <p className="text-sm text-destructive mt-1">{errors.payment_method}</p>
                                    )}
                                </div>

                                {getPaymentMethodFields()}

                                <div>
                                    <Label htmlFor="disbursement_transaction_id">Transaction Reference</Label>
                                    <Input
                                        id="disbursement_transaction_id"
                                        placeholder="Enter transaction reference (optional)"
                                        value={data.disbursement_transaction_id}
                                        onChange={(e) => setData('disbursement_transaction_id', e.target.value)}
                                    />
                                    {errors.disbursement_transaction_id && (
                                        <p className="text-sm text-destructive mt-1">{errors.disbursement_transaction_id}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="transaction_cost">Transaction Cost</Label>
                                    <Input
                                        id="transaction_cost"
                                        type="number"
                                        step="0.01"
                                        placeholder="Enter transaction fees (optional)"
                                        value={data.transaction_cost}
                                        onChange={(e) => setData('transaction_cost', e.target.value)}
                                    />
                                    {errors.transaction_cost && (
                                        <p className="text-sm text-destructive mt-1">{errors.transaction_cost}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="description">Description</Label>
                                    <Textarea
                                        id="description"
                                        placeholder="Enter additional description (optional)"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        rows={3}
                                    />
                                    {errors.description && (
                                        <p className="text-sm text-destructive mt-1">{errors.description}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="comments">Update Comments</Label>
                                    <Textarea
                                        id="comments"
                                        placeholder="Enter comments about this update (optional)"
                                        value={data.comments}
                                        onChange={(e) => setData('comments', e.target.value)}
                                        rows={2}
                                    />
                                    {errors.comments && (
                                        <p className="text-sm text-destructive mt-1">{errors.comments}</p>
                                    )}
                                </div>

                                <div className="flex gap-2 pt-4">
                                    <Button type="submit" disabled={processing}>
                                        <Save className="mr-2 h-4 w-4" />
                                        {processing ? 'Saving...' : 'Save Changes'}
                                    </Button>
                                    <Button type="button" variant="destructive" asChild>
                                        <Link href={`/transactions/${transaction.id}`}>
                                            <X className="mr-2 h-4 w-4" />
                                            Cancel
                                        </Link>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
