import { Navbar } from '@/components/layout/navbar';
import Footer from '@/components/layout/footer';
import DocumentViewer from '@/components/DocumentViewer';

export default function TermsOfService() {
  return (
    <>
      <div className="min-h-screen flex flex-col bg-gradient-to-b from-white/80 via-blue-100/60 to-gray-50 dark:from-gray-900 dark:via-slate-900/80 dark:to-gray-800">
        <Navbar />
        <main className="flex-1 flex flex-col justify-center items-center w-full px-2 pt-32 pb-12">
          <section className="w-full max-w-7xl mx-auto rounded-3xl bg-white/90 dark:bg-slate-900/90 shadow-2xl ring-1 ring-white/40 dark:ring-slate-700/40 backdrop-blur-2xl p-4 md:p-12 mt-0 mb-4 relative z-10" style={{ margin: '0.5rem', borderWidth: 0 }}>
            <div className="prose prose-lg md:prose-xl max-w-none text-gray-900 dark:text-white break-words whitespace-pre-line">
              <DocumentViewer filePath="/terms-of-service.md" />
            </div>
          </section>
        </main>
      </div>
      <Footer />
    </>
  );
}
