import { Navbar } from '@/components/layout/navbar';
import Footer from '@/components/layout/footer';
import { useState } from 'react';
import { Shield, Lock, Plug } from 'lucide-react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: 'easeOut' } },
};
const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};
const supportFeatures = [
  {
    icon: Shield,
    title: 'Fraud and Protection',
    description: 'We are committed to ensuring the security and integrity of our website and protecting our users. Our platform uses advanced monitoring, verification systems, and compliance with Kenyan financial regulations to prevent fraudulent activities and safeguard your transactions and data.',
  },
  {
    icon: Lock,
    title: 'Privacy and Security',
    description: 'Protecting your privacy and ensuring the security of your personal information is of utmost importance to us. We implement bank-grade security measures, including end-to-end encryption, secure authentication, and regular security audits to keep your data safe and confidential.',
  },
  {
    icon: Plug,
    title: 'Managing my account',
    description: 'We are here to provide you with the information and assistance you need to effectively manage your account. Update your profile, customize your settings, manage payment methods, and access support to ensure your experience is seamless and secure.',
  },
];

export default function Support() {
  const [search, setSearch] = useState('');
  const [category, setCategory] = useState('General');
  const [openSection, setOpenSection] = useState('getting-started');
  const [selectedSubSection, setSelectedSubSection] = useState('');

  // Restore search functionality with fuzzy search
  const handleFindQuery = () => {
    if (!search) return;
    const mainContent = document.getElementById('support-main-content');
    if (!mainContent) return;
    const walker = document.createTreeWalker(mainContent, NodeFilter.SHOW_TEXT, null);
    let node;
    const searchTerm = search.toLowerCase();

    function fuzzyMatch(text: string, pattern: string) {
      let t = 0, p = 0;
      text = text.toLowerCase();
      pattern = pattern.toLowerCase();
      while (t < text.length && p < pattern.length) {
        if (text[t] === pattern[p]) p++;
        t++;
      }
      return p === pattern.length || text.includes(pattern);
    }
    while ((node = walker.nextNode())) {
      if (node.textContent && fuzzyMatch(node.textContent, searchTerm)) {
        const parent = node.parentElement;
        if (parent) {
          parent.scrollIntoView({ behavior: 'smooth', block: 'center' });
          parent.classList.add('bg-primary/10');
          setTimeout(() => parent.classList.remove('bg-primary/10'), 1200);
        }
        break;
      }
    }
  };

  return (
    <>
      <div className="min-h-screen flex flex-col bg-gradient-to-b from-white/80 via-blue-100/60 to-gray-50 dark:from-gray-900 dark:via-slate-900/80 dark:to-gray-800">
        <Navbar />
        <main className="flex-1 w-full px-2 pt-32 pb-12">
          {/* Header */}
          <div className="max-w-4xl mx-auto text-center mb-10">
            <h1 className="text-4xl font-bold mb-4 text-primary">Help Centre</h1>
            <p className="text-foreground mb-8">We understand that sometimes you may encounter difficulties or have questions while using our platform, and we're here to assist you every step of the way.</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <input
                type="text"
                placeholder="Type a keyword"
                value={search}
                onChange={e => setSearch(e.target.value)}
                className="rounded-lg border border-border bg-foreground px-4 py-2 text-fbackground dark:text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary w-full sm:w-72"
              />
              <select
                value={category}
                onChange={e => setCategory(e.target.value)}
                className="rounded-lg border border-border bg-foreground px-4 py-2 text-background focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option>General</option>
                <option>Account</option>
                <option>Security</option>
              </select>
              <button
                className="rounded-lg bg-primary text-primary-foreground px-6 py-2 font-semibold shadow hover:bg-primary/90 transition-colors"
                onClick={handleFindQuery}
              >
                Find query
              </button>
            </div>
          </div>
          {/* Feature Cards */}
          <motion.section
            className="max-w-7xl mx-auto mb-16"
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-8">
              {supportFeatures.map((feature ) => {
                const IconComponent = feature.icon;
                return (
                  <motion.div
                    key={feature.title}
                    className="group hover:border-primary/60 relative transform rounded-2xl border border-white/60 bg-white/80 p-6 shadow-lg transition-all duration-300 hover:-translate-y-2 hover:shadow-xl dark:border-slate-700/40 dark:bg-slate-800/80 flex flex-col items-center text-center"
                    variants={fadeInUp}
                  >
                    <div className="bg-primary text-primary-foreground mb-5 flex h-12 w-12 items-center justify-center rounded-xl shadow-md transition-transform duration-300 group-hover:scale-110">
                      <IconComponent className="h-6 w-6" />
                    </div>
                    <h3 className="mb-3 text-lg font-semibold text-gray-900 dark:text-white">{feature.title}</h3>
                    <p className="dark:text-muted-foreground text-sm leading-relaxed text-gray-700">{feature.description}</p>
                  </motion.div>
                );
              })}
            </div>
          </motion.section>
          {/* Two-column layout with collapsible sections */}
          <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Sidebar - Categories */}
            <motion.aside
              className="md:col-span-1"
              variants={fadeInUp}
            >
              <Card className="h-full bg-foreground/80 dark:bg-background/80 p-6 shadow-md flex flex-col">
                <nav>
                  <ul className="space-y-4 text-foreground">
                    <li className="font-bold text-primary">
                      <button className="w-full text-left cursor-pointer select-none" onClick={() => {
                        if (openSection === 'getting-started') {
                          setOpenSection('');
                          setSelectedSubSection('');
                        } else {
                          setOpenSection('getting-started');
                          setSelectedSubSection('');
                        }
                      }}>
                        <span className="text-primary">{openSection === 'getting-started' ? '▼' : '▶'} Getting started</span>
                      </button>
                      {openSection === 'getting-started' && (
                        <ul className="ml-4 mt-2 space-y-2 rounded-lg p-2 text-foreground">
                          <li><button className={`font-bold w-full text-left ${selectedSubSection === 'account-creation' ? 'text-primary' : 'text-background dark:text-foreground/100 hover:text-primary'}`} onClick={() => setSelectedSubSection('account-creation')}>Account Creation</button></li>
                          <li><button className={`w-full text-left ${selectedSubSection === 'user-onboarding' ? 'text-primary' : 'text-background dark:text-foreground/100 hover:text-primary'}`} onClick={() => setSelectedSubSection('user-onboarding')}>User Onboarding</button></li>
                          <li><button className={`w-full text-left ${selectedSubSection === 'platform-navigation' ? 'text-primary' : 'text-background dark:text-foreground/100 hover:text-primary'}`} onClick={() => setSelectedSubSection('platform-navigation')}>Platform Navigation</button></li>
                        </ul>
                      )}
                    </li>
                    <li className="font-bold text-background/80">
                      <button className="w-full text-left cursor-pointer select-none" onClick={() => {
                        if (openSection === 'account-setup') {
                          setOpenSection('');
                          setSelectedSubSection('');
                        } else {
                          setOpenSection('account-setup');
                          setSelectedSubSection('');
                        }
                      }}>
                        <span className="text-primary">{openSection === 'account-setup' ? '▼' : '▶'} Account Setup and Management</span>
                      </button>
                      {openSection === 'account-setup' && (
                        <ul className="ml-4 mt-2 space-y-2 rounded-lg p-2 text-foreground">
                          <li><button className={`w-full text-left ${selectedSubSection === 'profile' ? 'text-primary' : 'text-background dark:text-foreground/100 hover:text-primary'}`} onClick={() => setSelectedSubSection('profile')}>Profile</button></li>
                          <li><button className={`w-full text-left ${selectedSubSection === 'settings' ? 'text-primary' : 'text-background dark:text-foreground/100 hover:text-primary'}`} onClick={() => setSelectedSubSection('settings')}>Settings</button></li>
                        </ul>
                      )}
                    </li>
                    <li className="font-bold text-primary">
                      <button className="w-full text-left cursor-pointer select-none" onClick={() => {
                        if (openSection === 'troubleshooting') {
                          setOpenSection('');
                          setSelectedSubSection('');
                        } else {
                          setOpenSection('troubleshooting');
                          setSelectedSubSection('');
                        }
                      }}>
                        <span className="text-primary">{openSection === 'troubleshooting' ? '▼' : '▶'} Troubleshooting and Technical Support</span>
                      </button>
                      {openSection === 'troubleshooting' && (
                        <ul className="ml-4 mt-2 space-y-2 rounded-lg p-2 text-foreground">
                          <li><button className={`w-full text-left ${selectedSubSection === 'faq' ? 'text-primary' : 'text-background dark:text-foreground/100 hover:text-primary'}`} onClick={() => setSelectedSubSection('faq')}>FAQ</button></li>
                          <li><button className={`w-full text-left ${selectedSubSection === 'contact-support' ? 'text-primary' : 'text-background dark:text-foreground/100 hover:text-primary'}`} onClick={() => setSelectedSubSection('contact-support')}>Contact Support</button></li>
                        </ul>
                      )}
                    </li>
                    <li className="font-bold text-primary">
                      <button className="w-full text-left cursor-pointer select-none" onClick={() => {
                        if (openSection === 'privacy') {
                          setOpenSection('');
                          setSelectedSubSection('');
                        } else {
                          setOpenSection('privacy');
                          setSelectedSubSection('');
                        }
                      }}>
                        <span className="text-primary">{openSection === 'privacy' ? '▼' : '▶'} Privacy and Security</span>
                      </button>
                      {openSection === 'privacy' && (
                        <ul className="ml-4 mt-2 space-y-2 rounded-lg p-2 text-foreground">
                          <li><button className={`w-full text-left ${selectedSubSection === 'privacy-policy' ? 'text-primary' : 'text-background dark:text-foreground/100 hover:text-primary'}`} onClick={() => setSelectedSubSection('privacy-policy')}>Privacy Policy</button></li>
                          <li><button className={`w-full text-left ${selectedSubSection === 'security' ? 'text-primary' : 'text-background dark:text-foreground/100 hover:text-primary'}`} onClick={() => setSelectedSubSection('security')}>Security</button></li>
                        </ul>
                      )}
                    </li>
                    <li className="font-bold text-primary">
                      <button className="w-full text-left cursor-pointer select-none" onClick={() => {
                        if (openSection === 'fraud') {
                          setOpenSection('');
                          setSelectedSubSection('');
                        } else {
                          setOpenSection('fraud');
                          setSelectedSubSection('');
                        }
                      }}>
                        <span className="text-primary">{openSection === 'fraud' ? '▼' : '▶'} Fraud and Protection</span>
                      </button>
                      {openSection === 'fraud' && (
                        <ul className="ml-4 mt-2 space-y-2 rounded-lg p-2 text-foreground">
                          <li><button className={`w-full text-left ${selectedSubSection === 'fraud-prevention' ? 'text-primary' : 'text-background dark:text-foreground/100 hover:text-primary'}`} onClick={() => setSelectedSubSection('fraud-prevention')}>Fraud Prevention</button></li>
                          <li><button className={`w-full text-left ${selectedSubSection === 'protection' ? 'text-primary' : 'text-background dark:text-foreground/100 hover:text-primary'}`} onClick={() => setSelectedSubSection('protection')}>Protection</button></li>
                        </ul>
                      )}
                    </li>
                  </ul>
                </nav>
              </Card>
            </motion.aside>
            {/* Main Content */}
            <motion.section
              id="support-main-content"
              className="md:col-span-3"
              variants={fadeInUp}
            >
              {/* Section summaries above the Card */}
              {openSection === 'getting-started' && !selectedSubSection && (
                <>
                  <h2 className="text-2xl font-bold mb-4 text-primary">Getting Started</h2>
                  <p className="mb-4 text-background/80 dark:text-foreground/100">Welcome to Sippar! This section will help you get started with account creation, onboarding, and platform navigation so you can make the most of our services.</p>
                </>
              )}
              {openSection === 'account-setup' && !selectedSubSection && (
                <>
                  <h2 className="text-2xl font-bold mb-4 text-primary">Account Setup and Management</h2>
                  <p className="mb-4 text-background/80 dark:text-foreground/100">Account Setup and Management provides you with the tools to personalize your Sippar experience and keep your information up to date. Here, you can update your profile with your latest contact details, set your preferred language and time zone, and manage your organization and team memberships. The settings area allows you to configure notification preferences, connect payment methods, and enhance your account security with two-factor authentication. You can also manage integrations, set up approval workflows, and ensure your account complies with privacy regulations. Keeping your account well-managed ensures seamless collaboration and secure access to all platform features.</p>
                </>
              )}
              {openSection === 'troubleshooting' && !selectedSubSection && (
                <>
                  <h2 className="text-2xl font-bold mb-4 text-primary">Troubleshooting and Technical Support</h2>
                  <p className="mb-4 text-background/80 dark:text-foreground/100">Troubleshooting and Technical Support is your go-to resource for resolving issues and getting the most out of Sippar. This section includes a comprehensive FAQ, troubleshooting tips for common problems, and step-by-step guides for advanced features. If you encounter technical difficulties, you can contact our support team for personalized assistance. Our support resources are designed to help you quickly resolve login issues, payment failures, and other challenges, so you can focus on managing your organization's finances efficiently.</p>
                </>
              )}
              {openSection === 'privacy' && !selectedSubSection && (
                <>
                  <h2 className="text-2xl font-bold mb-4 text-primary">Privacy and Security</h2>
                  <p className="mb-4 text-background/80 dark:text-foreground/100">Privacy and Security are at the core of Sippar's commitment to protecting your data. Learn how we collect, use, and safeguard your personal information in compliance with privacy laws. This section covers your rights regarding data access and deletion, our use of cookies and tracking technologies, and our robust security measures, including encryption and two-factor authentication. Stay informed about best practices for keeping your account secure and how to contact our Data Protection Officer for privacy-related concerns.</p>
                </>
              )}
              {openSection === 'fraud' && !selectedSubSection && (
                <>
                  <h2 className="text-2xl font-bold mb-4 text-primary">Fraud and Protection</h2>
                  <p className="mb-4 text-background/80 dark:text-foreground/100">Fraud and Protection outlines the advanced measures Sippar takes to keep your account and transactions safe. Our platform uses real-time monitoring, verification systems, and compliance with Kenyan financial regulations to detect and prevent fraudulent activities. Learn how to recognize phishing attempts, report suspicious activity, and understand our response procedures. We also provide educational resources and insurance policies to ensure your peace of mind while using Sippar for your organization's financial management.</p>
                </>
              )}
              {/* Only render the Card if a sub-section is selected */}
              {selectedSubSection && (
                <Card className="p-8 shadow-md bg-foreground dark:bg-background/80 text-background/80 dark:text-foreground/100 rounded-2xl">
                  {openSection === 'getting-started' && selectedSubSection === 'account-creation' && (
                    <>
                      <h2 id="account-creation" className="text-2xl font-bold mb-4 text-primary">Account Creation</h2>
                      <ul className="list-disc ml-6 text-background dark:text-foreground/100">
                        <li>Visit our website and click on the "Sign Up" or "Create Account" button to begin your journey with Sippar.</li>
                        <li>Fill in the required information, such as your name, email address, and password, to set up your secure account.</li>
                        <li>Follow the verification process, which may include confirming your email or phone number, to ensure your account's security and authenticity.</li>
                      </ul>
                    </>
                  )}
                  {openSection === 'getting-started' && selectedSubSection === 'user-onboarding' && (
                    <>
                      <h2 id="user-onboarding" className="text-2xl font-bold mb-4 text-primary">User Onboarding</h2>
                      <ul className="list-disc ml-6 text-background dark:text-foreground/100 space-y-2">
                        <li>After creating your account, you will be welcomed with a guided onboarding process designed to help you get familiar with Sippar's interface and features.</li>
                        <li>The onboarding includes a step-by-step walkthrough of the dashboard, navigation menus, and key actions such as submitting expense requests, viewing approvals, and accessing reports.</li>
                        <li>You will be introduced to the notification system, which keeps you updated on approvals, rejections, and important platform announcements.</li>
                        <li>Learn how to personalize your experience by updating your profile, setting notification preferences, and connecting your account to relevant payment methods.</li>
                        <li>Access the Help Centre at any time for detailed guides, FAQs, and support resources to answer your questions as you explore the platform.</li>
                        <li>Engage with interactive tips and tooltips that appear throughout the platform, providing context-sensitive help and best practices for using Sippar efficiently.</li>
                        <li>Discover how to join your organization or team within Sippar, collaborate with colleagues, and manage permissions for different roles.</li>
                        <li>Understand the importance of security and privacy settings, and how to enable two-factor authentication for enhanced account protection.</li>
                        <li>Complete the onboarding checklist to ensure you have set up all essential features and are ready to start managing your organization's finances with confidence.</li>
                      </ul>
                    </>
                  )}
                  {openSection === 'getting-started' && selectedSubSection === 'platform-navigation' && (
                    <>
                      <h2 id="platform-navigation" className="text-2xl font-bold mb-4 text-primary">Platform Navigation</h2>
                      <ul className="list-disc ml-6 text-background dark:text-foreground/100 space-y-2">
                        <li>Explore the main dashboard, which provides a summary of your recent activity, pending approvals, and financial status at a glance.</li>
                        <li>Use the sidebar menu to quickly access key sections such as Expenses, Approvals, Reports, Billing, and Settings.</li>
                        <li>Each section is organized for intuitive access, with clear labels and icons to help you find what you need without hassle.</li>
                        <li>Utilize the search bar at the top of the page to locate specific transactions, users, or documents instantly.</li>
                        <li>Take advantage of filters and sorting options within tables and lists to customize your view and focus on relevant data.</li>
                        <li>Access contextual help and tooltips throughout the platform for guidance on using advanced features and understanding financial terms.</li>
                        <li>Switch between light and dark mode for a comfortable viewing experience in any environment.</li>
                        <li>Navigate seamlessly between desktop and mobile devices, as Sippar is fully responsive and optimized for all screen sizes.</li>
                      </ul>
                    </>
                  )}
                  {openSection === 'account-setup' && selectedSubSection === 'profile' && (
                    <>
                      <h2 id="profile" className="text-2xl font-bold mb-4 text-primary">Profile</h2>
                      <ul className="list-disc ml-6 text-background dark:text-foreground/100 space-y-2">
                        <li>Complete your profile by adding a profile picture, your full name, and a brief bio to help colleagues recognize you within the organization.</li>
                        <li>Update your contact information, including email and phone number, to ensure you receive important notifications and account alerts.</li>
                        <li>Set your preferred language and time zone for a personalized experience across the platform.</li>
                        <li>Review your organization and team memberships, and request to join additional teams if needed.</li>
                        <li>Manage your account security by enabling two-factor authentication and reviewing recent login activity.</li>
                        <li>Keep your profile information current to facilitate smooth communication and collaboration with your team.</li>
                      </ul>
                    </>
                  )}
                  {openSection === 'account-setup' && selectedSubSection === 'settings' && (
                    <>
                      <h2 id="settings" className="text-2xl font-bold mb-4 text-primary">Settings</h2>
                      <ul className="list-disc ml-6 text-background dark:text-foreground/100 space-y-2">
                        <li>Access your account settings to customize notification preferences, such as email alerts for approvals, rejections, and system updates.</li>
                        <li>Configure privacy settings to control who can view your profile and activity within your organization.</li>
                        <li>Connect and manage payment methods, including M-PESA, bank accounts, and credit cards, for seamless transactions.</li>
                        <li>Change your password regularly and enable two-factor authentication for enhanced security.</li>
                        <li>Review and manage connected third-party integrations, such as accounting software or HR systems.</li>
                        <li>Set up approval workflows and permissions for different roles within your organization.</li>
                        <li>Export your data or request account deletion in compliance with privacy regulations.</li>
                      </ul>
                    </>
                  )}
                  {openSection === 'troubleshooting' && selectedSubSection === 'faq' && (
                    <>
                      <h2 id="faq" className="text-2xl font-bold mb-4 text-primary">FAQ</h2>
                      <ul className="list-disc ml-6 text-background dark:text-foreground/100 space-y-2">
                        <li>Browse frequently asked questions to find quick answers to common issues and platform features.</li>
                        <li>Learn how to reset your password, recover your account, and update your contact information.</li>
                        <li>Get troubleshooting tips for login problems, payment failures, and document uploads.</li>
                        <li>Understand how to use advanced features such as bulk expense uploads, custom reports, and approval chains.</li>
                        <li>Find links to detailed guides, video tutorials, and support resources for further assistance.</li>
                      </ul>
                    </>
                  )}
                  {openSection === 'troubleshooting' && selectedSubSection === 'contact-support' && (
                    <>
                      <h2 id="contact-support" className="text-2xl font-bold mb-4 text-primary">Contact Support</h2>
                      <ul className="list-disc ml-6 text-background dark:text-foreground/100 space-y-2">
                        <li>If you encounter an issue that cannot be resolved through the FAQ or help guides, contact our support team for personalized assistance.</li>
                        <li>Reach out via email at <a href="mailto:<EMAIL>" className="text-primary underline"><EMAIL></a> or use the contact form on our website.</li>
                        <li>Our support team is available during business hours and strives to respond to all inquiries within 24 hours.</li>
                        <li>Provide as much detail as possible about your issue, including screenshots or error messages, to help us assist you efficiently.</li>
                        <li>Track the status of your support requests and view previous tickets in your account dashboard.</li>
                      </ul>
                    </>
                  )}
                  {openSection === 'privacy' && selectedSubSection === 'privacy-policy' && (
                    <>
                      <h2 id="privacy-policy" className="text-2xl font-bold mb-4 text-primary">Privacy Policy</h2>
                      <ul className="list-disc ml-6 text-background dark:text-foreground/100 space-y-2">
                        <li>Read our comprehensive Privacy Policy to understand how we collect, use, and protect your personal information.</li>
                        <li>Learn about your rights regarding data access, correction, and deletion under applicable privacy laws.</li>
                        <li>Find out how we use cookies and similar technologies to enhance your experience on Sippar.</li>
                        <li>Review our data retention practices and how we safeguard your information from unauthorized access.</li>
                        <li>Contact our Data Protection Officer for privacy-related inquiries or concerns.</li>
                      </ul>
                    </>
                  )}
                  {openSection === 'privacy' && selectedSubSection === 'security' && (
                    <>
                      <h2 id="security" className="text-2xl font-bold mb-4 text-primary">Security</h2>
                      <ul className="list-disc ml-6 text-background dark:text-foreground/100 space-y-2">
                        <li>We use bank-grade security measures, including end-to-end encryption, secure authentication, and regular security audits.</li>
                        <li>Enable two-factor authentication to add an extra layer of protection to your account.</li>
                        <li>Review your recent login activity and report any suspicious access immediately.</li>
                        <li>Learn about our incident response procedures and how we handle security breaches.</li>
                        <li>Stay informed about best practices for keeping your account and data secure.</li>
                      </ul>
                    </>
                  )}
                  {openSection === 'fraud' && selectedSubSection === 'fraud-prevention' && (
                    <>
                      <h2 id="fraud-prevention" className="text-2xl font-bold mb-4 text-primary">Fraud Prevention</h2>
                      <ul className="list-disc ml-6 text-background dark:text-foreground/100 space-y-2">
                        <li>Our platform uses advanced monitoring and verification systems to detect and prevent fraudulent activities in real time.</li>
                        <li>We comply with Kenyan financial regulations and industry standards to ensure the integrity of your transactions.</li>
                        <li>Learn how to recognize phishing attempts and report suspicious activity to our support team.</li>
                        <li>Understand the steps we take to investigate and resolve fraud cases quickly and transparently.</li>
                        <li>Access educational resources on safe online practices and fraud prevention tips.</li>
                      </ul>
                    </>
                  )}
                  {openSection === 'fraud' && selectedSubSection === 'protection' && (
                    <>
                      <h2 id="protection" className="text-2xl font-bold mb-4 text-primary">Protection</h2>
                      <ul className="list-disc ml-6 text-background dark:text-foreground/100 space-y-2">
                        <li>Your account and transactions are protected by multiple layers of security, including encryption and compliance with regulatory requirements.</li>
                        <li>We regularly update our security protocols to address emerging threats and vulnerabilities.</li>
                        <li>Learn about our insurance policies and guarantees for user protection in case of unauthorized transactions.</li>
                        <li>Contact our support team for guidance on protecting your account and reporting any concerns.</li>
                        <li>Review our user agreement for detailed information on your rights and responsibilities.</li>
                      </ul>
                    </>
                  )}
                </Card>
              )}
            </motion.section>
          </div>
          {/* FAQ Section */}
          <section className="max-w-7xl mx-auto mt-16 mb-8 shadow-md">
            <Card className="rounded-2xl p-8 shadow-md bg-foreground dark:bg-background/80 text-background/80 dark:text-foreground/100">
              <h2 className="text-2xl font-bold mb-6 text-primary">Frequently Asked Questions (FAQs)</h2>
              <div className="space-y-6">
                <details className="group">
                  <summary className="cursor-pointer select-none font-semibold text-background dark:text-foreground group-open:text-primary">How do I reset my password?</summary>
                  <p className="mt-2 text-background dark:text-foreground/70">Go to your account settings, select 'Change Password', and follow the instructions to reset your password.</p>
                </details>
                <details className="group">
                  <summary className="cursor-pointer select-none font-semibold text-background dark:text-foreground group-open:text-primary">How can I contact support?</summary>
                  <p className="mt-2 text-background dark:text-foreground/70">You can contact our support team via the 'Contact Support' link in the sidebar or <NAME_EMAIL>.</p>
                </details>
                <details className="group">
                  <summary className="cursor-pointer select-none font-semibold text-background dark:text-foreground group-open:text-primary">Where can I find billing information?</summary>
                  <p className="mt-2 text-background dark:text-foreground/70">Billing information is available under the 'Billing and Payment' section in your account dashboard.</p>
                </details>
              </div>
            </Card>
          </section>
        </main>
      </div>
      <Footer />
    </>
  );
}
