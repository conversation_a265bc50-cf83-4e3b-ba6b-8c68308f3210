import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Head, Link, useForm } from '@inertiajs/react';
import { Save, X } from 'lucide-react';
import React from 'react';
import AppLayout from '@/layouts/app-layout';

interface Transaction {
    id: number;
    requisition_id: number;
    status: string;
    total_amount: number;
    requisition: {
        requisition_number: string;
        purpose: string;
    };
}

interface EditProps {
    transaction: Transaction;
}

const Edit: React.FC<EditProps> = ({ transaction }) => {
    type BreadcrumbItem = {
        title: string;
        href: string;
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Disbursements',
            href: '/disbursement',
        },
        {
            title: `Disbursement #${transaction.id}`,
            href: `/disbursement/${transaction.id}`,
        },
        {
            title: 'Edit Account Details',
            href: `/disbursement/${transaction.id}/edit`,
        },
    ];

    const { data, setData, post, processing, errors } = useForm({
        account_name: '',
        account_number: '',
        bank_name: '',
        bank_branch: '',
        swift_code: '',
        additional_details: '',
    });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(`/disbursement/${transaction.id}?_method=PUT`);
  };
  
  return (
        <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Provide Account Details - Disbursement #${transaction.id}`} />
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle>Provide M-PesaAccount Details</CardTitle>
            <CardDescription>
              For Requisition: {transaction.requisition.requisition_number} - {transaction.requisition.purpose}
            </CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="account_name">M-Pesa Account Name <span className="text-red-500">*</span></Label>
                    <Input
                      id="account_name"
                      value={data.account_name}
                      onChange={(e) => setData('account_name', e.target.value)}
                      required
                    />
                    {errors.account_name && (
                      <p className="text-red-500 text-sm mt-1">{errors.account_name}</p>
                    )}
                  </div>

                                    <div>
                                        <Label htmlFor="account_number">
                                            M-Pesa Account Number <span className="text-red-500">*</span>
                                        </Label>
                                        <Input
                                            id="account_number"
                                            value={data.account_number}
                                            onChange={(e) => setData('account_number', e.target.value)}
                                            required
                                        />
                                        {errors.account_number && <p className="mt-1 text-sm text-red-500">{errors.account_number}</p>}
                                    </div>

                                    <div>
                                        <Label htmlFor="additional_details">Any Additional Details</Label>
                                        <Textarea
                                            id="additional_details"
                                            value={data.additional_details}
                                            onChange={(e) => setData('additional_details', e.target.value)}
                                            rows={3}
                                        />
                                        {errors.additional_details && <p className="mt-1 text-sm text-red-500">{errors.additional_details}</p>}
                                    </div>
                                </div>
                            </div>

                            <div className="rounded-md border border-blue-200 bg-blue-50 p-4">
                                <h3 className="mb-2 font-medium text-blue-800">Important Note</h3>
                                <p className="text-sm text-blue-700">
                                    Please ensure all account details are accurate. These details will be used to process the disbursement.
                                </p>
                            </div>
                        </CardContent>
                        <CardFooter className="bg-muted/50 flex justify-between border-t p-4">
                            <Button variant="destructive" type="button" asChild>
                                <Link href={`/disbursement/${transaction.id}`}>
                                    <X className="mr-2 h-4 w-4" />
                                    Cancel
                                </Link>
                            </Button>
                            <Button type="submit" disabled={processing}>
                                <Save className="mr-2 h-4 w-4" />
                                Save Account Details
                            </Button>
                        </CardFooter>
                    </form>
                </Card>
            </div>
        </AppLayout>
    );
};

export default Edit;
