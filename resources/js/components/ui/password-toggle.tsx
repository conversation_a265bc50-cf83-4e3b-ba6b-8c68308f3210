import { Eye, EyeOff } from 'lucide-react';

type PasswordToggleProps = {
    show: boolean;
    toggle: () => void;
}

export default function PasswordToggle({ show }: PasswordToggleProps) {
    return (
        <div
            className="absolute top-1/2 right-3 -translate-y-1/2 text-muted-foreground hover:text-foreground"
        >
            {show ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
        </div>
    )
}
