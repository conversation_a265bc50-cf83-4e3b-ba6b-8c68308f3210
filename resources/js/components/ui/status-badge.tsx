import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  StatusBadgeProps,
  UniversalStatus,
  getStatusConfig,
  getStatusStyles,
  getStatusAccessibleLabel,
  getStatusIcon,
  getStatusLabel
} from '@/types/status-badge';

/**
 * Universal Status Badge Component
 * 
 * A unified, reusable component for displaying status across all interfaces.
 * Supports store requisitions, regular requisitions, and inventory items
 * with consistent styling, proper accessibility, and responsive design.
 * 
 * Features:
 * - Universal status support (store requisitions, requisitions, inventory)
 * - Consistent color system following industry standards
 * - Proper accessibility with ARIA labels and screen reader support
 * - Optional icons for better visual recognition
 * - Multiple sizes (sm, md, lg)
 * - Light/dark theme support
 * - No hardcoded colors - uses CSS variables
 */
export function StatusBadge({
  status,
  showIcon = true,
  size = 'md',
  className,
  'aria-label': ariaLabel,
  ...props
}: StatusBadgeProps) {
  // Get status configuration and styling
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const config = getStatusConfig(status);
  const styles = getStatusStyles(status);
  const label = getStatusLabel(status);
  const icon = getStatusIcon(status);
  const accessibleLabel = ariaLabel || getStatusAccessibleLabel(status);

  // Size-based styling
  const sizeClasses = {
    sm: 'text-xs px-2 py-0.5 gap-1',
    md: 'text-xs px-2 py-1 gap-1.5',
    lg: 'text-sm px-3 py-1.5 gap-2'
  };

  const iconSizes = {
    sm: 'text-xs',
    md: 'text-xs',
    lg: 'text-sm'
  };

  return (
    <Badge
      style={styles}
      className={cn(
        'font-medium rounded-full whitespace-nowrap',
        'inline-flex items-center justify-center',
        'border-0 transition-all duration-200',
        'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/50',
        sizeClasses[size],
        className
      )}
      aria-label={accessibleLabel}
      role="status"
      {...props}
    >
      {showIcon && (
        <span 
          className={cn('flex-shrink-0', iconSizes[size])} 
          aria-hidden="true"
        >
          {icon}
        </span>
      )}
      <span className="flex-shrink-0">{label}</span>
    </Badge>
  );
}

/**
 * Store Requisition Status Badge
 * 
 * Specialized component for store requisition statuses with enhanced features
 */
interface StoreRequisitionStatusBadgeProps extends Omit<StatusBadgeProps, 'status'> {
  status: import('@/types/status-badge').StoreRequisitionStatus;
  showProgress?: boolean;
  fulfillmentPercentage?: number;
}

export function StoreRequisitionStatusBadge({
  status,
  showProgress = false,
  fulfillmentPercentage,
  ...props
}: StoreRequisitionStatusBadgeProps) {
  return (
    <div className="flex items-center gap-2">
      <StatusBadge status={status} {...props} />
      {showProgress && fulfillmentPercentage !== undefined && (
        <span className="text-xs text-muted-foreground">
          ({fulfillmentPercentage}%)
        </span>
      )}
    </div>
  );
}

/**
 * Inventory Status Badge
 * 
 * Specialized component for inventory statuses with stock level indicators
 */
interface InventoryStatusBadgeProps extends Omit<StatusBadgeProps, 'status'> {
  status: import('@/types/status-badge').InventoryStatus;
  currentStock?: number;
  reorderLevel?: number;
  showStockLevel?: boolean;
}

export function InventoryStatusBadge({
  status,
  currentStock,
  reorderLevel,
  showStockLevel = false,
  ...props
}: InventoryStatusBadgeProps) {
  return (
    <div className="flex items-center gap-2">
      <StatusBadge status={status} {...props} />
      {showStockLevel && currentStock !== undefined && (
        <span className="text-xs text-muted-foreground">
          {currentStock} units
          {reorderLevel !== undefined && status === 'low-stock' && (
            <span className="text-warning-foreground">
              {' '}(min: {reorderLevel})
            </span>
          )}
        </span>
      )}
    </div>
  );
}

/**
 * Regular Requisition Status Badge
 * 
 * Specialized component for regular requisition statuses
 */
interface RequisitionStatusBadgeProps extends Omit<StatusBadgeProps, 'status'> {
  status: import('@/types/status-badge').RequisitionStatus;
  amount?: number;
  showAmount?: boolean;
}

export function RequisitionStatusBadge({
  status,
  amount,
  showAmount = false,
  ...props
}: RequisitionStatusBadgeProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(value);
  };

  return (
    <div className="flex items-center gap-2">
      <StatusBadge status={status} {...props} />
      {showAmount && amount !== undefined && (
        <span className="text-xs text-muted-foreground">
          {formatCurrency(amount)}
        </span>
      )}
    </div>
  );
}

/**
 * Status Badge Group
 * 
 * Component for displaying multiple related statuses
 */
interface StatusBadgeGroupProps {
  statuses: UniversalStatus[];
  showIcons?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  orientation?: 'horizontal' | 'vertical';
}

export function StatusBadgeGroup({
  statuses,
  showIcons = true,
  size = 'md',
  className,
  orientation = 'horizontal'
}: StatusBadgeGroupProps) {
  const containerClasses = orientation === 'horizontal' 
    ? 'flex flex-wrap items-center gap-2'
    : 'flex flex-col items-start gap-1';

  return (
    <div className={cn(containerClasses, className)}>
      {statuses.map((status, index) => (
        <StatusBadge
          key={`${status}-${index}`}
          status={status}
          showIcon={showIcons}
          size={size}
        />
      ))}
    </div>
  );
}

/**
 * Interactive Status Badge
 * 
 * Status badge with click handler for interactive use cases
 */
interface InteractiveStatusBadgeProps extends StatusBadgeProps {
  onClick?: (status: UniversalStatus) => void;
  disabled?: boolean;
}

export function InteractiveStatusBadge({
  status,
  onClick,
  disabled = false,
  className,
  ...props
}: InteractiveStatusBadgeProps) {
  const handleClick = () => {
    if (!disabled && onClick) {
      onClick(status);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if ((event.key === 'Enter' || event.key === ' ') && !disabled && onClick) {
      event.preventDefault();
      onClick(status);
    }
  };

  return (
    <StatusBadge
      status={status}
      className={cn(
        onClick && !disabled && 'cursor-pointer hover:opacity-80 focus:ring-2',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={onClick && !disabled ? 0 : undefined}
      {...props}
    />
  );
}

// Export the main component as default
export default StatusBadge;
