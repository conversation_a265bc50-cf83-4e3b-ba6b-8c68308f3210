// badge.tsx

import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-md border px-2.5 py-0.5 text-xs font-semibold w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none transition-colors",
  {
    variants: {
      variant: {
        default:
          "bg-slate-100 text-slate-900 border-transparent dark:bg-slate-800 dark:text-slate-50",
        pending:
          "bg-amber-100 text-amber-900 border-transparent dark:bg-amber-900 dark:text-amber-50",
        approved:
          "bg-emerald-100 text-emerald-900 border-transparent dark:bg-emerald-900 dark:text-emerald-50",
        rejected:
          "bg-red-100 text-red-900 border-transparent dark:bg-red-900 dark:text-red-50",
        revision:
          "bg-sky-100 text-sky-900 border-transparent dark:bg-sky-900 dark:text-sky-50",
        secondary:
          "bg-secondary text-secondary-foreground border-transparent hover:bg-secondary/80",
        destructive:
          "bg-destructive text-destructive-foreground border-transparent hover:bg-destructive/90",
        outline:
          "border-2 border-primary bg-background text-primary hover:bg-primary/10",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

function Badge({
  className,
  variant,
  asChild = false,
  ...props
}: React.ComponentProps<"span"> &
  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : "span";

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ variant }), className)}
      {...props}
    />
  );
}

export { Badge, badgeVariants };
