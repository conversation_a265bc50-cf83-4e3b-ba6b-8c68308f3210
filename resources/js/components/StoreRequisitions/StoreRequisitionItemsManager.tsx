import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import StoreRequisitionItemForm from './StoreRequisitionItemForm';
import { StoreRequisitionItemFormData, InventoryItem } from '@/types/store-requisitions';

interface StoreRequisitionItemsManagerProps {
    items: StoreRequisitionItemFormData[];
    inventoryItems: InventoryItem[];
    errors: Record<string, string>;
    onItemChange: (index: number, field: keyof StoreRequisitionItemFormData, value: string) => void;
    onAddItem: () => void;
    onRemoveItem: (index: number) => void;
}

export default function StoreRequisitionItemsManager({
    items,
    inventoryItems,
    errors,
    onItemChange,
    onAddItem,
    onRemoveItem
}: StoreRequisitionItemsManagerProps) {
    const canRemoveItems = items.length > 1;

    return (
        <div className="space-y-4">
            {items.map((item, index) => (
                <StoreRequisitionItemForm
                    key={index}
                    item={item}
                    index={index}
                    inventoryItems={inventoryItems}
                    allItems={items}
                    errors={errors}
                    onItemChange={onItemChange}
                    onRemoveItem={onRemoveItem}
                    canRemove={canRemoveItems}
                />
            ))}

            <div className="pt-2">
                <Button
                    type="button"
                    onClick={onAddItem}
                    variant="outline"
                    className="w-full h-11 sm:h-12 text-sm sm:text-base hover:bg-muted hover:text-foreground transition-colors"
                    size="lg"
                >
                    <Plus className="mr-2 h-4 w-4" />
                    Add Another Item
                </Button>
            </div>
        </div>
    );
}
