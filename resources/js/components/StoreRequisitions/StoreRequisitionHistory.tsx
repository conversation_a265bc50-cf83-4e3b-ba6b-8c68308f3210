import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { StoreRequisitionHistoryEntry } from '@/types/store-requisitions';
import {
  formatStoreRequisitionHistoryAction,
  getStoreRequisitionHistoryIconName,
  getStoreRequisitionHistoryBadgeVariant,
  formatDateTime
} from '@/utils/store-requisitions';
import {
  PlusCircle,
  Edit,
  Send,
  CheckCircle,
  XCircle,
  RotateCcw,
  Package,
  Clock
} from 'lucide-react';

// Helper function to format change values for display
const formatChangeValue = (key: string, value: unknown): string => {
  if (value === null || value === undefined) {
    return 'Not set';
  }

  // Handle different types of values
  if (typeof value === 'object') {
    // Handle arrays (like items)
    if (Array.isArray(value)) {
      if (key === 'items') {
        return `${value.length} item${value.length !== 1 ? 's' : ''}`;
      }
      return `${value.length} entries`;
    }

    // Handle other objects
    if (value && typeof value === 'object') {
      // Try to extract meaningful information from objects
      if ('name' in value && typeof value.name === 'string') {
        return value.name;
      }
      if ('id' in value && typeof value.id !== 'undefined') {
        return `ID: ${value.id}`;
      }
      // For complex objects, show a summary
      return 'Updated';
    }
  }

  // Handle primitive values
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }

  if (typeof value === 'number') {
    return value.toString();
  }

  // Handle strings
  const stringValue = String(value);

  // Truncate very long strings
  if (stringValue.length > 100) {
    return stringValue.substring(0, 97) + '...';
  }

  return stringValue;
};

// Helper function to determine if a change should be displayed
const shouldDisplayChange = (key: string, value: unknown): boolean => {
  // Skip internal fields that aren't useful to users
  const skipFields = ['updated_at', 'created_at', 'id', 'organization_id', 'requester_user_id'];
  if (skipFields.includes(key)) {
    return false;
  }

  // Skip null/undefined values
  if (value === null || value === undefined) {
    return false;
  }

  return true;
};

// Helper function to get user-friendly field names
const getFieldDisplayName = (key: string): string => {
  const fieldNames: Record<string, string> = {
    'branch_id': 'Branch',
    'department_id': 'Department',
    'purpose': 'Purpose',
    'status': 'Status',
    'items': 'Items',
    'rejection_reason': 'Rejection Reason',
    'comments': 'Comments',
    'quantity_requested': 'Quantity Requested',
    'inventory_item_id': 'Inventory Item'
  };

  return fieldNames[key] || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

interface StoreRequisitionHistoryProps {
  histories: StoreRequisitionHistoryEntry[];
}

// Helper function to render the appropriate icon
const renderHistoryIcon = (iconName: string) => {
  const iconProps = { className: "h-3 w-3" };

  switch (iconName) {
    case 'PlusCircle':
      return <PlusCircle {...iconProps} />;
    case 'Edit':
      return <Edit {...iconProps} />;
    case 'Send':
      return <Send {...iconProps} />;
    case 'CheckCircle':
      return <CheckCircle {...iconProps} />;
    case 'XCircle':
      return <XCircle {...iconProps} />;
    case 'RotateCcw':
      return <RotateCcw {...iconProps} />;
    case 'Package':
      return <Package {...iconProps} />;
    case 'Clock':
    default:
      return <Clock {...iconProps} />;
  }
};

export function StoreRequisitionHistory({ histories }: StoreRequisitionHistoryProps) {
  if (!histories || histories.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>History</CardTitle>
          <CardDescription>
            Complete history from creation to completion
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground text-center py-8">
            No history available
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>History</CardTitle>
        <CardDescription>
          Complete history from creation to completion
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          {histories.map((entry, index) => (
            <div key={entry.id} className="relative pl-8">
              {/* Timeline line */}
              {index < histories.length - 1 && (
                <div className="bg-border absolute top-6 left-3 h-full w-px" />
              )}

              {/* Timeline dot with appropriate icon */}
              <div className="bg-background absolute top-1 left-0 flex h-6 w-6 items-center justify-center rounded-full border">
                {renderHistoryIcon(getStoreRequisitionHistoryIconName(entry.action))}
              </div>

              {/* History content */}
              <div className="flex flex-col space-y-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-foreground">
                    {entry.user.first_name} {entry.user.last_name}
                  </span>
                  <Badge 
                    variant={getStoreRequisitionHistoryBadgeVariant(entry.action)} 
                    className="text-foreground/90"
                  >
                    {formatStoreRequisitionHistoryAction(entry.action)}
                  </Badge>
                </div>
                
                <div className="text-sm text-muted-foreground">
                  {formatDateTime(entry.created_at)}
                </div>
                
                {entry.comments && (
                  <div className="mt-2 rounded-md bg-muted/50 p-3">
                    <p className="text-sm text-foreground">{entry.comments}</p>
                  </div>
                )}
                
                {entry.changes && Object.keys(entry.changes).length > 0 && (
                  <div className="mt-2 rounded-md bg-muted/30 p-3">
                    <p className="text-xs font-medium text-muted-foreground mb-2">Changes:</p>
                    <div className="space-y-1">
                      {Object.entries(entry.changes)
                        .filter(([key, value]) => shouldDisplayChange(key, value))
                        .map(([key, value]) => {
                          // Handle nested changes (like original/updated structure)
                          if (key === 'original' || key === 'updated') {
                            if (typeof value === 'object' && value !== null) {
                              return (
                                <div key={key} className="space-y-1">
                                  <p className="text-xs font-medium text-foreground capitalize">
                                    {key === 'original' ? 'From:' : 'To:'}
                                  </p>
                                  <div className="pl-2 space-y-1">
                                    {Object.entries(value as Record<string, unknown>)
                                      .filter(([subKey, subValue]) => shouldDisplayChange(subKey, subValue))
                                      .map(([subKey, subValue]) => (
                                        <div key={`${key}-${subKey}`} className="flex justify-between text-xs">
                                          <span className="text-muted-foreground">
                                            {getFieldDisplayName(subKey)}:
                                          </span>
                                          <span className="font-medium text-foreground max-w-[60%] text-right">
                                            {formatChangeValue(subKey, subValue)}
                                          </span>
                                        </div>
                                      ))}
                                  </div>
                                </div>
                              );
                            }
                          }

                          // Handle direct field changes
                          return (
                            <div key={key} className="flex justify-between text-xs">
                              <span className="text-muted-foreground">
                                {getFieldDisplayName(key)}:
                              </span>
                              <span className="font-medium text-foreground max-w-[60%] text-right">
                                {formatChangeValue(key, value)}
                              </span>
                            </div>
                          );
                        })}
                    </div>
                  </div>
                )}
           
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
