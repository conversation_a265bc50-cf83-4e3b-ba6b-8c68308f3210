import React from 'react';
import { RequisitionStatusBadge as BaseRequisitionStatusBadge } from '@/components/ui/status-badge';
import { RequisitionStatus } from '@/types/status-badge';

/**
 * Regular Requisition Interface
 * Matches the structure from the existing codebase
 */
interface Requisition {
  id: number;
  requisition_number: string;
  purpose: string;
  status: RequisitionStatus;
  total_amount: number;
  created_at: string;
  updated_at: string;
  approved_at?: string;
  rejected_at?: string;
}

/**
 * Requisition Status Badge Props
 */
interface RequisitionStatusBadgeProps {
  status: RequisitionStatus;
  showIcon?: boolean;
  showAmount?: boolean;
  amount?: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: (status: RequisitionStatus) => void;
}


export function RequisitionStatusBadge({
  status,
  showIcon = true,
  showAmount = false,
  amount,
  size = 'md',
  className,
  onClick
}: RequisitionStatusBadgeProps) {
  const handleClick = () => {
    if (onClick) {
      onClick(status);
    }
  };

  const badgeElement = (
    <BaseRequisitionStatusBadge
      status={status}
      showIcon={showIcon}
      showAmount={showAmount}
      amount={amount}
      size={size}
      className={className}
    />
  );

  if (onClick) {
    return (
      <button
        type="button"
        onClick={handleClick}
        className="cursor-pointer focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded"
      >
        {badgeElement}
      </button>
    );
  }

  return badgeElement;
}

/**
 * Requisition Status Badge with Details
 * 
 * Enhanced version that shows additional requisition information
 */
interface RequisitionStatusBadgeWithDetailsProps extends RequisitionStatusBadgeProps {
  requisition: Requisition;
  showDate?: boolean;
  showRequisitionNumber?: boolean;
}

export function RequisitionStatusBadgeWithDetails({
  requisition,
  showDate = false,
  showRequisitionNumber = false,
  ...props
}: RequisitionStatusBadgeWithDetailsProps) {

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="flex items-center gap-2">
      <RequisitionStatusBadge
        status={requisition.status}
        amount={requisition.total_amount}
        showIcon={props.showIcon}
        showAmount={props.showAmount}
        size={props.size}
        className={props.className}
        onClick={props.onClick}
      />
      
      <div className="flex flex-col text-xs text-muted-foreground">
        {showRequisitionNumber && (
          <span>#{requisition.requisition_number}</span>
        )}
        
        {showDate && (
          <span>
            {requisition.status === 'approved' && requisition.approved_at
              ? `Approved: ${formatDate(requisition.approved_at)}`
              : requisition.status === 'rejected' && requisition.rejected_at
              ? `Rejected: ${formatDate(requisition.rejected_at)}`
              : `Created: ${formatDate(requisition.created_at)}`
            }
          </span>
        )}
      </div>
    </div>
  );
}

/**
 * Requisition Status Summary
 * 
 * Component for displaying requisition status counts
 */
interface RequisitionStatusSummaryProps {
  requisitions: Requisition[];
  showCounts?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function RequisitionStatusSummary({
  requisitions,
  showCounts = true,
  size = 'md',
  className
}: RequisitionStatusSummaryProps) {
  const statusCounts = requisitions.reduce(
    (counts, requisition) => {
      counts[requisition.status]++;
      return counts;
    },
    { 'pending_approval': 0, 'approved': 0, 'rejected': 0 } as Record<RequisitionStatus, number>
  );

  const statuses: RequisitionStatus[] = ['pending_approval', 'approved', 'rejected'];

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {statuses.map((status) => (
        <div key={status} className="flex items-center gap-1">
          <BaseRequisitionStatusBadge
            status={status}
            showIcon={true}
            size={size}
          />
          {showCounts && (
            <span className="text-sm text-muted-foreground">
              {statusCounts[status]}
            </span>
          )}
        </div>
      ))}
    </div>
  );
}

/**
 * Requisition Status Filter Badges
 * 
 * Interactive badges for filtering requisitions by status
 */
interface RequisitionStatusFilterProps {
  activeFilter: string;
  onFilterChange: (filter: string) => void;
  counts?: Record<string, number>;
  className?: string;
}

export function RequisitionStatusFilter({
  activeFilter,
  onFilterChange,
  counts,
  className
}: RequisitionStatusFilterProps) {
  const filters = [
    { key: 'all', label: 'All Requisitions', status: null },
    { key: 'pending_approval', label: 'Pending Approval', status: 'pending_approval' as RequisitionStatus },
    { key: 'approved', label: 'Approved', status: 'approved' as RequisitionStatus },
    { key: 'rejected', label: 'Rejected', status: 'rejected' as RequisitionStatus }
  ];

  return (
    <div className={`flex flex-wrap items-center gap-2 ${className}`}>
      {filters.map((filter) => (
        <button
          key={filter.key}
          onClick={() => onFilterChange(filter.key)}
          className={`
            inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium
            transition-all duration-200 border
            ${activeFilter === filter.key
              ? 'bg-primary text-primary-foreground border-primary'
              : 'bg-background text-foreground border-border hover:bg-muted'
            }
          `}
          aria-pressed={activeFilter === filter.key}
        >
          {filter.status && (
            <BaseRequisitionStatusBadge
              status={filter.status}
              showIcon={true}
              size="sm"
              className="border-0"
            />
          )}
          <span>{filter.label}</span>
          {counts && counts[filter.key] !== undefined && (
            <span className="ml-1 px-1.5 py-0.5 bg-muted text-muted-foreground rounded text-xs">
              {counts[filter.key]}
            </span>
          )}
        </button>
      ))}
    </div>
  );
}


export function getStatusBadge(status: string, className?: string) {
  // Validate that the status is a valid RequisitionStatus
  if (!['pending_approval', 'approved', 'rejected'].includes(status)) {
    return (
      <BaseRequisitionStatusBadge
        status="pending_approval"
        showIcon={false}
        className={className}
      />
    );
  }

  return (
    <RequisitionStatusBadge
      status={status as RequisitionStatus}
      showIcon={true}
      className={className}
    />
  );
}


export default RequisitionStatusBadge;
