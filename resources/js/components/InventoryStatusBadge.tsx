import React from 'react';
import { InventoryStatusBadge as BaseInventoryStatusBadge } from '@/components/ui/status-badge';
import { InventoryStatus } from '@/types/status-badge';

/**
 * Inventory Item Interface
 * Matches the structure from the existing codebase
 */
interface InventoryItem {
  id: number;
  sku: string;
  name: string;
  description?: string;
  unit_of_measure: string;
  quantity_on_hand: number;
  reorder_level: number;
  current_stock?: number;
  minimum_stock?: number;
  category?: string;
}

/**
 * Inventory Status Badge Props
 */
interface InventoryStatusBadgeProps {
  item: InventoryItem;
  showIcon?: boolean;
  showStockLevel?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: (item: InventoryItem, status: InventoryStatus) => void;
}

/**
 * Get inventory status based on stock levels
 * Matches the logic from the existing InventoryIndex.tsx
 */
function getInventoryStatus(item: InventoryItem): InventoryStatus {
  const quantity = parseFloat(item.quantity_on_hand.toString());
  const reorderLevel = parseFloat(item.reorder_level.toString());

  if (quantity <= 0) {
    return 'out-of-stock';
  } else if (reorderLevel > 0 && quantity <= reorderLevel) {
    return 'low-stock';
  } else {
    return 'in-stock';
  }
}

export function InventoryStatusBadge({
  item,
  showIcon = false,
  showStockLevel = false,
  size = 'md',
  className,
  onClick
}: InventoryStatusBadgeProps) {
  const status = getInventoryStatus(item);
  
  const handleClick = () => {
    if (onClick) {
      onClick(item, status);
    }
  };

  return (
    <div onClick={onClick ? handleClick : undefined} className={onClick ? "cursor-pointer" : ""}>
      <BaseInventoryStatusBadge
        status={status}
        currentStock={item.quantity_on_hand}
        reorderLevel={item.reorder_level}
        showIcon={showIcon}
        showStockLevel={showStockLevel}
        size={size}
        className={className}
        aria-label={`${item.name} stock status: ${status}. Current stock: ${item.quantity_on_hand} ${item.unit_of_measure}`}
      />
    </div>
  );
}

/**
 * Inventory Status Badge with Details
 * 
 * Enhanced version that shows additional stock information
 */
interface InventoryStatusBadgeWithDetailsProps extends InventoryStatusBadgeProps {
  showReorderAlert?: boolean;
  showPercentage?: boolean;
}

export function InventoryStatusBadgeWithDetails({
  item,
  showReorderAlert = true,
  showPercentage = false,
  showIcon = false,
  ...props
}: InventoryStatusBadgeWithDetailsProps) {
  const status = getInventoryStatus(item);
  const stockPercentage = item.reorder_level > 0 
    ? Math.round((item.quantity_on_hand / item.reorder_level) * 100)
    : 100;

  return (
    <div className="flex items-center gap-2">
      <InventoryStatusBadge item={item} showIcon={showIcon} {...props} />
      
      {/* Stock level details */}
      <div className="flex flex-col text-xs text-muted-foreground">
        <span>
          {item.quantity_on_hand} {item.unit_of_measure}
        </span>
        
        {/* Reorder level warning */}
        {showReorderAlert && status === 'low-stock' && item.reorder_level > 0 && (
          <span className="text-warning-foreground">
            Reorder at: {item.reorder_level} {item.unit_of_measure}
          </span>
        )}
        
        {/* Stock percentage */}
        {showPercentage && item.reorder_level > 0 && (
          <span className={status === 'low-stock' ? 'text-warning-foreground' : ''}>
            {stockPercentage}% of reorder level
          </span>
        )}
      </div>
    </div>
  );
}

/**
 * Inventory Status Summary
 * 
 * Component for displaying inventory status counts
 */
interface InventoryStatusSummaryProps {
  items: InventoryItem[];
  showCounts?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function InventoryStatusSummary({
  items,
  showCounts = true,
  size = 'md',
  className
}: InventoryStatusSummaryProps) {
  const statusCounts = items.reduce(
    (counts, item) => {
      const status = getInventoryStatus(item);
      counts[status]++;
      return counts;
    },
    { 'in-stock': 0, 'low-stock': 0, 'out-of-stock': 0 } as Record<InventoryStatus, number>
  );

  const statuses: InventoryStatus[] = ['in-stock', 'low-stock', 'out-of-stock'];

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {statuses.map((status) => (
        <div key={status} className="flex items-center gap-1">
          <BaseInventoryStatusBadge
            status={status}
            showIcon={false}
            size={size}
          />
          {showCounts && (
            <span className="text-sm text-muted-foreground">
              {statusCounts[status]}
            </span>
          )}
        </div>
      ))}
    </div>
  );
}

/**
 * Inventory Status Filter Badges
 * 
 * Interactive badges for filtering inventory by status
 */
interface InventoryStatusFilterProps {
  activeFilter: string;
  onFilterChange: (filter: string) => void;
  counts?: Record<string, number>;
  className?: string;
}

export function InventoryStatusFilter({
  activeFilter,
  onFilterChange,
  counts,
  className
}: InventoryStatusFilterProps) {
  const filters = [
    { key: 'all', label: 'All Items', status: null },
    { key: 'in-stock', label: 'In Stock', status: 'in-stock' as InventoryStatus },
    { key: 'low-stock', label: 'Low Stock', status: 'low-stock' as InventoryStatus },
    { key: 'out-of-stock', label: 'Out of Stock', status: 'out-of-stock' as InventoryStatus }
  ];

  return (
    <div className={`flex flex-wrap items-center gap-2 ${className}`}>
      {filters.map((filter) => (
        <button
          key={filter.key}
          onClick={() => onFilterChange(filter.key)}
          className={`
            inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium
            transition-all duration-200 border
            ${activeFilter === filter.key
              ? 'bg-primary text-primary-foreground border-primary'
              : 'bg-background text-foreground border-border hover:bg-muted'
            }
          `}
          aria-pressed={activeFilter === filter.key}
        >
          {filter.status && (
            <BaseInventoryStatusBadge
              status={filter.status}
              showIcon={false}
              size="sm"
              className="border-0"
            />
          )}
          <span>{filter.label}</span>
          {counts && counts[filter.key] !== undefined && (
            <span className="ml-1 px-1.5 py-0.5 bg-muted text-muted-foreground rounded text-xs">
              {counts[filter.key]}
            </span>
          )}
        </button>
      ))}
    </div>
  );
}

/**
 * Utility function to get inventory status
 */
export { getInventoryStatus };

export default InventoryStatusBadge;
