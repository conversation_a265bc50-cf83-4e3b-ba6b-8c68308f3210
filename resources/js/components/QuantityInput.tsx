import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface QuantityInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  unitType: 'discrete' | 'continuous' | 'custom';
  error?: string;
  required?: boolean;
  placeholder?: string;
  className?: string;
  id?: string;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  unitOfMeasure?: string;
  showUnitHint?: boolean;
}

export default function QuantityInput({
  label,
  value,
  onChange,
  unitType,
  error,
  required = false,
  placeholder,
  className,
  id,
  onBlur,
  onFocus,
  unitOfMeasure,
  showUnitHint = true
}: QuantityInputProps) {
  const isWholeNumbersOnly = unitType === 'discrete';

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    
    if (isWholeNumbersOnly) {
      // For discrete units, only allow whole numbers
      // Remove any decimal points and non-numeric characters except for the initial digits
      const cleanValue = inputValue.replace(/[^\d]/g, '');
      onChange(cleanValue);
    } else {
      // For continuous and custom units, allow decimals
      // Basic validation to ensure proper decimal format
      if (inputValue === '' || /^\d*\.?\d*$/.test(inputValue)) {
        onChange(inputValue);
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (isWholeNumbersOnly) {
      // Prevent decimal point, minus sign, and 'e' for discrete units
      if (e.key === '.' || e.key === '-' || e.key === 'e' || e.key === 'E') {
        e.preventDefault();
      }
    }
  };

  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor={id}>
        {label} {required && <span className="text-destructive">*</span>}
      </Label>
      <Input
        id={id}
        type="number"
        min="0"
        step={isWholeNumbersOnly ? "1" : "0.01"}
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onBlur={onBlur}
        onFocus={onFocus}
        placeholder={placeholder}
        className={error ? 'border-destructive' : ''}
        // Additional props to prevent decimal input for discrete units
        {...(isWholeNumbersOnly && {
          onPaste: (e: React.ClipboardEvent<HTMLInputElement>) => {
            const paste = e.clipboardData.getData('text');
            if (!/^\d+$/.test(paste)) {
              e.preventDefault();
            }
          }
        })}
      />
      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}
      {showUnitHint && (
        <div className="space-y-1">
          {unitOfMeasure && (
            <p className="text-xs text-muted-foreground">
              Unit: {unitOfMeasure}
            </p>
          )}
          {isWholeNumbersOnly && (
            <p className="text-xs text-muted-foreground">
              Whole numbers only for this unit type
            </p>
          )}
        </div>
      )}
    </div>
  );
}