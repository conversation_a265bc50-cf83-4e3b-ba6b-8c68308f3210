import React, { useState } from 'react';
import { Check, ChevronsUpDown, Info } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Label } from '@/components/ui/label';
import { useUnitTypes } from '@/hooks/useUnitTypes';

interface UnitSelectorProps {
  value: string;
  onChange: (value: string) => void;
  onUnitTypeChange?: (unitType: 'discrete' | 'continuous' | 'custom') => void;
  error?: string;
  required?: boolean;
  className?: string;
}

export default function UnitSelector({
  value,
  onChange,
  onUnitTypeChange,
  error,
  required = false,
  className
}: UnitSelectorProps) {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  
  const { unitTypes, loading, getUnitType } = useUnitTypes();

  // Combine all predefined units alphabetically
  const allPredefinedUnits = unitTypes ? [
    ...unitTypes.discrete_units,
    ...unitTypes.continuous_units
  ].sort() : [];

  // Handle value change
  const handleValueChange = (newValue: string) => {
    onChange(newValue);
    const unitType = getUnitType(newValue);
    onUnitTypeChange?.(unitType);
  };

  // Filter units based on search
  const filteredUnits = allPredefinedUnits.filter(unit =>
    unit.toLowerCase().includes(searchValue.toLowerCase())
  );

  // Check if current value is custom (not in predefined lists)
  const isCustomUnit = value && !allPredefinedUnits.includes(value.toLowerCase().trim());

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center gap-2">
        <Label htmlFor="unit_of_measure">
          Unit of Measure {required && <span className="text-destructive">*</span>}
        </Label>
        <p className="text-sm text-muted-foreground">
            Search for predefined units or type in a custom unit.
        </p>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Info className="h-4 w-4 text-muted-foreground cursor-help" />
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs text-sm">
                Predefined units are either whole numbers (pieces, bags) or decimals (kg, liters).
                Custom units default to decimals.
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between",
              error && "border-destructive",
              !value && "text-muted-foreground"
            )}
          >
            {value || "Select or type a unit..."}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              placeholder="Search or type custom unit..."
              value={searchValue}
              onValueChange={(search) => {
                setSearchValue(search);
                // If user is typing something not in the list, treat it as custom
                if (search && !allPredefinedUnits.some(unit => 
                  unit.toLowerCase().includes(search.toLowerCase())
                )) {
                  handleValueChange(search);
                }
              }}
            />
            <CommandList>
              {loading ? (
                <CommandEmpty>Loading units...</CommandEmpty>
              ) : (
                <>
                  {filteredUnits.length > 0 ? (
                    <CommandGroup heading="Predefined Units">
                      {filteredUnits.map((unit) => (
                        <CommandItem
                          key={unit}
                          value={unit}
                          onSelect={(currentValue) => {
                            handleValueChange(currentValue);
                            setSearchValue('');
                            setOpen(false);
                          }}
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              value.toLowerCase() === unit.toLowerCase()
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                          <div className="flex items-center justify-between w-full">
                            <span>{unit}</span>
                            <span className="text-xs text-muted-foreground">
                              {getUnitType(unit) === 'discrete' ? 'whole' : 'decimal'}
                            </span>
                          </div>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  ) : searchValue ? (
                    <CommandGroup heading="Custom Unit">
                      <CommandItem
                        value={searchValue}
                        onSelect={() => {
                          handleValueChange(searchValue);
                          setOpen(false);
                        }}
                      >
                        <Check className="mr-2 h-4 w-4 opacity-0" />
                        <div className="flex items-center justify-between w-full">
                          <span>"{searchValue}"</span>
                          <span className="text-xs text-muted-foreground">custom (decimal)</span>
                        </div>
                      </CommandItem>
                    </CommandGroup>
                  ) : (
                    <CommandEmpty>Start typing to create a custom unit</CommandEmpty>
                  )}
                </>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Show unit type indicator */}
      {value && (
        <div className="text-xs text-muted-foreground">
          {isCustomUnit ? (
            <span>Custom unit - allows decimals</span>
          ) : (
            <span>
              {getUnitType(value) === 'discrete' 
                ? 'Discrete unit - whole numbers only' 
                : 'Continuous unit - allows decimals'
              }
            </span>
          )}
        </div>
      )}

      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}
    </div>
  );
}