import React from 'react';
import { StoreRequisitionStatusBadge as BaseStoreRequisitionStatusBadge } from '@/components/ui/status-badge';
// Import but not directly used in this file, used for type re-export
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { StoreRequisitionStatus } from '@/types/status-badge';
import { StoreRequisitionStatusBadgeProps } from '@/types/store-requisitions';

// Re-export the unified status badge types for backward compatibility
export type { StoreRequisitionStatus } from '@/types/status-badge';

/**
 * StoreRequisitionStatusBadge Component
 *
 * Enhanced store requisition status badge using the unified status system.
 * Provides consistent styling, proper accessibility, and responsive design
 * across all interfaces while maintaining backward compatibility.
 *
 * Now uses the universal status badge system for better consistency
 * and maintainability across the application.
 */
export function StoreRequisitionStatusBadge({
  status,
  className,
  showIcon = false,
  size = 'md'
}: StoreRequisitionStatusBadgeProps & {
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg'
}) {
  return (
    <BaseStoreRequisitionStatusBadge
      status={status}
      showIcon={showIcon}
      size={size}
      className={className}
    />
  );
}

/**
 * StoreRequisitionStatusBadgeWithIcon Component
 *
 * Enhanced version with status icons for better visual recognition.
 * This is now an alias for the main component with showIcon=true
 * for backward compatibility.
 */
interface StoreRequisitionStatusBadgeWithIconProps extends StoreRequisitionStatusBadgeProps {
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function StoreRequisitionStatusBadgeWithIcon({
  status,
  className,
  showIcon = false,
  size = 'md'
}: StoreRequisitionStatusBadgeWithIconProps) {
  return (
    <BaseStoreRequisitionStatusBadge
      status={status}
      showIcon={showIcon}
      size={size}
      className={className}
    />
  );
}

/**
 * StoreRequisitionStatusBadgeWithProgress Component
 *
 * Enhanced version that shows fulfillment progress for issued requisitions
 */
interface StoreRequisitionStatusBadgeWithProgressProps extends StoreRequisitionStatusBadgeProps {
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
  fulfillmentPercentage?: number;
  showProgress?: boolean;
}

export function StoreRequisitionStatusBadgeWithProgress({
  status,
  className,
  showIcon = false,
  size = 'md',
  fulfillmentPercentage,
  showProgress = false
}: StoreRequisitionStatusBadgeWithProgressProps) {
  return (
    <BaseStoreRequisitionStatusBadge
      status={status}
      showIcon={showIcon}
      size={size}
      className={className}
      showProgress={showProgress}
      fulfillmentPercentage={fulfillmentPercentage}
    />
  );
}

export default StoreRequisitionStatusBadge;
