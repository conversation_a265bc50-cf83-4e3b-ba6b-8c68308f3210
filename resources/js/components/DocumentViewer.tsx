import React, { useEffect, useState } from 'react';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { dracula } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import Markdown from 'react-markdown';

interface DocumentViewerProps {
  filePath: string;
}

const MarkdownRenderer: React.FC<React.PropsWithChildren<{ children: string }>> = ({ children }) => (
  <Markdown
    remarkPlugins={[remarkGfm]}
    rehypePlugins={[rehypeRaw]}
    skipHtml={false}
    components={{
      code({ inline, className, children: codeChildren, ...props }: { inline?: boolean; className?: string; children?: React.ReactNode }) {
        const match = /language-(\w+)/.exec(className || '');
        return !inline && match ? (
          <SyntaxHighlighter
            style={dracula}
            PreTag="div"
            language={match[1]}
            {...props}
          >
            {String(codeChildren).replace(/\n$/, '')}
          </SyntaxHighlighter>
        ) : (
          <code className={className} {...props}>
            {codeChildren}
          </code>
        );
      },
      p({ children, ...props }: { children?: React.ReactNode }) {
        return (
          <p style={{ marginBottom: '0.5rem', whiteSpace: 'pre-line' }} {...props}>
            {children}
          </p>
        );
      },
      li({ children, ...props }: { children?: React.ReactNode }) {
        return (
          <li style={{ marginBottom: '0.25rem', whiteSpace: 'pre-line' }} {...props}>
            {children}
          </li>
        );
      },
      table({ children, ...props }: { children?: React.ReactNode }) {
        return (
          <table style={{ marginBottom: '1.5rem', width: '100%' }} {...props}>
            {children}
          </table>
        );
      },
      tr({ children, ...props }: { children?: React.ReactNode }) {
        return (
          <tr style={{ whiteSpace: 'pre-line' }} {...props}>
            {children}
          </tr>
        );
      },
      td({ children, ...props }: { children?: React.ReactNode }) {
        return (
          <td style={{ whiteSpace: 'pre-line', padding: '0.5rem' }} {...props}>
            {children}
          </td>
        );
      },
      th({ children, ...props }: { children?: React.ReactNode }) {
        return (
          <th style={{ whiteSpace: 'pre-line', padding: '0.5rem' }} {...props}>
            {children}
          </th>
        );
      },
      br() {
        return <br />;
      },
    }}
  >
    {children}
  </Markdown>
);

const DocumentViewer: React.FC<DocumentViewerProps> = ({ filePath }) => {
  const [content, setContent] = useState<string>('Loading...');

  useEffect(() => {
    fetch(filePath)
      .then((res) => res.text())
      .then(setContent)
      .catch(() => setContent('Failed to load document.'));
  }, [filePath]);

  return (
    <div className="prose dark:prose-invert max-w-none">
      <MarkdownRenderer>{content}</MarkdownRenderer>
    </div>
  );
};

export default DocumentViewer;
