import { FacebookIcon, LinkedinIcon, MailIcon, MapPinIcon, PhoneIcon, TwitterIcon } from '@/components/svg/icons';
import { AnimatedLink } from '@/components/ui/animated-link';
import { easeOut, motion } from 'framer-motion';

const linkHover = {
    hover: {
        scale: 1.05,
        transition: { duration: 0.3, ease: easeOut },
    },
};

const footerLinks = {
    company: [
        { name: 'About Us', href: '/#' },
        { name: 'Contact', href: '/#' },
        { name: 'Blog', href: '/#' },
    ],
    support: [
        { name: 'Help Center', href: '/support' },
        { name: 'Contact Support', href: '/support' },
        { name: 'Community', href: '/support' },
    ],
};

const fadeIn = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: easeOut } },
};

export default function Footer() {
    return (
        <motion.footer
            className="border-border from-muted-foreground/5 to-muted-foreground/10 border-t bg-gradient-to-r"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
        >
            <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
                {/* Main footer content */}
                <div className="mb-8 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-5">
                    {/* Company info */}
                    <div className="lg:col-span-2">
                        <a
                            href="/"
                            className="mb-4 flex items-center text-gray-900 dark:text-white transition-colors duration-200 hover:text-primary"
                        >
                            <div className="from-primary to-primary/80 flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br shadow-md">
                                <img src="/sippar_logo.webp" alt="Sippar Logo" className="h-8 w-8 object-contain" />
                            </div>
                            <span className="ml-3 text-2xl font-bold">Sippar</span>
                        </a>
                        <p className="dark:text-muted-foreground mb-4 text-base leading-relaxed text-gray-900">
                            The most comprehensive financial management platform designed specifically for Kenyan organizations. Streamline operations, ensure compliance, and drive growth.
                        </p>
                        {/* Contact info */}
                        <div className="space-y-2 text-base">
                            <div className="dark:text-muted-foreground flex items-center text-gray-900">
                                <MailIcon className="text-primary mr-2 h-5 w-5" />
                                <span className="dark:text-muted-foreground text-gray-900"><EMAIL></span>
                            </div>
                            <div className="dark:text-muted-foreground flex items-center text-gray-900">
                                <PhoneIcon className="text-primary mr-2 h-5 w-5" />
                                <span className="dark:text-muted-foreground text-gray-900">+254 748 902 779</span>
                            </div>
                            <div className="dark:text-muted-foreground flex items-center text-gray-900">
                                <MapPinIcon className="text-primary mr-2 h-5 w-5" />
                                <span className="dark:text-muted-foreground text-gray-900">Kisumu, Kenya</span>
                            </div>
                        </div>
                    </div>
                    {/* Footer links: responsive layout */}
                    <div className="col-span-1 md:col-span-2 lg:col-span-3 flex flex-col justify-between">
                        {/* Responsive: double column on mobile, revert to grid-cols-3 on md+ */}
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 md:gap-8">
                            <div>
                                <h3 className="mb-3 text-lg font-semibold text-accent/70">Legal</h3>
                                <ul className="space-y-2 text-base">
                                    <li>
                                        <AnimatedLink href="/privacy-policy" className="transition-colors duration-200 hover:text-primary text-gray-900 dark:text-white">Privacy Policy</AnimatedLink>
                                    </li>
                                    <li>
                                        <AnimatedLink href="/terms-of-service" className="transition-colors duration-200 hover:text-primary text-gray-900 dark:text-white">Terms of Service</AnimatedLink>
                                    </li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="mb-3 text-lg font-semibold text-accent/70">Company</h3>
                                <ul className="space-y-2 text-base">
                                    {footerLinks.company.map((link) => (
                                        <li key={link.name}>
                                            <AnimatedLink href={link.href} className="text-gray-900 dark:text-white transition-colors duration-200 hover:text-primary">{link.name}</AnimatedLink>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                            <div>
                                <h3 className="mb-3 text-lg font-semibold text-accent/70">Support</h3>
                                <ul className="space-y-2 text-base">
                                    {footerLinks.support.map((link) => (
                                        <li key={link.name}>
                                            <AnimatedLink href={link.href} className="text-gray-900 dark:text-white transition-colors duration-200 hover:text-primary">{link.name}</AnimatedLink>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                {/* Newsletter signup - more compact */}
                <div className="border-border mb-4 border-t pt-6">
                    <form
                        className="rounded-2xl border-2 border-white/90 bg-white/95 p-6 text-center shadow-lg backdrop-blur-2xl dark:border-slate-700/70 dark:bg-slate-900/90 max-w-xl mx-auto"
                        onSubmit={async (e) => {
                            e.preventDefault();
                            const form = e.target as HTMLFormElement;
                            const email = (form.elements.namedItem('newsletter_email') as HTMLInputElement).value;
                            if (!email) return;
                            try {
                                const res = await fetch('/api/newsletter', {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({ email }),
                                });
                                if (res.ok) {
                                    // @ts-expect-error: window.toast is not typed but is globally available
                                    window.toast?.success('You are now subscribed! Check your email for updates.');
                                    form.reset();
                                } else {
                                    // @ts-expect-error: window.toast is not typed but is globally available
                                    window.toast?.error('Subscription failed. Please try again.');
                                }
                            } catch {
                                // @ts-expect-error: window.toast is not typed but is globally available
                                window.toast?.error('Network error. Please try again.');
                            }
                        }}
                    >
                        <h3 className="mb-2 text-lg font-bold text-gray-900 dark:text-white">Stay Updated with Sippar</h3>
                        <p className="dark:text-muted-foreground mb-4 text-base text-gray-900">
                            Get the latest updates on new features, financial management tips, and Kenyan business insights.
                        </p>
                        <div className="mx-auto flex max-w-md flex-col gap-2 sm:flex-row">
                            <input
                                type="email"
                                name="newsletter_email"
                                placeholder="Enter your email"
                                required
                                className="focus:border-primary focus:ring-primary flex-1 rounded-lg border border-gray-300 bg-white px-3 py-2 text-gray-900 focus:ring-2 dark:bg-slate-800/80 dark:text-white text-base"
                            />
                            <button
                                type="submit"
                                className="from-primary to-primary/80 text-primary-foreground hover:from-primary/80 hover:to-primary rounded-lg bg-gradient-to-r px-4 py-2 font-medium transition-all duration-200 text-base"
                            >
                                Subscribe
                            </button>
                        </div>
                    </form>
                </div>
                {/* Social links and copyright - always at the bottom */}
                <div className="border-border flex flex-col items-center justify-between border-t pt-4 gap-4 md:flex-row md:gap-2">
                    <div className="flex gap-4">
                        <motion.a
                            href="https://www.facebook.com/Zone01Kisumu/?locale=sw_KE"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-muted-foreground dark:text-white hover:text-primary p-2 rounded transition-colors duration-200"
                            variants={linkHover}
                            whileHover="hover"
                        >
                            <FacebookIcon className="h-6 w-6" />
                        </motion.a>
                        <motion.a
                            href="https://x.com/zone01kisumu"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-muted-foreground dark:text-white hover:text-primary p-2 rounded transition-colors duration-200"
                            variants={linkHover}
                            whileHover="hover"
                        >
                            <TwitterIcon className="h-6 w-6" />
                        </motion.a>
                        <motion.a
                            href="https://ke.linkedin.com/company/zone01kisumu"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-muted-foreground dark:text-white hover:text-primary p-2 rounded transition-colors duration-200"
                            variants={linkHover}
                            whileHover="hover"
                        >
                            <LinkedinIcon className="h-6 w-6" />
                        </motion.a>
                    </div>
                    <div className="flex flex-col items-center space-y-1 md:flex-row md:space-y-0 md:space-x-4">
                        <p className="dark:text-muted-foreground text-sm text-gray-900">
                            &copy; {new Date().getFullYear()} <span className="text-primary font-semibold">Sippar</span> Inc. All rights reserved.
                        </p>
                        <div className="flex space-x-2 text-sm">
                            <AnimatedLink href="/privacy-policy" className="transition-colors duration-200 hover:text-primary text-muted-foreground dark:text-white">Privacy Policy</AnimatedLink>
                            <AnimatedLink href="/terms-of-service" className="transition-colors duration-200 hover:text-primary text-muted-foreground dark:text-white">Terms of Service</AnimatedLink>
                        </div>
                    </div>
                </div>
            </div>
        </motion.footer>
    );
}
