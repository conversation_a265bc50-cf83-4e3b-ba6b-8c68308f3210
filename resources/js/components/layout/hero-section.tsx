import { ArrowRightIcon, PlayIcon } from '@/components/svg/icons';
import { motion, Variants } from 'framer-motion';
import { AnimatedLink } from '../ui/animated-link';

const fadeInUp: Variants = {
    hidden: { opacity: 0, y: 60 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: 'easeInOut' } },
};

const buttonShimmer: Variants = {
    initial: { backgroundPosition: '0% 50%' },
    animate: {
        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
        transition: { duration: 3, repeat: Number.POSITIVE_INFINITY, ease: 'linear' },
    },
};

export function HeroSection() {
    return (
        <motion.div
            className="to-primary-100 dark:from-background/90 dark:to-primary-900 relative flex min-h-screen items-center justify-center overflow-hidden rounded-3xl border-2 border-white/60 bg-gradient-to-br from-white/90 via-blue-50 text-gray-900 shadow-2xl ring-1 ring-blue-200/40 backdrop-blur-2xl dark:border-slate-800 dark:via-slate-900 dark:text-white dark:ring-slate-700/40 mt-20 sm:mt-24"
            style={{ boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.15), 0 1.5px 8px 0 rgba(80, 120, 255, 0.08)' }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1 }}
        >
            {/* Acrylic edge effect */}
            <div
                className="pointer-events-none absolute inset-0 rounded-3xl border-2 border-white/60 shadow-2xl ring-2 ring-blue-200/30 backdrop-blur-2xl dark:border-slate-800 dark:ring-slate-700/30"
                style={{ zIndex: 2 }}
            />

            {/* Background gradients - beautiful light theme */}
            <div className="to-primary-100 dark:from-background/90 dark:to-primary-900 absolute inset-0 bg-gradient-to-br from-white/80 via-blue-50 dark:via-slate-900" />
            <div className="bg-primary/70 absolute top-1/4 left-1/4 h-96 w-96 rounded-full blur-3xl" />
            <div className="bg-foreground/60 dark:bg-background/100 absolute right-1/4 bottom-1/4 h-96 w-96 rounded-full blur-3xl" />

            <div className="relative z-10 mx-auto max-w-7xl px-4 py-20 sm:px-6 md:py-32 lg:px-8">
                <div className="md:flex md:items-center md:justify-between">
                    {/* Left content */}
                    <motion.div className="mb-12 md:mb-0 md:w-1/2" variants={fadeInUp} initial="hidden" animate="visible">
                        <h1 className="mb-6 text-4xl font-bold tracking-tight text-gray-900 md:text-5xl dark:text-white">
                            Streamline Your{' '}
                            <span className="from-primary bg-gradient-to-r to-blue-500 bg-clip-text text-transparent">Petty Cash</span> Management
                        </h1>

                        <p className="dark:text-muted-foreground mb-8 text-lg text-gray-800 md:text-xl">
                            A powerful platform to manage organizational petty cash, connect suppliers, and optimize approval workflows with ease.
                        </p>

                        <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
                            <motion.div variants={buttonShimmer} initial="initial" animate="animate">
                                <AnimatedLink
                                    href="/register"
                                    className="bg-primary text-primary-foreground hover:bg-primary/90 inline-flex items-center justify-center rounded-full px-8 py-4 text-lg font-medium shadow-lg transition-all duration-200 hover:shadow-xl"
                                >
                                    Get Started
                                    <ArrowRightIcon className="ml-2 h-5 w-5" />
                                </AnimatedLink>
                            </motion.div>

                            <AnimatedLink
                                href="#features"
                                className="border-border bg-background text-foreground hover:bg-muted inline-flex items-center justify-center rounded-full border px-8 py-4 text-lg font-medium transition-all duration-200"
                            >
                                <PlayIcon className="mr-2 h-5 w-5" />
                                Learn More
                            </AnimatedLink>
                        </div>
                    </motion.div>

                    {/* Right content - Dashboard preview */}
                    <motion.div
                        className="md:w-1/2"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 1, delay: 0.2 }}
                    >
                        <div className="relative">
                            {/* Glowing border effect */}
                            <div className="from-primary to-primary absolute -inset-1 animate-pulse rounded-3xl bg-gradient-to-r via-blue-500 opacity-30 blur" />

                            {/* Dashboard mockup with glass effect (frame) and image inside */}
                            <div className="relative flex items-center justify-center rounded-3xl border border-white/20 bg-white/80 p-8 shadow-2xl backdrop-blur-sm dark:border-slate-700 dark:bg-slate-800/80">
                                <img
                                    src="/images/hero.webp"
                                    alt="Platform Preview"
                                    className="relative z-10 h-64 w-full rounded-2xl object-cover shadow-2xl md:h-[400px]"
                                />
                            </div>
                        </div>
                    </motion.div>
                </div>
            </div>
        </motion.div>
    );
}
