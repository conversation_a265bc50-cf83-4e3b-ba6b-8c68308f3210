import { motion } from 'framer-motion';
import { useState } from 'react';

interface FAQItem {
    id: string;
    question: string;
    answer: string;
}

const faqs: FAQItem[] = [
    {
        id: 'faq-1',
        question: 'What services does <PERSON>ppa<PERSON> provide?',
        answer: 'Sippar provides a comprehensive petty cash management platform that helps organizations streamline their cash handling processes. Our services include digital petty cash tracking, approval workflows, expense management, and automated reporting.'
    },
    {
        id: 'faq-2',
        question: 'How do I get started with <PERSON><PERSON><PERSON>?',
        answer: 'Getting started is easy! Simply click the "Get Started" button, register your organization, and follow our guided setup process. You can then invite team members and start managing your petty cash digitally.'
    },
    {
        id: 'faq-3',
        question: 'Can I customize approval workflows?',
        answer: 'Yes, <PERSON>ppar allows you to create custom approval workflows tailored to your organization\'s structure. You can define multiple approval levels, set spending limits, and assign specific approvers for different types of expenses.'
    },
    {
        id: 'faq-4',
        question: 'Is my financial data secure?',
        answer: 'Absolutely. We implement bank-grade security measures to protect your data, including end-to-end encryption, secure authentication, and regular security audits. Your financial information is always protected.'
    },
    {
        id: 'faq-5',
        question: 'Do you offer integration with accounting software?',
        answer: 'Yes, <PERSON><PERSON><PERSON> integrates with popular accounting software to ensure seamless financial data synchronization. This helps maintain accurate records and simplifies your accounting processes.'
    },
    {
        id: 'faq-6',
        question: 'What kind of support do you offer?',
        answer: 'We provide comprehensive support including 24/7 chat support, email assistance, and detailed documentation. Our team is always ready to help you make the most of Sippar.'
    }
];

function splitIntoColumns<T>(arr: T[], columns: number): T[][] {
    const result: T[][] = Array.from({ length: columns }, () => []);
    arr.forEach((item, idx) => {
        result[idx % columns].push(item);
    });
    return result;
}

export function FAQAccordionTwoColumn() {
    const [openId, setOpenId] = useState<string | null>(null);
    const columns = splitIntoColumns(faqs, 2);

    return (
        <div className="w-full">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {columns.map((col, colIdx) => (
                    <div key={colIdx} className="space-y-6">
                        {col.map((faq, idx) => {
                            const number = colIdx + 1 + idx * 2;
                            const isOpen = openId === faq.id;
                            return (
                                <motion.div
                                    key={faq.id}
                                    className={`group rounded-2xl border-2 border-primary/20 dark:border-primary/30 bg-white/70 dark:bg-slate-800/70 shadow-xl ring-1 ring-white/40 dark:ring-slate-700/40 backdrop-blur-lg transition-all duration-200 ${isOpen ? 'border-primary bg-primary/10 dark:bg-primary/20' : ''}`}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3, delay: idx * 0.05 }}
                                >
                                    <button
                                        className="flex w-full items-center justify-between text-left px-6 py-5 focus:outline-none"
                                        onClick={() => setOpenId(isOpen ? null : faq.id)}
                                        aria-expanded={isOpen}
                                    >
                                        <span className="flex items-center gap-4">
                                            <span className={`flex h-9 w-9 items-center justify-center rounded-full text-lg font-bold border-2 ${isOpen ? 'bg-primary text-white border-primary' : 'bg-white/80 dark:bg-slate-900/80 text-primary border-primary/30'}`}>{number}</span>
                                            <span className="text-lg font-semibold leading-7 text-gray-900 dark:text-white">{faq.question}</span>
                                        </span>
                                        <span className={`ml-6 flex h-7 items-center transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}>
                                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </span>
                                    </button>
                                    {isOpen && (
                                        <motion.div
                                            initial={{ height: 0, opacity: 0 }}
                                            animate={{ height: 'auto', opacity: 1 }}
                                            transition={{ duration: 0.3 }}
                                            className="px-6 pb-6 text-base leading-7 text-muted-foreground"
                                        >
                                            {faq.answer}
                                        </motion.div>
                                    )}
                                </motion.div>
                            );
                        })}
                    </div>
                ))}
            </div>
        </div>
    );
}
