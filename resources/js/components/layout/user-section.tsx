'use client';

import { motion } from 'framer-motion';
import { Building2, Network, Settings, Shield, UserPlus, Users } from 'lucide-react';

const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: 'easeOut' } },
};

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.2,
        },
    },
};

const userFeatures = [
    {
        icon: Building2,
        title: 'Organizational Structure',
        description: 'Define departments, divisions, and hierarchical structures with unlimited levels and custom reporting lines.',
    },
    {
        icon: Users,
        title: 'Role-Based Access Control',
        description: 'Create custom roles with granular permissions, ensuring users only access what they need.',
    },
    {
        icon: UserPlus,
        title: 'Employee Onboarding',
        description: 'Streamlined onboarding process with automated account creation, role assignment, and training workflows.',
    },
    {
        icon: Shield,
        title: 'Permission Matrix',
        description: 'Visual permission management with module-level, feature-level, and data-level access controls.',
    },
    {
        icon: Settings,
        title: 'User Lifecycle Management',
        description: 'Complete user management from creation to deactivation with audit trails and compliance tracking.',
    },
    {
        icon: Network,
        title: 'Multi-Branch Support',
        description: 'Manage users across multiple branches with centralized control and local administration rights.',
    },
];

export function UsersSection() {
    return (
        <>
            <style jsx>{`
                .org-chart {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    text-align: center;
                }

                .org-node {
                    border-radius: 0.75rem;
                    padding: 0.75rem 1rem;
                    transition: all 0.3s ease;
                    position: relative;
                    margin-bottom: 0.5rem;
                    min-width: 150px;
                }

                .org-root {
                    margin-bottom: 0;
                    z-index: 2;
                }

                .org-connector {
                    width: 2px;
                    background: hsl(var(--primary));
                    margin: 0 auto;
                    position: relative;
                    z-index: 1;
                }

                .org-connector::after {
                    content: '';
                    position: absolute;
                    bottom: -5px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 0;
                    height: 0;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-top: 5px solid hsl(var(--primary));
                }

                .org-level {
                    display: flex;
                    justify-content: center;
                    gap: 1rem;
                    width: 100%;
                    position: relative;
                    z-index: 2;
                }

                .org-sublevel {
                    margin-left: -4rem;
                }

                .org-branch {
                    position: relative;
                    width: 100%;
                }

                .org-connector-branch {
                    width: 2px;
                    background: hsl(var(--primary));
                    position: absolute;
                    left: calc(25% - 1px);
                    z-index: 1;
                }

                .org-connector-branch::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 50%;
                    height: 2px;
                    background: hsl(var(--primary));
                }

                .org-connector-branch::after {
                    content: '';
                    position: absolute;
                    bottom: -5px;
                    left: 0;
                    width: 0;
                    height: 0;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-top: 5px solid hsl(var(--primary));
                }

                .org-subnode {
                    font-size: 0.875rem;
                    min-width: 120px;
                }

                .org-node:hover {
                    border-color: hsl(var(--primary));
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                }
            `}</style>

            <motion.section
                id="users"
                className="ring-primary/20 dark:ring-primary/20 relative overflow-hidden rounded-3xl border-2 border-white/90 bg-white/90 py-20 shadow-2xl ring-2 backdrop-blur-2xl sm:py-24 dark:border-slate-700/70 dark:bg-slate-900/90"
                style={{ boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.10), 0 1.5px 8px 0 rgba(80, 120, 255, 0.08)' }}
                variants={staggerContainer}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
            >
                <div
                    className="ring-primary/20 dark:ring-primary/20 pointer-events-none absolute inset-0 rounded-3xl border-2 border-white/90 shadow-2xl ring-2 backdrop-blur-2xl dark:border-slate-700/70"
                    style={{ zIndex: 2 }}
                />
                <div className="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <motion.div className="mb-16 text-center" variants={fadeInUp}>
                        <h2 className="mb-4 text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl dark:text-white">
                            Comprehensive User Management
                        </h2>
                        <p className="dark:text-muted-foreground mx-auto max-w-2xl text-lg text-gray-800">
                            Build and manage your organizational structure with advanced user controls and security features.
                        </p>
                    </motion.div>

                    {/* Organizational Setup Explanation */}
                    <motion.div
                        className="mb-16 rounded-2xl border-2 border-white/80 bg-white/90 p-8 shadow-lg backdrop-blur-2xl dark:border-slate-700/50 dark:bg-slate-800/90"
                        variants={fadeInUp}
                    >
                        <h3 className="mb-6 text-center text-2xl font-bold text-gray-900 dark:text-white">Organizational Setup & Structure</h3>

                        <div className="grid grid-cols-1 items-center gap-8 lg:grid-cols-2">
                            <div>
                                <h4 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Flexible Hierarchy</h4>
                                <ul className="dark:text-muted-foreground space-y-3 text-gray-800">
                                    <li className="flex items-start">
                                        <div className="bg-primary mt-2 mr-3 h-2 w-2 flex-shrink-0 rounded-full" />
                                        <span>
                                            <strong>Multi-level departments:</strong> Create unlimited department levels with custom naming
                                        </span>
                                    </li>
                                    <li className="flex items-start">
                                        <div className="bg-primary mt-2 mr-3 h-2 w-2 flex-shrink-0 rounded-full" />
                                        <span>
                                            <strong>Reporting structures:</strong> Define who reports to whom with matrix management support
                                        </span>
                                    </li>
                                    <li className="flex items-start">
                                        <div className="bg-primary mt-2 mr-3 h-2 w-2 flex-shrink-0 rounded-full" />
                                        <span>
                                            <strong>Cost centers:</strong> Assign departments to cost centers for accurate financial tracking
                                        </span>
                                    </li>
                                    <li className="flex items-start">
                                        <div className="bg-primary mt-2 mr-3 h-2 w-2 flex-shrink-0 rounded-full" />
                                        <span>
                                            <strong>Branch management:</strong> Manage multiple locations with local and central controls
                                        </span>
                                    </li>
                                </ul>
                            </div>

                            <motion.div
                                className="rounded-xl border-2 border-white/80 bg-white/90 p-6 shadow-lg backdrop-blur-2xl dark:border-slate-700/50 dark:bg-slate-800/90 flex items-center justify-center"
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 1, delay: 0.2 }}
                            >
                                <img
                                    src="/images/structure.webp"
                                    alt="Organizational structure diagram"
                                    className="max-w-full h-auto rounded-lg shadow-md border border-gray-200 dark:border-slate-700 bg-white/80 dark:bg-slate-900/80"
                                    style={{ objectFit: 'contain', maxHeight: 340 }}
                                />
                            </motion.div>
                        </div>
                    </motion.div>

                    {/* Features Grid */}
                    <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                        {userFeatures.map((feature, index) => {
                            const IconComponent = feature.icon;
                            return (
                                <motion.div
                                    key={index}
                                    className="group rounded-2xl border-2 border-white/80 bg-white/90 p-8 shadow-lg backdrop-blur-2xl transition-all duration-300 hover:border-blue-300 hover:shadow-2xl dark:border-slate-700/50 dark:bg-slate-800/90"
                                    variants={fadeInUp}
                                >
                                    <div className="from-primary text-primary-foreground mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br to-blue-500 shadow-lg transition-transform duration-300 group-hover:scale-110">
                                        <IconComponent className="h-8 w-8" />
                                    </div>
                                    <h3 className="mb-3 text-xl font-semibold text-gray-900 dark:text-white">{feature.title}</h3>
                                    <p className="dark:text-muted-foreground leading-relaxed text-gray-800">{feature.description}</p>
                                </motion.div>
                            );
                        })}
                    </div>
                </div>
            </motion.section>
        </>
    );
}
