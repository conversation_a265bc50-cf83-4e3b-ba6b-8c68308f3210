'use client';

import { motion } from 'framer-motion';
import { BarChart3, Bell, FileSpreadsheet, Mail, PieChart, TrendingUp } from 'lucide-react';

const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: { duration: 0.8, ease: [0.42, 0, 0.58, 1] as [number, number, number, number] },
    },
};

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.2,
        },
    },
};

const expenseFeatures = [
    {
        icon: PieChart,
        title: 'Chart of Accounts',
        description: '5 comprehensive account categories: Assets, Liabilities, Equity, Revenue, and Expenses with sub-account management.',
    },
    {
        icon: BarChart3,
        title: 'Financial Reports',
        description: 'Generate P&L statements, balance sheets, cash flow reports, and custom financial reports instantly.',
    },
    {
        icon: TrendingUp,
        title: 'Real-time Dashboards',
        description: 'Live financial dashboards with KPIs, expense tracking, budget vs actual, and trend analysis.',
    },
    {
        icon: Bell,
        title: 'Smart Notifications',
        description: 'Automated alerts for budget overruns, pending approvals, payment due dates, and compliance deadlines.',
    },
    {
        icon: FileSpreadsheet,
        title: 'Advanced Reports',
        description: 'Customizable reports with filters, drill-down capabilities, and export to PDF.',
    },
    {
        icon: Mail,
        title: 'Email Integration',
        description: 'Automated email reports, expense notifications, and approval workflows with customizable templates.',
    },
];

export function ExpensesSection() {
    return (
        <motion.section
            className="ring-primary/20 dark:ring-primary/20 relative overflow-hidden rounded-3xl border-2 border-white/90 bg-white/95 py-20 shadow-2xl ring-2 backdrop-blur-2xl sm:py-24 dark:border-slate-700/70 dark:bg-slate-900/90"
            style={{ boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.10), 0 1.5px 8px 0 rgba(80, 120, 255, 0.08)' }}
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
        >
            {/* Acrylic edge effect */}
            <div
                className="ring-primary/20 dark:ring-primary/20 pointer-events-none absolute inset-0 rounded-3xl border-2 border-white/90 shadow-2xl ring-2 backdrop-blur-2xl dark:border-slate-700/70"
                style={{ zIndex: 2 }}
            />
            <div className="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <motion.div className="mb-16 text-center" variants={fadeInUp}>
                    <h2 className="mb-4 text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl dark:text-white">Expense Management</h2>
                    <p className="dark:text-muted-foreground mx-auto max-w-2xl text-lg text-gray-800">
                        Track, control, and optimize your organization's expenses with real-time analytics and approval workflows.
                    </p>
                </motion.div>
                {/* Chart of Accounts */}
                <motion.div className="mb-16" variants={fadeInUp}>                    
                </motion.div>
                {/* Features Grid */}
                <div className="flex flex-wrap justify-center gap-8">
                    {expenseFeatures.map((feature, index) => {
                        const IconComponent = feature.icon;
                        return (
                            <motion.div
                                key={index}
                                className="group rounded-2xl border-2 border-white/80 bg-white/95 p-8 shadow-lg backdrop-blur-2xl transition-all duration-300 hover:border-emerald-300 hover:shadow-2xl dark:border-slate-700/50 dark:bg-slate-800/80 flex flex-col items-center w-full sm:w-[340px]"
                                variants={fadeInUp}
                            >
                                <div className="from-primary text-primary-foreground mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br to-blue-500 shadow-lg transition-transform duration-300 group-hover:scale-110">
                                    <IconComponent className="h-8 w-8" />
                                </div>
                                <h3 className="mb-3 text-xl font-semibold text-gray-900 dark:text-white">{feature.title}</h3>
                                <p className="dark:text-muted-foreground leading-relaxed text-gray-800">{feature.description}</p>
                            </motion.div>
                        );
                    })}
                </div>
            </div>
        </motion.section>
    );
}
