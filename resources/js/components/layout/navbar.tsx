import { ArrowRightIcon, MoonIcon, SunIcon } from '@/components/svg/icons';
import { AnimatedLink } from '@/components/ui/animated-link';
import { ButtonLink } from '@/components/ui/button-variants';
import { motion, easeInOut } from 'framer-motion';
import { useEffect, useState } from 'react';
import { router } from '@inertiajs/react';

const linkHover = {
    hover: {
        scale: 1.05,
        transition: { duration: 0.3, ease: easeInOut },
    },
};

const logoHover = {
    hover: {
        scale: 1.1,
        transition: { duration: 0.3, ease: easeInOut },
    },
};

const navLinks = [
    { href: '/#features', label: 'Features' },
    { href: '/#legal', label: 'Legal' },
    { href: '/support', label: 'Support' },
];

interface User {
    first_name: string;
    last_name: string;
}

function handleSectionLinkClick(href: string) {
    return (e: React.MouseEvent) => {
        // Only handle section links (/#section)
        if (!href.startsWith('/#')) return;
        const match = href.match(/^\/#([^/]+)/);
        if (!match) return;
        const section = href.slice(2); // '/#section' -> 'section'
        const isHome = window.location.pathname === '/';
        const scrollToSection = (attempts = 0) => {
            const el = document.getElementById(section);
            if (el) {
                el.scrollIntoView({ behavior: 'smooth' });
            } else if (attempts < 5) {
                setTimeout(() => scrollToSection(attempts + 1), 200);
            }
        };
        e.preventDefault();
        if (isHome) {
            scrollToSection();
        } else {
            router.visit('/', {
                onSuccess: () => {
                    setTimeout(() => scrollToSection(), 400);
                },
            });
        }
    };
}

export function Navbar({ user }: { user?: User }) {
    const [isDark, setIsDark] = useState(false);
    const [isScrolled, setIsScrolled] = useState(false);
    const [isClient, setIsClient] = useState(false);
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        setIsClient(true);

        // Check for saved theme preference or default to light
        if (typeof window !== 'undefined') {
            const savedTheme = localStorage.getItem('theme');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

            if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
                setIsDark(true);
                document.documentElement.classList.add('dark');
            }

            // Handle scroll effect
            const handleScroll = () => {
                setIsScrolled(window.scrollY > 50);
            };

            window.addEventListener('scroll', handleScroll, { passive: true });
            return () => window.removeEventListener('scroll', handleScroll);
        }
    }, []);

    const toggleTheme = () => {
        if (typeof window === 'undefined') return;

        const newTheme = !isDark;
        setIsDark(newTheme);

        if (newTheme) {
            document.documentElement.classList.add('dark');
            localStorage.setItem('theme', 'dark');
        } else {
            document.documentElement.classList.remove('dark');
            localStorage.setItem('theme', 'light');
        }
    };

    // Prevent hydration mismatch
    if (!isClient) {
        return (
            <nav className="fixed top-4 z-50 w-full px-4">
                <div className="mx-auto max-w-6xl">
                    <div className="relative rounded-2xl border border-gray-200/30 bg-white/70 shadow-lg backdrop-blur-md">
                        <div className="flex items-center justify-between px-6 py-4">
                            <div className="flex items-center">
                                <ButtonLink href="/" className="flex flex-shrink-0 items-center">
                                    <div className="flex h-8 w-8 items-center justify-center rounded-lg">
                                        <span className="text-foreground/100 text-lg font-bold">S</span>
                                    </div>
                                    <span className="text-background/100 ml-2 text-xl font-semibold">Sippar</span>
                                </ButtonLink>
                            </div>
                            <div className="flex items-center space-x-4">
                                <ButtonLink href="/login" className="text-muted-foreground/100 text-lg font-medium">
                                    Log in
                                </ButtonLink>
                                <ButtonLink
                                    href="/register"
                                    className="from-primary-600 to-primary-800 text-foreground/90 inline-flex items-center gap-1 rounded-lg bg-gradient-to-r px-4 py-2 text-sm font-medium"
                                >
                                    Get Started
                                </ButtonLink>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        );
    }

    return (
        <nav className={`fixed z-50 w-full transition-all duration-500 ease-out ${isScrolled ? 'top-0 px-0' : 'top-4 px-4'}`}>
            <div className={`mx-auto transition-all duration-500 ease-out ${isScrolled ? 'max-w-none' : 'max-w-6xl'}`}>
                <div
                    className={`relative border backdrop-blur-xl transition-all duration-500 ease-out ${
                        isScrolled
                            ? 'dark:border-border/20 rounded-none border-muted-foreground/20 bg-muted-foreground/40 shadow-lg dark:bg-background/40'
                            : 'dark:border-border/30 rounded-2xl border-muted-foreground/30 bg-muted-foreground/30 shadow-xl dark:bg-background/30'
                    }`}
                >
                    <div
                        className={`flex items-center justify-between transition-all duration-500 ease-out ${
                            isScrolled ? 'mx-auto max-w-7xl px-8 py-2' : 'px-6 py-2'
                        }`}
                    >
                        <div className="flex items-center">
                            <ButtonLink href="/" className="flex flex-shrink-0 items-center">
                                <motion.div
                                    className="flex h-8 w-8 items-center justify-center rounded-lg shadow-md transition-shadow duration-200 hover:shadow-lg"
                                    variants={logoHover}
                                    whileHover="hover"
                                >
                                    <img src="/sippar_logo.webp" alt="Sippar Logo" className="h-6 w-6 object-contain" />
                                </motion.div>
                                <motion.span
                                    className="dark:text-background ml-2 text-xl font-semibold text-gray-900 transition-colors duration-200"
                                    variants={logoHover}
                                    whileHover="hover"
                                >
                                    Sippar
                                </motion.span>
                            </ButtonLink>
                        </div>

                        {/* Desktop Menu */}
                        <div className="hidden items-center space-x-1 md:flex">
                            {navLinks.map((item) => (
                                <AnimatedLink
                                    key={item.href}
                                    href={item.href}
                                    onClick={item.href.startsWith('/#') ? handleSectionLinkClick(item.href) : undefined}
                                    className="dark:text-foreground/80 hover:text-primary group relative px-3 py-1.5 text-base font-medium text-gray-900 transition-colors duration-200"
                                >
                                    <span className="relative z-10">{item.label}</span>
                                    <span className="bg-primary absolute right-3 -bottom-1 left-3 h-0.5 origin-left scale-x-0 transition-transform duration-300 group-hover:scale-x-100" />
                                </AnimatedLink>
                            ))}
                        </div>

                        <div className="flex items-center space-x-4">
                            <button
                                onClick={toggleTheme}
                                className="rounded-lg p-2 transition-all duration-200 hover:scale-105 hover:bg-accent/10"
                                aria-label="Toggle theme"
                            >
                                {isDark ? (
                                    <SunIcon className="h-5 w-5 text-yellow-500 hover:text-yellow-400" />
                                ) : (
                                    <MoonIcon className="h-5 w-5 text-slate-500 hover:text-primary transition-colors" />
                                )}
                            </button>

                            {/* Desktop Auth Buttons */}
                            <div className="hidden items-center space-x-4 sm:flex">
                                {user ? (
                                    <div className="flex items-center space-x-4">
                                        <span className="text-foreground font-medium">
                                            {user.first_name} {user.last_name}
                                        </span>
                                        <motion.button
                                            className="bg-primary hover:bg-primary/80 rounded-full px-4 py-2 text-sm font-medium text-white transition-colors"
                                            variants={linkHover}
                                            whileHover="hover"
                                        >
                                            Logout
                                        </motion.button>
                                    </div>
                                ) : (
                                    <>
                                        <motion.div variants={linkHover} whileHover="hover">
                                            <AnimatedLink
                                                href="/login"
                                                className="text-muted-foreground hover:text-primary text-sm font-medium transition-colors duration-200"
                                            >
                                                Log in
                                            </AnimatedLink>
                                        </motion.div>

                                        <motion.div variants={linkHover} whileHover="hover">
                                            <ButtonLink
                                                href="/register"
                                                className="from-primary-600 to-primary-800 hover:from-primary-700 hover:to-primary-900 focus:ring-primary inline-flex transform items-center gap-1 rounded-lg bg-gradient-to-r px-4 py-2 text-sm font-medium text-white shadow-md transition-all duration-200 hover:-translate-y-0.5 hover:shadow-lg focus:ring-2 focus:ring-offset-2 focus:outline-none"
                                            >
                                                Get Started
                                                <ArrowRightIcon className="h-4 w-4" />
                                            </ButtonLink>
                                        </motion.div>
                                    </>
                                )}
                            </div>

                            {/* Mobile Menu Toggle */}
                            <div className="flex items-center sm:hidden">
                                <button onClick={() => setIsOpen(!isOpen)} className="text-primary dark:text-white hover:text-accent focus:outline-none">
                                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        {isOpen ? (
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        ) : (
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
                                        )}
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Mobile Menu */}
                    {isOpen && (
                        <motion.div
                            className="bg-background/90 border-border border-t shadow-md backdrop-blur-xl sm:hidden"
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            transition={{ duration: 0.3, ease: 'easeOut' }}
                        >
                            <div className="space-y-2 px-2 pt-2 pb-3">
                                {/* Mobile Navigation Links */}
                                <div className="mb-2 flex flex-col gap-1">
                                    {navLinks.map((item) => (
                                        <AnimatedLink
                                            key={item.href}
                                            href={item.href}
                                            onClick={item.href.startsWith('/#') ? handleSectionLinkClick(item.href) : undefined}
                                            className="dark:text-foreground/90 hover:bg-primary/10 hover:text-primary block w-full rounded-lg bg-white/80 px-4 py-3 text-left text-base font-semibold text-gray-900 shadow-sm transition-all duration-200 dark:bg-[#23272f]/80"
                                        >
                                            {item.label}
                                        </AnimatedLink>
                                    ))}
                                </div>

                                {/* Mobile Auth Buttons */}
                                {user ? (
                                    <div className="mt-2 flex flex-col gap-2">
                                        <span className="dark:text-foreground/80 block rounded-lg bg-white/70 px-4 py-2 text-base font-medium text-gray-900 dark:bg-[#23272f]/70">
                                            {user.first_name} {user.last_name}
                                        </span>
                                        <button className="bg-primary hover:bg-primary/80 rounded-full px-4 py-2 text-base font-semibold text-white transition-colors">
                                            Logout
                                        </button>
                                    </div>
                                ) : (
                                    <div className="mt-2 flex flex-col gap-2">
                                        <AnimatedLink
                                            href="/login"
                                            className="border-primary text-primary hover:bg-primary rounded-full border-2 px-4 py-2 text-base font-semibold transition-colors hover:text-white text-center"
                                        >
                                            Login
                                        </AnimatedLink>
                                        <ButtonLink
                                            href="/register"
                                            className="bg-primary hover:bg-primary/80 rounded-full px-4 py-2 text-base font-semibold text-white transition-colors text-center"
                                        >
                                            Register
                                        </ButtonLink>
                                    </div>
                                )}
                            </div>
                        </motion.div>
                    )}
                </div>
            </div>
        </nav>
    );
}
