import '../css/app.css';

import { createInertiaApp } from '@inertiajs/react';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { createRoot } from 'react-dom/client';
import { initializeTheme } from './hooks/use-appearance';

createInertiaApp({
    title: (title) => `${title} - Sippar`,
    resolve: (name) => {
        // Explicit aliases for legal pages
        if (name === 'privacy-policy') return import('./pages/privacy-policy');
        if (name === 'terms-of-service') return import('./pages/terms-of-service');
        return resolvePageComponent(`./pages/${name}.tsx`, import.meta.glob('./pages/**/*.tsx'));
    },
    setup({ el, App, props }) {
        const root = createRoot(el);
        root.render(<App {...props} />);
    },
    progress: {
        color: '#10b981',
    },
});

// This will set light / dark mode on load...
initializeTheme();
