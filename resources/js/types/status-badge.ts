export type StoreRequisitionStatus =
  | 'draft'
  | 'pending_approval'
  | 'approved'
  | 'rejected'
  | 'returned_for_revision'
  | 'issued'
  | 'partially_issued';

/**
 * Regular Requisition Status Types
 */
export type RequisitionStatus =
  | 'pending_approval'
  | 'approved'
  | 'rejected';

/**
 * Inventory Item Status Types
 */
export type InventoryStatus =
  | 'in-stock'
  | 'low-stock'
  | 'out-of-stock';

export type UniversalStatus = 
  | StoreRequisitionStatus 
  | RequisitionStatus 
  | InventoryStatus;

export type StatusCategory =
  | 'draft'      
  | 'pending'    
  | 'success'    
  | 'error'      
  | 'warning'    
  | 'info'      
  | 'partial';  

/**
 * Status Configuration Interface
 */
export interface StatusConfig {
  label: string;
  category: StatusCategory;
  icon: string;
  description: string;
  cssVariable: string;
}

/**
 * Status Badge Props Interface
 */
export interface StatusBadgeProps {
  status: UniversalStatus;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  'aria-label'?: string;
}

export const STATUS_CONFIG: Record<UniversalStatus, StatusConfig> = {
  // Store Requisition Statuses
  'draft': {
    label: 'Draft',
    category: 'draft',
    icon: '📝',
    description: 'Initial draft state, can be edited',
    cssVariable: '--status-draft'
  },
  'pending_approval': {
    label: 'Pending Approval',
    category: 'pending',
    icon: '⏳',
    description: 'Submitted and waiting for approval',
    cssVariable: '--status-pending-approval'
  },
  'approved': {
    label: 'Approved',
    category: 'success',
    icon: '✅',
    description: 'Approved by authorized personnel',
    cssVariable: '--status-approved'
  },
  'rejected': {
    label: 'Rejected',
    category: 'error',
    icon: '❌',
    description: 'Rejected with reason provided',
    cssVariable: '--status-rejected'
  },
  'returned_for_revision': {
    label: 'Returned for Revision',
    category: 'warning',
    icon: '🔄',
    description: 'Returned for changes or corrections',
    cssVariable: '--status-returned-for-revision'
  },
  'issued': {
    label: 'Issued',
    category: 'info',
    icon: '📦',
    description: 'Items have been issued/completed',
    cssVariable: '--status-issued'
  },
  'partially_issued': {
    label: 'Partially Issued',
    category: 'partial',
    icon: '📋',
    description: 'Some items issued, others pending',
    cssVariable: '--status-partially-issued'
  },
  
  // Inventory Statuses
  'in-stock': {
    label: 'In Stock',
    category: 'success',
    icon: '✅',
    description: 'Adequate stock levels available',
    cssVariable: '--status-in-stock'
  },
  'low-stock': {
    label: 'Low Stock',
    category: 'warning',
    icon: '⚠️',
    description: 'Stock below reorder level',
    cssVariable: '--status-low-stock'
  },
  'out-of-stock': {
    label: 'Out of Stock',
    category: 'error',
    icon: '🚫',
    description: 'No stock available',
    cssVariable: '--status-out-of-stock'
  }
} as const;

/**
 * Status Category Color Mapping
 */
export const CATEGORY_STYLES: Record<StatusCategory, { backgroundColor: string; color: string }> = {
  draft: { 
    backgroundColor: 'var(--status-draft)', 
    color: 'var(--status-draft-foreground)' 
  },
  pending: { 
    backgroundColor: 'var(--status-pending)', 
    color: 'var(--status-pending-foreground)' 
  },
  success: { 
    backgroundColor: 'var(--status-success)', 
    color: 'var(--status-success-foreground)' 
  },
  error: { 
    backgroundColor: 'var(--status-error)', 
    color: 'var(--status-error-foreground)' 
  },
  warning: { 
    backgroundColor: 'var(--status-revision)', 
    color: 'var(--status-revision-foreground)' 
  },
  info: { 
    backgroundColor: 'var(--status-info)', 
    color: 'var(--status-info-foreground)' 
  },
  partial: { 
    backgroundColor: 'var(--status-partial)', 
    color: 'var(--status-partial-foreground)' 
  }
} as const;

/**
 * Get status configuration for a given status
 */
export function getStatusConfig(status: UniversalStatus): StatusConfig {
  return STATUS_CONFIG[status];
}

/**
 * Get status label for display
 */
export function getStatusLabel(status: UniversalStatus): string {
  return STATUS_CONFIG[status]?.label || status;
}

/**
 * Get status icon
 */
export function getStatusIcon(status: UniversalStatus): string {
  return STATUS_CONFIG[status]?.icon || '📄';
}

/**
 * Get status category
 */
export function getStatusCategory(status: UniversalStatus): StatusCategory {
  return STATUS_CONFIG[status]?.category || 'draft';
}

/**
 * Get status styles based on category
 */
export function getStatusStyles(status: UniversalStatus): { backgroundColor: string; color: string } {
  const category = getStatusCategory(status);
  return CATEGORY_STYLES[category];
}

/**
 * Check if status represents a pending state
 */
export function isPendingStatus(status: UniversalStatus): boolean {
  return getStatusCategory(status) === 'pending';
}

/**
 * Check if status represents a success state
 */
export function isSuccessStatus(status: UniversalStatus): boolean {
  return getStatusCategory(status) === 'success';
}

/**
 * Check if status represents an error state
 */
export function isErrorStatus(status: UniversalStatus): boolean {
  return getStatusCategory(status) === 'error';
}

/**
 * Check if status represents a warning state
 */
export function isWarningStatus(status: UniversalStatus): boolean {
  return getStatusCategory(status) === 'warning';
}

/**
 * Get accessible label for status
 */
export function getStatusAccessibleLabel(status: UniversalStatus): string {
  const config = getStatusConfig(status);
  
  // Handle undefined config gracefully
  if (!config) {
    console.warn(`No configuration found for status: "${status}"`);
    return `Status: ${status}. No additional information available.`;
  }
  
  return `Status: ${config.label}. ${config.description}`;
}

/**
 * Type guard for Store Requisition Status
 */
export function isStoreRequisitionStatus(status: string): status is StoreRequisitionStatus {
  return ['draft', 'pending_approval', 'approved', 'rejected', 'returned_for_revision', 'issued', 'partially_issued'].includes(status);
}

/**
 * Type guard for Regular Requisition Status
 */
export function isRequisitionStatus(status: string): status is RequisitionStatus {
  return ['pending_approval', 'approved', 'rejected'].includes(status);
}

/**
 * Type guard for Inventory Status
 */
export function isInventoryStatus(status: string): status is InventoryStatus {
  return ['in-stock', 'low-stock', 'out-of-stock'].includes(status);
}

/**
 * Type guard for Universal Status
 */
export function isUniversalStatus(status: string): status is UniversalStatus {
  return isStoreRequisitionStatus(status) || isRequisitionStatus(status) || isInventoryStatus(status);
}
