export interface StoreRequisitionItem {
  id?: number;
  store_requisition_id?: number;
  inventory_item_id: number;
  quantity_requested: number;
  quantity_issued?: number;

  // Relationships (<PERSON><PERSON> uses snake_case in JSON)
  inventory_item?: InventoryItem;
  inventoryItem?: InventoryItem; 
}

export interface StoreRequisition {
  id: number;
  organization_id: number;
  branch_id: number;
  department_id: number;
  requester_user_id: number;
  approver_user_id?: number | null;
  issuer_user_id?: number | null;
  purpose: string;
  status: StoreRequisitionStatus;
  rejection_reason?: string | null;
  requested_at: string | null;
  approved_at?: string | null;
  issued_at?: string | null;
  created_at: string;
  updated_at: string;

  // Relationships
  items?: StoreRequisitionItem[];
  requester?: StoreRequisitionUser;
  approver?: StoreRequisitionUser;
  issuer?: StoreRequisitionUser;
  department?: StoreRequisitionDepartment;
  organization?: StoreRequisitionOrganization;
  branch?: StoreRequisitionBranch;
  histories?: StoreRequisitionHistoryEntry[];
  attachments?: Attachment[];
}

export interface Attachment {
   id: number;
   original_name: string;
   file_size: number;
   mime_type: string;
   description?: string;
   is_evidence: boolean;
   uploaded_at_step?: string;
   created_at: string;
   uploader: {
       id: number;
       first_name: string;
       last_name: string;
   };
}

export interface InventoryItem {
  id: number;
  sku: string;
  name: string;
  description?: string;
  unit_of_measure: string;
  quantity_on_hand: number;
  reorder_level: number;
  current_stock?: number;
  minimum_stock?: number;
  category?: string;
  organization?: StoreRequisitionOrganization;
  branch?: StoreRequisitionBranch;
}

export interface InventoryTransaction {
  id: number;
  inventory_item_id: number;
  transaction_type: string;
  quantity_change: number;
  balance_after: number;
  transaction_date: string;
  reference_number?: string;
  notes?: string;
  user?: StoreRequisitionUser;
}

//  SUPPORTING ENTITIES

export interface StoreRequisitionUser {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  username?: string;
  phone?: string | null;
  avatar?: string | null;
  display_name?: string;
  roles?: string[];
  permissions?: string[];
}

export interface StoreRequisitionDepartment {
  id: number;
  name: string;
  branch_id?: number;
}

export interface StoreRequisitionOrganization {
  id: number;
  name: string;
}

export interface StoreRequisitionBranch {
  id: number;
  name: string;
  organization_id?: number;
}

export interface StoreRequisitionRole {
  id: number;
  name: string;
  description?: string;
}

export interface StoreRequisitionHistoryEntry {
  id: number;
  store_requisition_id: number;
  user_id: number;
  action: string;
  comments?: string | null;
  changes?: Record<string, unknown> | null;
  created_at: string;
  updated_at: string;

  // Relationships
  user: StoreRequisitionUser;
}

export type StoreRequisitionHistoryAction =
  | 'created_as_draft'
  | 'created_and_submitted'
  | 'edited'
  | 'edited_and_submitted'
  | 'edited_and_resubmitted'
  | 'submitted_for_approval'
  | 'approved'
  | 'rejected'
  | 'returned_for_revision'
  | 'issued';

//  ENUMS AND CONSTANTS 

// Import from unified status system for consistency
export type { StoreRequisitionStatus } from '@/types/status-badge';

export type StoreRequisitionAction =
  | 'submit'
  | 'approve'
  | 'reject'
  | 'return_for_revision'
  | 'issue'
  | 'edit';

export const STORE_REQUISITION_STATUSES: Record<StoreRequisitionStatus, string> = {
  draft: 'Draft',
  pending_approval: 'Pending Approval',
  approved: 'Approved',
  rejected: 'Rejected',
  returned_for_revision: 'Returned for Revision',
  issued: 'Issued',
  partially_issued: 'Partially Issued',
} as const;

import {
  getStatusStyles,
  type StoreRequisitionStatus
} from '@/types/status-badge';

export const STORE_STATUS_VARIANTS: Record<StoreRequisitionStatus, 'muted' | 'warning' | 'success' | 'destructive' | 'info'> = {
  draft: 'muted',
  pending_approval: 'warning',
  approved: 'success',
  rejected: 'destructive',
  returned_for_revision: 'warning',
  issued: 'info',
  partially_issued: 'warning',
} as const;

// Use unified status system for styles
export const STORE_STATUS_STYLES: Record<StoreRequisitionStatus, { backgroundColor: string; color: string }> = {
  draft: getStatusStyles('draft'),
  pending_approval: getStatusStyles('pending_approval'),
  approved: getStatusStyles('approved'),
  rejected: getStatusStyles('rejected'),
  returned_for_revision: getStatusStyles('returned_for_revision'),
  issued: getStatusStyles('issued'),
  partially_issued: getStatusStyles('partially_issued'),
} as const;

//  FORM INTERFACES 

export interface StoreRequisitionFormData {
  branch_id?: number;
  department_id?: number;
  purpose: string;
  items: StoreRequisitionItemFormData[];
  save_as_draft?: boolean;
}

export interface StoreRequisitionItemFormData {
  inventory_item_id: number | null;
  quantity_requested: number;
}

export interface StoreRequisitionActionFormData {
  action: StoreRequisitionAction;
  rejection_reason?: string;
  items?: StoreRequisitionIssueItemData[];
}

export interface StoreRequisitionIssueItemData {
  id: number; 
  quantity_issued: number;
}

//  API RESPONSE INTERFACES 

export interface StoreRequisitionListResponse {
  data: StoreRequisition[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
}

export interface StoreRequisitionCreateResponse {
  requisition: StoreRequisition;
  message: string;
}

//  COMPONENT PROP INTERFACES 

export interface StoreRequisitionCardProps {
  requisition: StoreRequisition;
  showActions?: boolean;
  onEdit?: (requisition: StoreRequisition) => void;
  onView?: (requisition: StoreRequisition) => void;
  onSubmit?: (requisition: StoreRequisition) => void;
}

export interface StoreRequisitionStatusBadgeProps {
  status: StoreRequisitionStatus;
  className?: string;
}

export interface StoreRequisitionActionsProps {
  requisition: StoreRequisition;
  onSubmit?: (requisition: StoreRequisition) => void;
  onApprove?: (requisition: StoreRequisition) => void;
  onReject?: (requisition: StoreRequisition, reason: string) => void;
  onIssue?: (requisition: StoreRequisition, items: StoreRequisitionIssueItemData[]) => void;
}

//  FILTER AND SEARCH INTERFACES 

export interface StoreRequisitionFilters {
  status?: StoreRequisitionStatus[];
  department_id?: number[];
  branch_id?: number[];
  requester_user_id?: number[];
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface StoreRequisitionSortOptions {
  field: 'created_at' | 'requested_at' | 'status' | 'purpose';
  direction: 'asc' | 'desc';
}

//  UI STATE INTERFACES 

export interface StoreRequisitionLoadingStates {
  submitting: boolean;
  loading: boolean;
  processing: boolean;
  issuing: boolean;
}

export interface StoreRequisitionErrorStates {
  form?: Record<string, string>;
  api?: string;
  network?: boolean;
}

export interface StoreRequisitionIssueItemData {
  id: number;
  quantity_issued: number;
}

export interface StockValidationError {
  item_id: number;
  item_name: string;
  available: number;
  requested: number;
  message: string;
}

export interface StockValidationWarning {
  item_id: number;
  item_name: string;
  remaining_stock: number;
  reorder_level: number;
  message: string;
}

export interface StockValidationResponse {
  valid: boolean;
  errors: StockValidationError[];
  warnings: StockValidationWarning[];
}
