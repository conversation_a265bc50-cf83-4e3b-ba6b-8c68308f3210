@component('mail::message')

<img src="{{ asset('public/sippar_logo.webp') }}" alt="{{ config('app.name') }} Logo" style="height: 60px; margin-bottom: 20px;">

# Requisition Disbursement Completed

Dear {{ $notifiable->full_name }},

We are pleased to inform you that the funds for your requisition have been successfully disbursed to your account.

**Requisition Details:**
- Reference: #{{ $requisition->requisition_number }}
- Purpose: {{ $requisition->purpose }}
- Amount: KSH {{ number_format($transaction->total_amount, 2) }}
- Disbursement Date: {{ $transaction->updated_at->format('F j, Y') }}
- Transaction ID: {{ $transaction->disbursement_transaction_id ?? 'N/A' }}

**Important Reminder:**
Please remember to upload evidence of your requisition acquisition as soon as possible. This is a crucial step in completing the requisition process and maintaining proper documentation.

To upload your evidence:
1. Click the button below to go to your requisition
2. Navigate to the "Evidence" section
3. Upload relevant documents (receipts, invoices, or proof of purchase)

@component('mail::button', ['url' => route('transactions.show', $transaction->id)])
Upload Evidence Now
@endcomponent

If you have any questions about uploading evidence or need assistance, please don't hesitate to contact the finance department or SIPPAR <NAME_EMAIL>.

Thanks,<br>
{{ config('app.name') }}
@endcomponent 