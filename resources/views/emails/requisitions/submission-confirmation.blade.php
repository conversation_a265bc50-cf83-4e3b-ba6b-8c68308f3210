@component('mail::message')

<img src="{{ asset('public/sippar_logo.webp') }}" alt="{{ config('app.name') }} Logo" style="height: 60px; margin-bottom: 20px;">

# Requisition Submitted Successfully

Dear {{ $notifiable->full_name }},

Your requisition **#{{ $requisition->requisition_number }}** has been submitted successfully and is now pending approval.

## Requisition Details
- **Amount:** KSH {{ number_format($requisition->total_amount, 2) }}
- **Purpose:** {{ $requisition->purpose }}
- **Department:** {{ $requisition->department->name ?? 'N/A' }}
- **Submitted at:** {{ now()->setTimezone('Africa/Nairobi')->format('F j, Y g:i A') }}

## Next Step
Your requisition will be reviewed and a feedback will be given

@component('mail::button', ['url' => $url])
View Requisition
@endcomponent

If you did not create this requisition, please contact the system administrator immediately or SIPPAR <NAME_EMAIL>.

Thanks,<br>
{{ config('app.name') }}

@endcomponent 