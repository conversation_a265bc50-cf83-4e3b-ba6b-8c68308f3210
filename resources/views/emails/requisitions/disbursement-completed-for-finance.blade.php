@component('mail::message')

<img src="{{ asset('public/sippar_logo.webp') }}" alt="{{ config('app.name') }} Logo" style="height: 60px; margin-bottom: 20px;">

# Disbursement Completed Successfully

Dear {{ $notifiable->full_name }},

A disbursement has been completed successfully and is now ready for monitoring.

**Transaction Details:**
- Requisition Number: #{{ $transaction->requisition->requisition_number }}
- Purpose: {{ $transaction->requisition->purpose }}
- Amount Disbursed: KSH {{ number_format($transaction->total_amount, 2) }}
- Requester: {{ $transaction->requisition->requester->full_name }}
- Department: {{ $transaction->requisition->department->name ?? 'N/A' }}
- Transaction ID: {{ $transaction->disbursement_transaction_id ?? 'N/A' }}
- Disbursement Date: {{ $transaction->updated_at->format('F j, Y') }}

**Next Steps:**
The requester has been notified to upload evidence of expenditure. You can monitor the transaction status and evidence submission through the system.

@component('mail::button', ['url' => route('transactions.show', $transaction->id)])
View Transaction Details
@endcomponent

If you have any questions or need assistance, please contact SIPPAR <NAME_EMAIL>.

Thanks,<br>
{{ config('app.name') }}
@endcomponent