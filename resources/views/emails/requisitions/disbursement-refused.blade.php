@component('mail::message')

<img src="{{ asset('public/sippar_logo.webp') }}" alt="{{ config('app.name') }} Logo" style="height: 60px; margin-bottom: 20px;">

# Requisition Disbursement Refused

Dear {{ $user->full_name }},

We regret to inform you that the disbursement for your requisition "**{{ $requisition->title }}**" has been refused by the finance manager.

**Reason for Refusal:**
{{ $reason }}

**Requisition Details:**
- Reference Number: {{ $requisition->reference_number }}
- Amount: {{ $requisition->formatted_total_amount }}
- Submitted on: {{ $requisition->created_at->format('F j, Y') }}

If you have any questions about this decision, please contact the finance department for clarification or SIPPAR <NAME_EMAIL>.

@component('mail::button', ['url' => route('requisitions.show', $requisition->id)])
View Requisition
@endcomponent

Thanks,<br>
{{ config('app.name') }}
@endcomponent 