@component('mail::message')

<img src="{{ asset('public/sippar_logo.webp') }}" alt="{{ config('app.name') }} Logo" style="height: 60px; margin-bottom: 20px;">

# Low Cash Float Alert

@php
    $fullName = isset($notifiable) && isset($notifiable->full_name) ? $notifiable->full_name : 'Finance Manager';
@endphp
Dear {{ $fullName }},

This is to notify you that a cash float is below the alert threshold and requires your attention.

## Cash Float Details

**Cash Float:** {{ $cashFloat->name }}  
**Current Balance:** {{ number_format($currentBalance, 2) }}  
**Alert Threshold:** {{ number_format($alertThreshold, 2) }}  
**Percentage Remaining:** {{ number_format($percentageRemaining, 1) }}%

@if($cashFloat->organization)
**Organization:** {{ $cashFloat->organization->name }}
@endif

@if($cashFloat->branch)
**Branch:** {{ $cashFloat->branch->name }}
@endif

@component('mail::button', ['url' => $actionUrl])
View Cash Float
@endcomponent

Please take appropriate action to replenish this cash float to ensure smooth operations.

If you have any questions or need assistance, please contact SIPPAR <NAME_EMAIL>.

Thanks,<br>
{{ config('app.name') }}
@endcomponent