<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_requisitions', function (Blueprint $table) {
            $table->id();
            
            // Multi-tenant structure
            $table->foreignId('organization_id')->constrained()->cascadeOnDelete();
            $table->foreignId('branch_id')->constrained()->cascadeOnDelete();
            $table->foreignId('department_id')->constrained()->cascadeOnDelete();
            $table->foreignId('requester_user_id')->constrained('users')->cascadeOnDelete();
            
            // Approval workflow
            $table->foreignId('approval_workflow_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('current_approval_step_id')->nullable()->constrained('approval_workflow_steps')->nullOnDelete();
            
            // Core fields
            $table->string('requisition_number')->unique();
            $table->text('purpose');
            $table->date('required_by_date');
            
            // Financial
            $table->decimal('estimated_total_amount', 15, 2)->default(0);
            $table->string('budget_code')->nullable();
            
            // Status and classification
            $table->enum('status', [
                'draft', 
                'pending_approval', 
                'approved', 
                'rejected', 
                'converted_to_tender', 
                'cancelled'
            ])->default('draft');
            $table->enum('requisition_type', [
                'store_replenishment', 
                'capital_expenditure', 
                'operational'
            ])->default('operational');
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['organization_id', 'status']);
            $table->index(['requester_user_id', 'status']);
            $table->index(['branch_id', 'status']);
            $table->index(['department_id', 'status']);
            $table->index('required_by_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_requisitions');
    }
};
