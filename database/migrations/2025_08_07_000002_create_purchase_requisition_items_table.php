<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_requisition_items', function (Blueprint $table) {
            $table->id();
            
            // Foreign keys
            $table->foreignId('purchase_requisition_id')->constrained()->cascadeOnDelete();
            $table->foreignId('inventory_item_id')->nullable()->constrained()->nullOnDelete();
            
            // Core fields
            $table->string('item_name');
            $table->text('description');
            $table->text('specifications')->nullable();
            $table->decimal('quantity', 10, 2);
            $table->string('unit_of_measure', 50);
            
            // Financial
            $table->decimal('estimated_unit_price', 15, 2);
            $table->decimal('estimated_total_price', 15, 2);
            
            // Status
            $table->enum('urgency_level', ['low', 'medium', 'high', 'critical'])->default('medium');
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index('purchase_requisition_id');
            $table->index('inventory_item_id');
            $table->index('urgency_level');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_requisition_items');
    }
};
