<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('suppliers', function (Blueprint $table) {
            $table->id();
            
            $table->string('name');
            $table->string('contact_person')->nullable();
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            
            $table->string('tax_number')->nullable();
            $table->string('registration_number')->nullable();
            
            $table->enum('status', ['active', 'inactive', 'blacklisted'])->default('active');
            $table->boolean('is_verified')->default(false);
            $table->timestamp('verification_date')->nullable();
            
            $table->decimal('overall_rating', 3, 2)->default(0.00); 
            $table->integer('total_orders')->default(0);
            $table->integer('successful_deliveries')->default(0);
            
            $table->text('company_description')->nullable();
            $table->year('established_year')->nullable();
            $table->string('website')->nullable();
            
            $table->string('password')->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->timestamp('last_login_at')->nullable();
            $table->rememberToken();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['status', 'is_verified']);
            $table->index('overall_rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('suppliers');
    }
};
