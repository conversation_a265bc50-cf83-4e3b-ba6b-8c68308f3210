<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * This migration ensures that existing installations get purchase requisition permissions
     * added to their roles by running the PurchaseRequisitionPermissionsSeeder.
     */
    public function up(): void
    {
        // Run the purchase requisition permissions seeder to add new permissions
        \Artisan::call('db:seed', [
            '--class' => 'Database\\Seeders\\PurchaseRequisitionPermissionsSeeder'
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * Note: We don't remove permissions in down() to avoid breaking functionality
     * Purchase requisition permissions can be manually removed if needed.
     */
    public function down(): void
    {
        // Intentionally left empty - removing permissions could break functionality
        // If rollback is needed, manually remove purchase requisition permissions from roles
    }
};
