<?php
/*
* date: 2025-07-03
* description: Factory for ApprovalWorkflowStep model
* file: database/factories/ApprovalWorkflowStepFactory.php
*/

namespace Database\Factories;

use App\Models\ApprovalWorkflowStep;
use App\Models\ApprovalWorkflow;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ApprovalWorkflowStepFactory extends Factory
{
    protected $model = ApprovalWorkflowStep::class;

    public function definition(): array
    {
        return [
            'approval_workflow_id' => ApprovalWorkflow::factory(),
            'step_number' => 1,
            'role_id' => null, // Role::factory(),
            'approver_user_id' => User::factory(),
            'description' => $this->faker->sentence(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    public function withoutRole(): static
    {
        return $this->state(fn () => ['role_id' => null]);
    }

    public function withoutApprover(): static
    {
        return $this->state(fn () => ['approver_user_id' => null]);
    }
}
