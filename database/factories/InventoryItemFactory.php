<?php

namespace Database\Factories;

use App\Models\Branch;
use App\Models\InventoryItem;
use App\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\InventoryItem>
 */
class InventoryItemFactory extends Factory
{
    protected $model = InventoryItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'branch_id' => Branch::factory(),
            'sku' => $this->faker->unique()->regexify('[A-Z]{3}-[0-9]{3}'),
            'name' => $this->faker->words(2, true),
            'description' => $this->faker->sentence(),
            'unit_of_measure' => $this->faker->randomElement(['pieces', 'kg', 'liters', 'meters', 'boxes', 'packets']),
            'quantity_on_hand' => $this->faker->randomFloat(2, 0, 1000),
            'reorder_level' => $this->faker->randomFloat(2, 5, 50),
            'last_low_stock_alert_sent_at' => null,
        ];
    }

    /**
     * Indicate that the inventory item is low on stock.
     */
    public function lowStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'quantity_on_hand' => $this->faker->randomFloat(2, 0, 5),
            'reorder_level' => $this->faker->randomFloat(2, 10, 20),
        ]);
    }

    /**
     * Indicate that the inventory item is out of stock.
     */
    public function outOfStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'quantity_on_hand' => 0,
        ]);
    }

    /**
     * Indicate that the inventory item has high stock.
     */
    public function highStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'quantity_on_hand' => $this->faker->randomFloat(2, 500, 2000),
            'reorder_level' => $this->faker->randomFloat(2, 10, 50),
        ]);
    }

    /**
     * Indicate that the inventory item uses discrete units.
     */
    public function discreteUnit(): static
    {
        return $this->state(fn (array $attributes) => [
            'unit_of_measure' => $this->faker->randomElement(['pieces', 'items', 'boxes', 'packets', 'bottles']),
        ]);
    }

    /**
     * Indicate that the inventory item uses continuous units.
     */
    public function continuousUnit(): static
    {
        return $this->state(fn (array $attributes) => [
            'unit_of_measure' => $this->faker->randomElement(['kg', 'liters', 'meters', 'grams', 'ml']),
        ]);
    }
}
