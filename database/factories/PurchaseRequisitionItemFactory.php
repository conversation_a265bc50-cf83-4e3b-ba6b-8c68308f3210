<?php

namespace Database\Factories;

use App\Models\InventoryItem;
use App\Models\PurchaseRequisition;
use App\Models\PurchaseRequisitionItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PurchaseRequisitionItem>
 */
class PurchaseRequisitionItemFactory extends Factory
{
    protected $model = PurchaseRequisitionItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $quantity = $this->faker->randomFloat(2, 1, 100);
        $unitPrice = $this->faker->randomFloat(2, 10, 1000);
        
        return [
            'purchase_requisition_id' => PurchaseRequisition::factory(),
            'inventory_item_id' => $this->faker->optional(0.7)->randomElement([
                InventoryItem::factory(),
                null
            ]),
            'item_name' => $this->faker->words(3, true),
            'description' => $this->faker->sentence(),
            'specifications' => $this->faker->optional()->paragraph(),
            'quantity' => $quantity,
            'unit_of_measure' => $this->faker->randomElement(['pieces', 'kg', 'liters', 'meters', 'boxes', 'packets']),
            'estimated_unit_price' => $unitPrice,
            'estimated_total_price' => $quantity * $unitPrice,
            'urgency_level' => $this->faker->randomElement(['low', 'medium', 'high', 'critical']),
        ];
    }

    /**
     * Indicate that the item has low urgency.
     */
    public function lowUrgency(): static
    {
        return $this->state(fn (array $attributes) => [
            'urgency_level' => 'low',
        ]);
    }

    /**
     * Indicate that the item has medium urgency.
     */
    public function mediumUrgency(): static
    {
        return $this->state(fn (array $attributes) => [
            'urgency_level' => 'medium',
        ]);
    }

    /**
     * Indicate that the item has high urgency.
     */
    public function highUrgency(): static
    {
        return $this->state(fn (array $attributes) => [
            'urgency_level' => 'high',
        ]);
    }

    /**
     * Indicate that the item has critical urgency.
     */
    public function criticalUrgency(): static
    {
        return $this->state(fn (array $attributes) => [
            'urgency_level' => 'critical',
        ]);
    }

    /**
     * Indicate that the item is linked to an inventory item.
     */
    public function withInventoryItem(): static
    {
        return $this->state(fn (array $attributes) => [
            'inventory_item_id' => InventoryItem::factory(),
        ]);
    }

    /**
     * Indicate that the item is not linked to an inventory item.
     */
    public function withoutInventoryItem(): static
    {
        return $this->state(fn (array $attributes) => [
            'inventory_item_id' => null,
        ]);
    }
}
