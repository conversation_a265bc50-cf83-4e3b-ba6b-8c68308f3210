<?php

namespace Database\Factories;

use App\Models\Role;
use Illuminate\Database\Eloquent\Factories\Factory;

class RoleFactory extends Factory
{
    protected $model = Role::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->jobTitle,
            'guard_name' => 'web',
            'description' => $this->faker->sentence(),
            'organization_id' => null, // if applicable in your DB
        ];
    }
}
