<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Supplier>
 */
class SupplierFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $totalOrders = $this->faker->numberBetween(0, 100);
        $successfulDeliveries = $this->faker->numberBetween(0, $totalOrders);
        
        return [
            'name' => $this->faker->company(),
            'contact_person' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'address' => $this->faker->address(),
            'tax_number' => 'KRA' . $this->faker->numerify('##########'),
            'registration_number' => 'REG' . $this->faker->numerify('########'),
            'status' => $this->faker->randomElement(['active', 'inactive', 'blacklisted']),
            'is_verified' => $this->faker->boolean(70), 
            'verification_date' => $this->faker->optional(0.7)->dateTimeBetween('-2 years', 'now'),
            'overall_rating' => $this->faker->randomFloat(2, 0, 5),
            'total_orders' => $totalOrders,
            'successful_deliveries' => $successfulDeliveries,
            'company_description' => $this->faker->optional()->paragraph(),
            'established_year' => $this->faker->optional()->numberBetween(1980, 2023),
            'website' => $this->faker->optional()->url(),
            'password' => Hash::make('password'),
            'email_verified_at' => $this->faker->optional(0.8)->dateTimeBetween('-1 year', 'now'),
            'last_login_at' => $this->faker->optional(0.6)->dateTimeBetween('-3 months', 'now'),
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Indicate that the supplier is verified.
     */
    public function verified(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_verified' => true,
            'verification_date' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the supplier is unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_verified' => false,
            'verification_date' => null,
            'email_verified_at' => null,
        ]);
    }

    /**
     * Indicate that the supplier is blacklisted.
     */
    public function blacklisted(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'blacklisted',
            'is_verified' => false,
        ]);
    }

    /**
     * Indicate that the supplier is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Indicate that the supplier has high performance.
     */
    public function highPerformance(): static
    {
        return $this->state(function (array $attributes) {
            $totalOrders = $this->faker->numberBetween(50, 200);
            $successfulDeliveries = $this->faker->numberBetween(
                (int)($totalOrders * 0.85), 
                $totalOrders
            );
            
            return [
                'overall_rating' => $this->faker->randomFloat(2, 4.0, 5.0),
                'total_orders' => $totalOrders,
                'successful_deliveries' => $successfulDeliveries,
                'is_verified' => true,
                'status' => 'active',
            ];
        });
    }

    /**
     * Indicate that the supplier is new with no order history.
     */
    public function newSupplier(): static
    {
        return $this->state(fn (array $attributes) => [
            'overall_rating' => 0.00,
            'total_orders' => 0,
            'successful_deliveries' => 0,
            'last_login_at' => null,
        ]);
    }
}
