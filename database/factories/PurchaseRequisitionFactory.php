<?php

namespace Database\Factories;

use App\Models\ApprovalWorkflow;
use App\Models\Branch;
use App\Models\Department;
use App\Models\Organization;
use App\Models\PurchaseRequisition;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PurchaseRequisition>
 */
class PurchaseRequisitionFactory extends Factory
{
    protected $model = PurchaseRequisition::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'branch_id' => Branch::factory(),
            'department_id' => Department::factory(),
            'requester_user_id' => User::factory(),
            'approval_workflow_id' => null, // Will be set by relationships if needed
            'current_approval_step_id' => null,
            'requisition_number' => $this->generateRequisitionNumber(),
            'purpose' => $this->faker->sentence(),
            'required_by_date' => $this->faker->dateTimeBetween('now', '+3 months')->format('Y-m-d'),
            'estimated_total_amount' => $this->faker->randomFloat(2, 100, 50000),
            'budget_code' => $this->faker->optional()->regexify('[A-Z]{2}[0-9]{4}'),
            'status' => $this->faker->randomElement(['draft', 'pending_approval', 'approved', 'rejected', 'converted_to_tender', 'cancelled']),
            'requisition_type' => $this->faker->randomElement(['store_replenishment', 'capital_expenditure', 'operational']),
        ];
    }

    /**
     * Generate a unique requisition number.
     */
    private function generateRequisitionNumber(): string
    {
        $prefix = 'PR';
        $year = date('Y');
        $month = date('m');
        $sequence = $this->faker->unique()->numberBetween(1, 9999);
        
        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Indicate that the purchase requisition is in draft status.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
        ]);
    }

    /**
     * Indicate that the purchase requisition is pending approval.
     */
    public function pendingApproval(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending_approval',
        ]);
    }

    /**
     * Indicate that the purchase requisition is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
        ]);
    }

    /**
     * Indicate that the purchase requisition is for store replenishment.
     */
    public function storeReplenishment(): static
    {
        return $this->state(fn (array $attributes) => [
            'requisition_type' => 'store_replenishment',
        ]);
    }

    /**
     * Indicate that the purchase requisition is for capital expenditure.
     */
    public function capitalExpenditure(): static
    {
        return $this->state(fn (array $attributes) => [
            'requisition_type' => 'capital_expenditure',
        ]);
    }

    /**
     * Indicate that the purchase requisition is operational.
     */
    public function operational(): static
    {
        return $this->state(fn (array $attributes) => [
            'requisition_type' => 'operational',
        ]);
    }
}
