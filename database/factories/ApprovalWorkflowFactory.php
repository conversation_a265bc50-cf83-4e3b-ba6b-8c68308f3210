<?php
/*
* date: 2025-07-03
* description: Factory for ApprovalWorkflow model
* file: database/factories/ApprovalWorkflowFactory.php
*/

namespace Database\Factories;

use App\Models\ApprovalWorkflow;
use App\Models\Organization;
use App\Models\Branch;
use App\Models\Department;
use Illuminate\Database\Eloquent\Factories\Factory;

class ApprovalWorkflowFactory extends Factory
{
    protected $model = ApprovalWorkflow::class;

    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'branch_id' => Branch::factory(),
            'department_id' => Department::factory(),
            'name' => $this->faker->words(3, true),
            'is_default' => false,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    public function default(): static
    {
        return $this->state(fn () => ['is_default' => true]);
    }
}
