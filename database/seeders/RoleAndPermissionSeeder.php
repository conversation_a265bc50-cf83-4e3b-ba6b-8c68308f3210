<?php

namespace Database\Seeders;

use Spatie\Permission\Models\Role;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\PermissionRegistrar;

class RoleAndPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions with descriptions
        $permissions = [
            // Organization permissions
            ['name' => 'manage-organizations', 'description' => 'Manage all aspects of organizations'],
            ['name' => 'view-organizations', 'description' => 'View organization details'],
            ['name' => 'create-organizations', 'description' => 'Create new organizations'],
            ['name' => 'edit-organizations', 'description' => 'Edit existing organizations'],
            ['name' => 'delete-organizations', 'description' => 'Delete organizations'],

            // Branch permissions
            ['name' => 'manage-branches', 'description' => 'Manage all aspects of branches'],
            ['name' => 'view-branches', 'description' => 'View branch details'],
            ['name' => 'create-branches', 'description' => 'Create new branches'],
            ['name' => 'edit-branches', 'description' => 'Edit existing branches'],
            ['name' => 'delete-branches', 'description' => 'Delete branches'],

            // Department permissions
            ['name' => 'manage-departments', 'description' => 'Manage all aspects of departments'],
            ['name' => 'view-departments', 'description' => 'View department details'],
            ['name' => 'create-departments', 'description' => 'Create new departments'],
            ['name' => 'edit-departments', 'description' => 'Edit existing departments'],
            ['name' => 'delete-departments', 'description' => 'Delete departments'],

            // User permissions
            ['name' => 'manage-users', 'description' => 'Manage all aspects of users'],
            ['name' => 'view-users', 'description' => 'View user details'],
            ['name' => 'create-users', 'description' => 'Create new users'],
            ['name' => 'edit-users', 'description' => 'Edit existing users'],
            ['name' => 'delete-users', 'description' => 'Delete users'],

            // Role permissions
            ['name' => 'manage-roles', 'description' => 'Manage all aspects of roles'],
            ['name' => 'view-roles', 'description' => 'View role details'],
            ['name' => 'create-roles', 'description' => 'Create new roles'],
            ['name' => 'edit-roles', 'description' => 'Edit existing roles'],
            ['name' => 'delete-roles', 'description' => 'Delete roles'],

            // Requisition permissions
            ['name' => 'manage-requisitions', 'description' => 'Manage all aspects of requisitions'],
            ['name' => 'create-requisition', 'description' => 'Create new requisitions'],
            ['name' => 'view-requisition', 'description' => 'View requisition details'],
            ['name' => 'edit-requisition', 'description' => 'Edit existing requisitions'],
            ['name' => 'delete-requisition', 'description' => 'Delete requisitions'],
            ['name' => 'approve-requisition', 'description' => 'Approve requisitions'],
            ['name' => 'reject-requisition', 'description' => 'Reject requisitions'],
            ['name' => 'review-requisition', 'description' => 'Review requisitions (acknowledge before approval)'],
            ['name' => 'approver', 'description' => 'User can approve requisitions and view requisitions they are involved in as approvers'],
            ['name' => 'amend-requisition', 'description' => 'Amend requisitions (make changes to submitted requisitions)'],

            // Float management permissions
            ['name' => 'manage-floats', 'description' => 'Manage all aspects of cash floats'],
            ['name' => 'create-float', 'description' => 'Create new cash floats'],
            ['name' => 'view-float', 'description' => 'View cash float details'],
            ['name' => 'edit-float', 'description' => 'Edit existing cash floats'],
            ['name' => 'delete-float', 'description' => 'Delete cash floats'],
            ['name' => 'issue-float', 'description' => 'Issue cash floats to departments or individuals'],
            ['name' => 'receive-float', 'description' => 'Receive cash floats'],
            ['name' => 'reconcile-float', 'description' => 'Reconcile cash floats'],
            ['name' => 'view-float-history', 'description' => 'View cash float transaction history'],

            // Expense category permissions
            ['name' => 'manage-expense-categories', 'description' => 'Manage all aspects of expense categories'],
            ['name' => 'create-expense-category', 'description' => 'Create new expense categories'],
            ['name' => 'view-expense-category', 'description' => 'View expense category details'],
            ['name' => 'edit-expense-category', 'description' => 'Edit existing expense categories'],
            ['name' => 'delete-expense-category', 'description' => 'Delete expense categories'],

            // Chart of accounts permissions
            ['name' => 'manage-chart-of-accounts', 'description' => 'Manage all aspects of chart of accounts'],
            ['name' => 'view-chart-of-accounts', 'description' => 'View chart of accounts'],
            ['name' => 'create-chart-of-accounts', 'description' => 'Create new chart of accounts'],
            ['name' => 'edit-chart-of-accounts', 'description' => 'Edit existing chart of accounts'],
            ['name' => 'delete-chart-of-accounts', 'description' => 'Delete chart of accounts'],

            // Report permissions
            ['name' => 'manage-reports', 'description' => 'Manage all aspects of reports'],
            ['name' => 'view-reports', 'description' => 'View reports'],
            ['name' => 'export-reports', 'description' => 'Export reports to Excel/PDF'],
            ['name' => 'view-audit-logs', 'description' => 'View audit logs of changes'],

            // Reconciliation permissions
            ['name' => 'manage-reconciliations', 'description' => 'Manage all aspects of reconciliations'],
            ['name' => 'create-reconciliation', 'description' => 'Create new reconciliations'],
            ['name' => 'view-reconciliation', 'description' => 'View reconciliation details'],
            ['name' => 'approve-reconciliation', 'description' => 'Approve reconciliations'],
            ['name' => 'reject-reconciliation', 'description' => 'Reject reconciliations'],

            // Transaction permissions
            ['name' => 'manage-transactions', 'description' => 'Manage all aspects of transactions'],
            ['name' => 'view-transactions', 'description' => 'View transaction details'],
            ['name' => 'create-transactions', 'description' => 'Create new transactions'],
            ['name' => 'edit-transactions', 'description' => 'Edit existing transactions'],
            ['name' => 'delete-transactions', 'description' => 'Delete transactions'],
        ];

        foreach ($permissions as $permission) {
            // Check if permission already exists
            $existingPermission = Permission::where('name', $permission['name'])->first();

            if ($existingPermission) {
                // Update the description
                $existingPermission->description = $permission['description'];
                $existingPermission->save();
            } else {
                // Create new permission
                Permission::create($permission);
            }
        }

        // Create system roles
        $platformAdminRole = Role::firstOrCreate(
            ['name' => 'Platform Admin'],
            ['description' => 'Super administrator with access to all system features']
        );

        // Assign all permissions to Platform Admin
        $platformAdminRole->givePermissionTo(Permission::all());

        // Create organization-level roles
        $organizationAdminRole = Role::firstOrCreate(
            ['name' => 'Organization Admin', 'organization_id' => null],
            ['description' => 'Administrator for an organization with access to manage all organization features']
        );

        $organizationAdminRole->givePermissionTo([
            // Organization structure management
            'manage-branches',
            'view-branches',
            'create-branches',
            'edit-branches',
            'delete-branches',
            'manage-departments',
            'view-departments',
            'create-departments',
            'edit-departments',
            'delete-departments',

            // User management
            'manage-users',
            'view-users',
            'create-users',
            'edit-users',
            'delete-users',

            // Role and permission management
            'manage-roles',
            'view-roles',
            'create-roles',
            'edit-roles',
            'delete-roles',

            // Petty cash management oversight
            'view-requisition',
            'view-float',
            'view-reports',
            'export-reports',
            'view-audit-logs',
            'view-expense-category',
            'view-reconciliation',
            'approver',
            'manage-transactions',

            // Chart of accounts management
            'manage-chart-of-accounts',
            'view-chart-of-accounts',
            'create-chart-of-accounts',
            'edit-chart-of-accounts',
            'delete-chart-of-accounts',
        ]);

        // Create role templates for organization-specific roles
        // These will be used as templates when creating organization-specific roles

        // HOD role template (Reviewer)
        $hodRoleTemplate = Role::firstOrCreate(
            ['name' => 'HOD', 'organization_id' => null],
            ['description' => 'Head of Department with access to review and manage department requisitions']
        );

        $hodRoleTemplate->givePermissionTo([
            'view-departments',
            'view-users',
            'view-requisition',
            'review-requisition',
            'approve-requisition',
            'reject-requisition',
            'view-reports',
            'view-float',
            'approver',
        ]);

        // Finance Manager role template (Approver)
        $financeManagerRoleTemplate = Role::firstOrCreate(
            ['name' => 'Finance Manager', 'organization_id' => null],
            ['description' => 'Finance Manager with access to approve requisitions and manage financial aspects']
        );

        $financeManagerRoleTemplate->givePermissionTo([
            'view-requisition',
            'approve-requisition',
            'reject-requisition',
            'amend-requisition',
            'manage-floats',
            'create-float',
            'view-float',
            'edit-float',
            'issue-float',
            'manage-reports',
            'view-reports',
            'export-reports',
            'view-audit-logs',
            'manage-reconciliations',
            'approve-reconciliation',
            'reject-reconciliation',
            'approver',
            'manage-transactions',
            'view-transactions',
            'create-transactions',
            'edit-transactions',
            'delete-transactions',

            // Chart of accounts management
            'manage-chart-of-accounts',
            'view-chart-of-accounts',
            'create-chart-of-accounts',
            'edit-chart-of-accounts',
            'delete-chart-of-accounts',
        ]);

        // Employee role template (Requestor)
        $employeeRoleTemplate = Role::firstOrCreate(
            ['name' => 'Employee', 'organization_id' => null],
            ['description' => 'Regular employee with ability to create and manage requisitions']
        );

        $employeeRoleTemplate->givePermissionTo([
            'create-requisition',
            'view-requisition',
            'edit-requisition',
            'delete-requisition',
        ]);

        // Cashier role template
        $cashierRoleTemplate = Role::firstOrCreate(
            ['name' => 'Cashier', 'organization_id' => null],
            ['description' => 'Finance team member who handles disbursements and reconciliations']
        );

        $cashierRoleTemplate->givePermissionTo([
            'view-requisition',
            'view-float',
            'issue-float',
            'receive-float',
            'create-reconciliation',
            'view-reconciliation',
            'view-reports',
            'export-reports',
        ]);

        // Admin role template (Finance Admin)
        $adminRoleTemplate = Role::firstOrCreate(
            ['name' => 'Finance Admin', 'organization_id' => null],
            ['description' => 'Finance team administrator with full access to financial features']
        );

        $adminRoleTemplate->givePermissionTo([
            'manage-requisitions',
            'create-requisition',
            'view-requisition',
            'edit-requisition',
            'delete-requisition',
            'approve-requisition',
            'reject-requisition',
            'amend-requisition',
            'manage-floats',
            'create-float',
            'view-float',
            'edit-float',
            'delete-float',
            'issue-float',
            'receive-float',
            'manage-expense-categories',
            'create-expense-category',
            'view-expense-category',
            'edit-expense-category',
            'delete-expense-category',
            'manage-reports',
            'view-reports',
            'export-reports',
            'view-audit-logs',
            'manage-reconciliations',
            'create-reconciliation',
            'view-reconciliation',
            'approve-reconciliation',
            'reject-reconciliation',
            'approver',

            // Chart of accounts management
            'manage-chart-of-accounts',
            'view-chart-of-accounts',
            'create-chart-of-accounts',
            'edit-chart-of-accounts',
            'delete-chart-of-accounts',
        ]);

        // Note: Organization-specific instances of these roles will be created
        // in the RegularUserSeeder when users are assigned to organizations

        // Update existing organization-specific roles with chart of accounts permissions
        $this->updateExistingRolesWithChartOfAccountsPermissions();
    }

    /**
     * Update existing organization-specific roles with chart of accounts permissions.
     */
    private function updateExistingRolesWithChartOfAccountsPermissions(): void
    {
        $chartOfAccountsPermissions = [
            'manage-chart-of-accounts',
            'view-chart-of-accounts',
            'create-chart-of-accounts',
            'edit-chart-of-accounts',
            'delete-chart-of-accounts',
        ];

        // Update existing organization-specific Finance Manager roles
        $existingFinanceManagerRoles = Role::where('name', 'Finance Manager')
            ->whereNotNull('organization_id')
            ->get();

        foreach ($existingFinanceManagerRoles as $role) {
            $role->givePermissionTo($chartOfAccountsPermissions);
        }

        if ($existingFinanceManagerRoles->count() > 0) {
            $this->command->info("Chart of Accounts permissions added to {$existingFinanceManagerRoles->count()} existing Finance Manager roles.");
        }

        // Update existing organization-specific Finance Admin roles
        $existingFinanceAdminRoles = Role::where('name', 'Finance Admin')
            ->whereNotNull('organization_id')
            ->get();

        foreach ($existingFinanceAdminRoles as $role) {
            $role->givePermissionTo($chartOfAccountsPermissions);
        }

        if ($existingFinanceAdminRoles->count() > 0) {
            $this->command->info("Chart of Accounts permissions added to {$existingFinanceAdminRoles->count()} existing Finance Admin roles.");
        }

        // Update existing organization-specific Organization Admin roles
        $existingOrgAdminRoles = Role::where('name', 'Organization Admin')
            ->whereNotNull('organization_id')
            ->get();

        foreach ($existingOrgAdminRoles as $role) {
            $role->givePermissionTo($chartOfAccountsPermissions);
        }

        if ($existingOrgAdminRoles->count() > 0) {
            $this->command->info("Chart of Accounts permissions added to {$existingOrgAdminRoles->count()} existing Organization Admin roles.");
        }
    }
}
