<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RoleAndPermissionSeeder::class,
            TemplateSeeder::class,
            OrganizationSeeder::class,
            BranchSeeder::class,
            DepartmentSeeder::class,
            PlatformAdminSeeder::class,
            RegularUserSeeder::class,
            COASeeder::class,
            DemoOrganizationDefaults::class,
            UpdateRolesWithApproverPermissionSeeder::class,
            StoreInventoryPermissionsSeeder::class,
            PurchaseRequisitionPermissionsSeeder::class,
            WorkflowTemplateSeeder::class,
        ]);
    }
}
