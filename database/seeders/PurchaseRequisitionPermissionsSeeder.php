<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

class PurchaseRequisitionPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        // Define purchase requisition permissions (simplified)
        $permissions = [
            // Core Purchase Requisition permissions (following existing naming pattern)
            ['name' => 'create-purchase-requisitions', 'description' => 'Create and edit purchase requisitions'],
            ['name' => 'view-purchase-requisitions', 'description' => 'View purchase requisition details and lists'],
            ['name' => 'approve-purchase-requisitions', 'description' => 'Approve or reject purchase requisitions'],
            ['name' => 'manage-purchase-requisitions', 'description' => 'Full management of purchase requisitions including deletion and workflow management'],
            ['name' => 'view-purchase-reports', 'description' => 'View and export purchase requisition reports'],
        ];

        // Create or update permissions
        foreach ($permissions as $permission) {
            $existingPermission = Permission::where('name', $permission['name'])->first();

            if ($existingPermission) {
                // Update the description
                $existingPermission->description = $permission['description'];
                $existingPermission->save();
                $this->command->info("Updated permission: {$permission['name']}");
            } else {
                // Create new permission
                Permission::create($permission);
                $this->command->info("Created permission: {$permission['name']}");
            }
        }

        // Assign permissions to existing roles
        $this->assignPermissionsToRoles();
    }

    /**
     * Assign purchase requisition permissions to existing roles
     */
    private function assignPermissionsToRoles(): void
    {
        // Platform Admin gets all permissions (this is a system role, not a template)
        $platformAdminRole = Role::where('name', 'Platform Admin')->first();
        if ($platformAdminRole) {
            $allPermissions = [
                'create-purchase-requisitions',
                'view-purchase-requisitions',
                'approve-purchase-requisitions',
                'manage-purchase-requisitions',
                'view-purchase-reports',
            ];

            $platformAdminRole->givePermissionTo($allPermissions);
            $this->command->info("Assigned all purchase requisition permissions to Platform Admin role");
        }

        // Update role templates (organization_id = null) following established pattern

        // Employee role template - basic purchase requisition permissions
        $employeeRoleTemplate = Role::where('name', 'Employee')
            ->whereNull('organization_id')
            ->first();

        if ($employeeRoleTemplate) {
            $employeeRoleTemplate->givePermissionTo([
                'create-purchase-requisitions',
                'view-purchase-requisitions',
            ]);
            $this->command->info("Updated Employee role template with purchase requisition permissions");
        }

        // HOD role template - can view and approve purchase requisitions
        $hodRoleTemplate = Role::where('name', 'HOD')
            ->whereNull('organization_id')
            ->first();

        if ($hodRoleTemplate) {
            $hodRoleTemplate->givePermissionTo([
                'view-purchase-requisitions',
                'approve-purchase-requisitions',
            ]);
            $this->command->info("Updated HOD role template with purchase requisition permissions");
        }

        // Finance Manager role template - approval and reporting permissions
        $financeManagerRoleTemplate = Role::where('name', 'Finance Manager')
            ->whereNull('organization_id')
            ->first();

        if ($financeManagerRoleTemplate) {
            $financeManagerRoleTemplate->givePermissionTo([
                'view-purchase-requisitions',
                'approve-purchase-requisitions',
                'view-purchase-reports',
            ]);
            $this->command->info("Updated Finance Manager role template with purchase requisition permissions");
        }

        // Finance Admin role template - comprehensive permissions
        $financeAdminRoleTemplate = Role::where('name', 'Finance Admin')
            ->whereNull('organization_id')
            ->first();

        if ($financeAdminRoleTemplate) {
            $financeAdminRoleTemplate->givePermissionTo([
                'create-purchase-requisitions',
                'view-purchase-requisitions',
                'approve-purchase-requisitions',
                'manage-purchase-requisitions',
                'view-purchase-reports',
            ]);
            $this->command->info("Updated Finance Admin role template with purchase requisition permissions");
        }

        // Organization Admin role template - full permissions
        $organizationAdminRoleTemplate = Role::where('name', 'Organization Admin')
            ->whereNull('organization_id')
            ->first();

        if ($organizationAdminRoleTemplate) {
            $organizationAdminRoleTemplate->givePermissionTo([
                'create-purchase-requisitions',
                'view-purchase-requisitions',
                'approve-purchase-requisitions',
                'manage-purchase-requisitions',
                'view-purchase-reports',
            ]);
            $this->command->info("Updated Organization Admin role template with purchase requisition permissions");
        }

        // Update existing organization-specific roles (for existing organizations)
        $this->updateExistingOrganizationRoles();
    }

    /**
     * Update existing organization-specific roles with purchase requisition permissions
     */
    private function updateExistingOrganizationRoles(): void
    {
        // Employee roles - basic purchase requisition permissions
        $orgEmployeeRoles = Role::where('name', 'Employee')
            ->whereNotNull('organization_id')
            ->get();

        foreach ($orgEmployeeRoles as $role) {
            $role->givePermissionTo([
                'create-purchase-requisitions',
                'view-purchase-requisitions',
            ]);
        }

        if ($orgEmployeeRoles->count() > 0) {
            $this->command->info("Updated {$orgEmployeeRoles->count()} existing Employee roles with purchase requisition permissions");
        }

        // HOD roles - approval permissions
        $orgHodRoles = Role::where('name', 'HOD')
            ->whereNotNull('organization_id')
            ->get();

        foreach ($orgHodRoles as $role) {
            $role->givePermissionTo([
                'view-purchase-requisitions',
                'approve-purchase-requisitions',
            ]);
        }

        if ($orgHodRoles->count() > 0) {
            $this->command->info("Updated {$orgHodRoles->count()} existing HOD roles with purchase requisition permissions");
        }

        // Finance Manager roles
        $orgFinanceManagerRoles = Role::where('name', 'Finance Manager')
            ->whereNotNull('organization_id')
            ->get();

        foreach ($orgFinanceManagerRoles as $role) {
            $role->givePermissionTo([
                'view-purchase-requisitions',
                'approve-purchase-requisitions',
                'view-purchase-reports',
            ]);
        }

        if ($orgFinanceManagerRoles->count() > 0) {
            $this->command->info("Updated {$orgFinanceManagerRoles->count()} existing Finance Manager roles with purchase requisition permissions");
        }

        // Finance Admin roles
        $orgFinanceAdminRoles = Role::where('name', 'Finance Admin')
            ->whereNotNull('organization_id')
            ->get();

        foreach ($orgFinanceAdminRoles as $role) {
            $role->givePermissionTo([
                'create-purchase-requisitions',
                'view-purchase-requisitions',
                'approve-purchase-requisitions',
                'manage-purchase-requisitions',
                'view-purchase-reports',
            ]);
        }

        if ($orgFinanceAdminRoles->count() > 0) {
            $this->command->info("Updated {$orgFinanceAdminRoles->count()} existing Finance Admin roles with purchase requisition permissions");
        }

        // Organization Admin roles
        $orgAdminRoles = Role::where('name', 'Organization Admin')
            ->whereNotNull('organization_id')
            ->get();

        foreach ($orgAdminRoles as $role) {
            $role->givePermissionTo([
                'create-purchase-requisitions',
                'view-purchase-requisitions',
                'approve-purchase-requisitions',
                'manage-purchase-requisitions',
                'view-purchase-reports',
            ]);
        }

        if ($orgAdminRoles->count() > 0) {
            $this->command->info("Updated {$orgAdminRoles->count()} existing Organization Admin roles with purchase requisition permissions");
        }
    }
}
