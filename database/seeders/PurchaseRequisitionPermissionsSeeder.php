<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

class PurchaseRequisitionPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        // Define purchase requisition permissions (simplified)
        $permissions = [
            // Core Purchase Requisition permissions (following existing naming pattern)
            ['name' => 'create-purchase-requisitions', 'description' => 'Create and edit purchase requisitions'],
            ['name' => 'view-purchase-requisitions', 'description' => 'View purchase requisition details and lists'],
            ['name' => 'approve-purchase-requisitions', 'description' => 'Approve or reject purchase requisitions'],
            ['name' => 'manage-purchase-requisitions', 'description' => 'Full management of purchase requisitions including deletion and workflow management'],
            ['name' => 'view-purchase-reports', 'description' => 'View and export purchase requisition reports'],
        ];

        // Create or update permissions
        foreach ($permissions as $permission) {
            $existingPermission = Permission::where('name', $permission['name'])->first();

            if ($existingPermission) {
                // Update the description
                $existingPermission->description = $permission['description'];
                $existingPermission->save();
                $this->command->info("Updated permission: {$permission['name']}");
            } else {
                // Create new permission
                Permission::create($permission);
                $this->command->info("Created permission: {$permission['name']}");
            }
        }

        // Assign permissions to existing roles
        $this->assignPermissionsToRoles();
    }

    /**
     * Assign purchase requisition permissions to existing roles
     */
    private function assignPermissionsToRoles(): void
    {
        // Platform Admin gets all permissions
        $platformAdminRole = Role::where('name', 'Platform Admin')->first();
        if ($platformAdminRole) {
            $allPermissions = [
                'create-purchase-requisitions',
                'view-purchase-requisitions',
                'approve-purchase-requisitions',
                'manage-purchase-requisitions',
                'view-purchase-reports',
            ];

            $platformAdminRole->givePermissionTo($allPermissions);
            $this->command->info("Assigned all purchase requisition permissions to Platform Admin role");
        }

        // Organization Admin gets all permissions
        $this->assignToRole('Organization Admin', [
            'create-purchase-requisitions',
            'view-purchase-requisitions',
            'approve-purchase-requisitions',
            'manage-purchase-requisitions',
            'view-purchase-reports',
        ]);

        // Finance Manager gets approval and reporting permissions
        $this->assignToRole('Finance Manager', [
            'view-purchase-requisitions',
            'approve-purchase-requisitions',
            'view-purchase-reports',
        ]);

        // HOD gets approval permissions
        $this->assignToRole('HOD', [
            'view-purchase-requisitions',
            'approve-purchase-requisitions',
        ]);

        // Employee gets creation and viewing permissions
        $this->assignToRole('Employee', [
            'create-purchase-requisitions',
            'view-purchase-requisitions',
        ]);

        // Finance Admin gets comprehensive permissions
        $this->assignToRole('Finance Admin', [
            'create-purchase-requisitions',
            'view-purchase-requisitions',
            'approve-purchase-requisitions',
            'manage-purchase-requisitions',
            'view-purchase-reports',
        ]);

        // Procurement Manager gets specialized permissions
        $this->assignToRole('Procurement Manager', [
            'create-purchase-requisitions',
            'view-purchase-requisitions',
            'approve-purchase-requisitions',
            'manage-purchase-requisitions',
            'view-purchase-reports',
        ]);
    }

    /**
     * Helper method to assign permissions to a role
     */
    private function assignToRole(string $roleName, array $permissions): void
    {
        $roles = Role::where('name', $roleName)->get();
        
        foreach ($roles as $role) {
            $role->givePermissionTo($permissions);
            $this->command->info("Assigned purchase requisition permissions to {$roleName} role (ID: {$role->id})");
        }
    }
}
