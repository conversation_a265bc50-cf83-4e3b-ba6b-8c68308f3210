<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

class PurchaseRequisitionPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        // Define purchase requisition permissions
        $permissions = [
            // Purchase Requisition permissions
            ['name' => 'manage-purchase-requisitions', 'description' => 'Manage all aspects of purchase requisitions'],
            ['name' => 'create-purchase-requisition', 'description' => 'Create new purchase requisitions'],
            ['name' => 'view-purchase-requisitions', 'description' => 'View purchase requisition details and lists'],
            ['name' => 'edit-purchase-requisition', 'description' => 'Edit existing purchase requisitions'],
            ['name' => 'delete-purchase-requisition', 'description' => 'Delete purchase requisitions'],
            ['name' => 'submit-purchase-requisition', 'description' => 'Submit purchase requisitions for approval'],
            ['name' => 'approve-purchase-requisition', 'description' => 'Approve purchase requisitions'],
            ['name' => 'reject-purchase-requisition', 'description' => 'Reject purchase requisitions'],
            ['name' => 'review-purchase-requisition', 'description' => 'Review purchase requisitions (acknowledge before approval)'],
            ['name' => 'amend-purchase-requisition', 'description' => 'Amend purchase requisitions (make changes to submitted requisitions)'],
            ['name' => 'cancel-purchase-requisition', 'description' => 'Cancel purchase requisitions'],
            ['name' => 'convert-to-tender', 'description' => 'Convert purchase requisitions to tender processes'],
            
            // Purchase Requisition Item permissions
            ['name' => 'manage-purchase-requisition-items', 'description' => 'Manage purchase requisition items'],
            ['name' => 'add-purchase-requisition-items', 'description' => 'Add items to purchase requisitions'],
            ['name' => 'edit-purchase-requisition-items', 'description' => 'Edit items in purchase requisitions'],
            ['name' => 'remove-purchase-requisition-items', 'description' => 'Remove items from purchase requisitions'],
            
            // Purchase Requisition Reports and Analytics
            ['name' => 'view-purchase-requisition-reports', 'description' => 'View purchase requisition reports and analytics'],
            ['name' => 'export-purchase-requisition-reports', 'description' => 'Export purchase requisition reports to Excel/PDF'],
            ['name' => 'view-purchase-requisition-analytics', 'description' => 'View purchase requisition analytics and dashboards'],
            
            // Purchase Requisition Workflow permissions
            ['name' => 'manage-purchase-approval-workflows', 'description' => 'Manage purchase requisition approval workflows'],
            ['name' => 'view-purchase-approval-workflows', 'description' => 'View purchase requisition approval workflows'],
            ['name' => 'create-purchase-approval-workflows', 'description' => 'Create purchase requisition approval workflows'],
            ['name' => 'edit-purchase-approval-workflows', 'description' => 'Edit purchase requisition approval workflows'],
            ['name' => 'delete-purchase-approval-workflows', 'description' => 'Delete purchase requisition approval workflows'],
            
            // Purchase Requisition Audit and History
            ['name' => 'view-purchase-requisition-history', 'description' => 'View purchase requisition change history and audit logs'],
            ['name' => 'view-purchase-requisition-audit-logs', 'description' => 'View detailed audit logs for purchase requisitions'],
        ];

        // Create or update permissions
        foreach ($permissions as $permission) {
            $existingPermission = Permission::where('name', $permission['name'])->first();

            if ($existingPermission) {
                // Update the description
                $existingPermission->description = $permission['description'];
                $existingPermission->save();
                $this->command->info("Updated permission: {$permission['name']}");
            } else {
                // Create new permission
                Permission::create($permission);
                $this->command->info("Created permission: {$permission['name']}");
            }
        }

        // Assign permissions to existing roles
        $this->assignPermissionsToRoles();
    }

    /**
     * Assign purchase requisition permissions to existing roles
     */
    private function assignPermissionsToRoles(): void
    {
        // Platform Admin gets all permissions
        $platformAdminRole = Role::where('name', 'Platform Admin')->first();
        if ($platformAdminRole) {
            $allPermissions = [
                'manage-purchase-requisitions',
                'create-purchase-requisition',
                'view-purchase-requisitions',
                'edit-purchase-requisition',
                'delete-purchase-requisition',
                'submit-purchase-requisition',
                'approve-purchase-requisition',
                'reject-purchase-requisition',
                'review-purchase-requisition',
                'amend-purchase-requisition',
                'cancel-purchase-requisition',
                'convert-to-tender',
                'manage-purchase-requisition-items',
                'add-purchase-requisition-items',
                'edit-purchase-requisition-items',
                'remove-purchase-requisition-items',
                'view-purchase-requisition-reports',
                'export-purchase-requisition-reports',
                'view-purchase-requisition-analytics',
                'manage-purchase-approval-workflows',
                'view-purchase-approval-workflows',
                'create-purchase-approval-workflows',
                'edit-purchase-approval-workflows',
                'delete-purchase-approval-workflows',
                'view-purchase-requisition-history',
                'view-purchase-requisition-audit-logs',
            ];
            
            $platformAdminRole->givePermissionTo($allPermissions);
            $this->command->info("Assigned all purchase requisition permissions to Platform Admin role");
        }

        // Organization Admin gets most permissions except platform-level management
        $this->assignToRole('Organization Admin', [
            'manage-purchase-requisitions',
            'create-purchase-requisition',
            'view-purchase-requisitions',
            'edit-purchase-requisition',
            'delete-purchase-requisition',
            'submit-purchase-requisition',
            'approve-purchase-requisition',
            'reject-purchase-requisition',
            'review-purchase-requisition',
            'amend-purchase-requisition',
            'cancel-purchase-requisition',
            'convert-to-tender',
            'manage-purchase-requisition-items',
            'add-purchase-requisition-items',
            'edit-purchase-requisition-items',
            'remove-purchase-requisition-items',
            'view-purchase-requisition-reports',
            'export-purchase-requisition-reports',
            'view-purchase-requisition-analytics',
            'manage-purchase-approval-workflows',
            'view-purchase-approval-workflows',
            'create-purchase-approval-workflows',
            'edit-purchase-approval-workflows',
            'delete-purchase-approval-workflows',
            'view-purchase-requisition-history',
            'view-purchase-requisition-audit-logs',
        ]);

        // Finance Manager gets approval and management permissions
        $this->assignToRole('Finance Manager', [
            'view-purchase-requisitions',
            'approve-purchase-requisition',
            'reject-purchase-requisition',
            'review-purchase-requisition',
            'amend-purchase-requisition',
            'cancel-purchase-requisition',
            'convert-to-tender',
            'view-purchase-requisition-reports',
            'export-purchase-requisition-reports',
            'view-purchase-requisition-analytics',
            'view-purchase-requisition-history',
            'view-purchase-requisition-audit-logs',
        ]);

        // HOD gets review and approval permissions
        $this->assignToRole('HOD', [
            'view-purchase-requisitions',
            'approve-purchase-requisition',
            'reject-purchase-requisition',
            'review-purchase-requisition',
            'view-purchase-requisition-reports',
            'view-purchase-requisition-history',
        ]);

        // Employee gets basic creation and viewing permissions
        $this->assignToRole('Employee', [
            'create-purchase-requisition',
            'view-purchase-requisitions',
            'edit-purchase-requisition',
            'submit-purchase-requisition',
            'add-purchase-requisition-items',
            'edit-purchase-requisition-items',
            'remove-purchase-requisition-items',
        ]);

        // Finance Admin gets comprehensive permissions
        $this->assignToRole('Finance Admin', [
            'manage-purchase-requisitions',
            'create-purchase-requisition',
            'view-purchase-requisitions',
            'edit-purchase-requisition',
            'delete-purchase-requisition',
            'submit-purchase-requisition',
            'approve-purchase-requisition',
            'reject-purchase-requisition',
            'review-purchase-requisition',
            'amend-purchase-requisition',
            'cancel-purchase-requisition',
            'convert-to-tender',
            'manage-purchase-requisition-items',
            'add-purchase-requisition-items',
            'edit-purchase-requisition-items',
            'remove-purchase-requisition-items',
            'view-purchase-requisition-reports',
            'export-purchase-requisition-reports',
            'view-purchase-requisition-analytics',
            'view-purchase-approval-workflows',
            'view-purchase-requisition-history',
            'view-purchase-requisition-audit-logs',
        ]);

        // Procurement Manager (if exists) gets specialized permissions
        $this->assignToRole('Procurement Manager', [
            'manage-purchase-requisitions',
            'create-purchase-requisition',
            'view-purchase-requisitions',
            'edit-purchase-requisition',
            'submit-purchase-requisition',
            'approve-purchase-requisition',
            'reject-purchase-requisition',
            'review-purchase-requisition',
            'amend-purchase-requisition',
            'cancel-purchase-requisition',
            'convert-to-tender',
            'manage-purchase-requisition-items',
            'add-purchase-requisition-items',
            'edit-purchase-requisition-items',
            'remove-purchase-requisition-items',
            'view-purchase-requisition-reports',
            'export-purchase-requisition-reports',
            'view-purchase-requisition-analytics',
            'view-purchase-requisition-history',
            'view-purchase-requisition-audit-logs',
        ]);
    }

    /**
     * Helper method to assign permissions to a role
     */
    private function assignToRole(string $roleName, array $permissions): void
    {
        $roles = Role::where('name', $roleName)->get();
        
        foreach ($roles as $role) {
            $role->givePermissionTo($permissions);
            $this->command->info("Assigned purchase requisition permissions to {$roleName} role (ID: {$role->id})");
        }
    }
}
