<?php

namespace App\Services\StoreRequisition;

use App\Models\StoreRequisition;
use App\Models\StoreRequisitionItem;
use App\Models\InventoryItem;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StoreRequisitionService
{
    public function createRequisition(array $data): StoreRequisition
    {
        $user = Auth::user();
        $organization = $user->organizations()->first();
        
        if (!$organization) {
            throw new \Exception('User must belong to an organization');
        }

        $isDraft = $data['save_as_draft'] ?? false;
        $status = $isDraft ? StoreRequisition::STATUS_DRAFT : StoreRequisition::STATUS_PENDING_APPROVAL;

        DB::beginTransaction();
        try {
            $requisition = StoreRequisition::create([
                'organization_id' => $organization->id,
                'branch_id' => $data['branch_id'],
                'department_id' => $data['department_id'],
                'requester_user_id' => Auth::id(),
                'purpose' => $data['purpose'],
                'status' => $status,
                'requested_at' => $isDraft ? null : now(),
            ]);

            foreach ($data['items'] as $item) {
                $requisition->items()->create([
                    'inventory_item_id' => $item['inventory_item_id'],
                    'quantity_requested' => $item['quantity_requested'],
                ]);
            }

            \App\Models\StoreRequisitionHistory::createRecord(
                $requisition->id,
                Auth::id(),
                $isDraft ? 'created_as_draft' : 'created_and_submitted',
                $isDraft ? 'Store requisition saved as draft' : 'Store requisition created and submitted for approval'
            );

            DB::commit();
            return $requisition;
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to create store requisition: ' . $e->getMessage());
            throw $e;
        }
    }

    public function updateRequisition(StoreRequisition $requisition, array $data): StoreRequisition
    {
        $user = Auth::user();
        $isDraft = $data['save_as_draft'] ?? false;

        DB::beginTransaction();
        try {
            $originalData = [
                'purpose' => $requisition->purpose,
                'branch_id' => $requisition->branch_id,
                'department_id' => $requisition->department_id,
                'items' => $requisition->items->map(function ($item) {
                    return [
                        'inventory_item_id' => $item->inventory_item_id,
                        'quantity_requested' => $item->quantity_requested,
                    ];
                })->toArray(),
            ];

            $updateData = [
                'branch_id' => $data['branch_id'],
                'department_id' => $data['department_id'],
                'purpose' => $data['purpose'],
            ];

            if ($isDraft) {
                $updateData['status'] = StoreRequisition::STATUS_DRAFT;
                $updateData['requested_at'] = null;
            } else {
                $updateData['status'] = StoreRequisition::STATUS_PENDING_APPROVAL;
                $updateData['requested_at'] = now();
            }

            $requisition->update($updateData);
            $requisition->items()->delete();

            foreach ($data['items'] as $itemData) {
                StoreRequisitionItem::create([
                    'store_requisition_id' => $requisition->id,
                    'inventory_item_id' => $itemData['inventory_item_id'],
                    'quantity_requested' => $itemData['quantity_requested'],
                ]);
            }

            $changes = [
                'original' => $originalData,
                'updated' => [
                    'purpose' => $data['purpose'],
                    'branch_id' => $data['branch_id'],
                    'department_id' => $data['department_id'],
                    'items' => $data['items'],
                ],
            ];

            \App\Models\StoreRequisitionHistory::createRecord(
                $requisition->id,
                $user->id,
                $isDraft ? 'edited' : 'edited_and_submitted',
                $isDraft ? 'Store requisition updated and saved as draft' : 'Store requisition updated and submitted for approval',
                $changes
            );

            DB::commit();
            return $requisition;
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to update store requisition: ' . $e->getMessage());
            throw $e;
        }
    }

    public function approveRequisition(StoreRequisition $requisition, ?string $comments = null): void
    {
        DB::transaction(function () use ($requisition, $comments) {
            // Lock the row and re-fetch to ensure we have the latest state
            $lockedRequisition = StoreRequisition::where('id', $requisition->id)
                ->lockForUpdate()
                ->first();
                
            if (!$lockedRequisition) {
                throw new \Exception('Store requisition not found');
            }
            
            // Re-check if it can still be approved after acquiring lock
            if (!$lockedRequisition->canBeApproved()) {
                throw new \Exception('Store requisition has already been processed or is not in pending approval status');
            }
            
            $lockedRequisition->update([
                'status' => StoreRequisition::STATUS_APPROVED,
                'approver_user_id' => Auth::id(),
                'approved_at' => now(),
            ]);

            \App\Models\StoreRequisitionHistory::createRecord(
                $lockedRequisition->id,
                Auth::id(),
                'approved',
                $comments ?? 'Store requisition approved'
            );
        });
    }

    public function rejectRequisition(StoreRequisition $requisition, string $reason): void
    {
        DB::transaction(function () use ($requisition, $reason) {
            // Lock the row and re-fetch to ensure we have the latest state
            $lockedRequisition = StoreRequisition::where('id', $requisition->id)
                ->lockForUpdate()
                ->first();
                
            if (!$lockedRequisition) {
                throw new \Exception('Store requisition not found');
            }
            
            // Re-check if it can still be rejected after acquiring lock
            if (!$lockedRequisition->canBeApproved()) {
                throw new \Exception('Store requisition has already been processed or is not in pending approval status');
            }
            
            $lockedRequisition->update([
                'status' => StoreRequisition::STATUS_REJECTED,
                'approver_user_id' => Auth::id(),
                'rejection_reason' => $reason,
            ]);

            \App\Models\StoreRequisitionHistory::createRecord(
                $lockedRequisition->id,
                Auth::id(),
                'rejected',
                'Store requisition rejected: ' . $reason
            );
        });
    }

    public function returnForRevision(StoreRequisition $requisition, string $comments): void
    {
        DB::transaction(function () use ($requisition, $comments) {
            // Lock the row and re-fetch to ensure we have the latest state
            $lockedRequisition = StoreRequisition::where('id', $requisition->id)
                ->lockForUpdate()
                ->first();
                
            if (!$lockedRequisition) {
                throw new \Exception('Store requisition not found');
            }
            
            // Re-check if it can still be returned for revision after acquiring lock
            if (!$lockedRequisition->canBeReturnedForRevision()) {
                throw new \Exception('Store requisition has already been processed or is not in pending approval status');
            }
            
            $lockedRequisition->update([
                'status' => StoreRequisition::STATUS_RETURNED_FOR_REVISION,
                'rejection_reason' => $comments,
            ]);

            \App\Models\StoreRequisitionHistory::createRecord(
                $lockedRequisition->id,
                Auth::id(),
                'returned_for_revision',
                $comments
            );
        });
    }

    public function submitRequisition(StoreRequisition $requisition): void
    {
        $requisition->update([
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        \App\Models\StoreRequisitionHistory::createRecord(
            $requisition->id,
            Auth::id(),
            'submitted_for_approval',
            'Store requisition submitted for approval'
        );
    }
}