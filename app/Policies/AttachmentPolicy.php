<?php

namespace App\Policies;

use App\Models\Attachment;
use App\Models\User;
use App\Models\Requisition;
use App\Models\Transaction;

class AttachmentPolicy
{
    /**
     * Determine whether the user can download the attachment.
     */
    public function download(User $user, Attachment $attachment): bool
    {
        // Get the parent model (Requisition, StoreRequisition, or Transaction)
        $attachable = $attachment->attachable;

        if ($attachable instanceof Requisition) {
            return $this->canAccessRequisition($user, $attachable);
        }

        if ($attachable instanceof \App\Models\StoreRequisition) {
            return $this->canAccessStoreRequisition($user, $attachable);
        }

        if ($attachable instanceof Transaction) {
            return $this->canAccessTransaction($user, $attachable);
        }

        return false;
    }

    /**
     * Determine whether the user can delete the attachment.
     */
    public function delete(User $user, Attachment $attachment): bool
    {
        // Only the uploader or admin can delete
        if ($attachment->uploaded_by === $user->id) {
            return true;
        }

        // Platform admin can delete any attachment
        if ($user->is_platform_admin) {
            return true;
        }

        // Organization admin can delete attachments in their organization
        $attachable = $attachment->attachable;
        $userOrganizationIds = $user->organizations->pluck('id')->toArray();

        if ($attachable instanceof Requisition) {
            return $user->hasRole('Organization Admin') &&
                   in_array($attachable->organization_id, $userOrganizationIds);
        }

        if ($attachable instanceof Transaction) {
            return $user->hasRole('Organization Admin') &&
                   in_array($attachable->requisition->organization_id, $userOrganizationIds);
        }

        return false;
    }

    /**
     * Check if user can access a requisition.
     */
    private function canAccessRequisition(User $user, Requisition $requisition): bool
    {
        // Platform admin can access all
        if ($user->is_platform_admin) {
            return true;
        }

        // Must be in same organization
        $userOrganizationIds = $user->organizations->pluck('id')->toArray();
        if (!in_array($requisition->organization_id, $userOrganizationIds)) {
            return false;
        }

        // Requester can always access their own requisition
        if ($requisition->requester_user_id === $user->id) {
            return true;
        }

        // HOD can access requisitions from their department
        if ($user->hasRole('HOD')) {
            $userDepartments = $user->departments->pluck('id')->toArray();
            if (in_array($requisition->department_id, $userDepartments)) {
                return true;
            }
        }

        // Approvers can access requisitions they can approve
        if ($user->hasRole('Approver')) {
            // Check if user is in the approval workflow for this requisition
            // This would need to be implemented based on your approval workflow logic
            return true; // Simplified for now
        }

        // Organization admin can access all requisitions in their org
        if ($user->hasRole('Organization Admin')) {
            return true;
        }

        return false;
    }

    /**
     * Check if user can access a transaction.
     */
    private function canAccessTransaction(User $user, Transaction $transaction): bool
    {
        // Platform admin can access all
        if ($user->is_platform_admin) {
            return true;
        }

        // Must be in same organization as the parent requisition
        $userOrganizationIds = $user->organizations->pluck('id')->toArray();
        if (!in_array($transaction->requisition->organization_id, $userOrganizationIds)) {
            return false;
        }

        // Finance Manager can access all transaction attachments in their organization
        if ($user->hasRole('Finance Manager')) {
            return true;
        }

        // Cashier can access all transaction attachments in their organization
        if ($user->hasRole('Cashier')) {
            return true;
        }

        // Organization admin can access all transaction attachments in their org
        if ($user->hasRole('Organization Admin')) {
            return true;
        }

        // Requester can access their own transaction attachments
        if ($transaction->requisition->requester_user_id === $user->id) {
            return true;
        }

        // HOD can access transaction attachments from their department
        if ($user->hasRole('HOD')) {
            $userDepartments = $user->departments->pluck('id')->toArray();
            if (in_array($transaction->requisition->department_id, $userDepartments)) {
                return true;
            }
        }

        // Check access to the parent requisition for other roles
        return $this->canAccessRequisition($user, $transaction->requisition);
    }

    /**
     * Check if user can access a store requisition.
     */
    private function canAccessStoreRequisition(User $user, \App\Models\StoreRequisition $storeRequisition): bool
    {
        // Debug logging to help identify permission issues
        \Log::info('AttachmentPolicy: Checking store requisition access', [
            'user_id' => $user->id,
            'user_roles' => $user->getRoleNames()->toArray(),
            'user_permissions' => $user->getAllPermissions()->pluck('name')->toArray(),
            'store_requisition_id' => $storeRequisition->id,
            'store_requisition_status' => $storeRequisition->status,
            'requester_id' => $storeRequisition->requester_user_id,
            'organization_id' => $storeRequisition->organization_id,
            'user_organizations' => $user->organizations->pluck('id')->toArray(),
        ]);

        $canView = $user->can('view', $storeRequisition);

        \Log::info('AttachmentPolicy: Store requisition view permission result', [
            'user_id' => $user->id,
            'store_requisition_id' => $storeRequisition->id,
            'can_view' => $canView,
        ]);

        return $canView;
    }
}
