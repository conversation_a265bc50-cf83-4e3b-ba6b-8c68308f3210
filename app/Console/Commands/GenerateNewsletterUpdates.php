<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class GenerateNewsletterUpdates extends Command
{
    protected $signature = 'newsletter:generate {--since=1 week ago : Generate updates since a specific date or period}';

    protected $description = 'Generates a summary of recent Git commit logs for the newsletter.';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $since = $this->option('since');
        $this->info("Generating newsletter updates since {$since}...");

        $command = "git log --pretty=format:\"%s\" --since=\"$since\"";
        $output = shell_exec($command);

        if (empty($output)) {
            $this->info('No new commits found.');
            return "No significant updates in the past week. Stay tuned for more!";
        }

        $commits = explode("\n", $output);
        $updates = [];

        foreach ($commits as $commit) {
            $commit = trim($commit);
            if (empty($commit)) {
                continue;
            }

            // Filter out merge commits and internal messages, focus on user-facing changes
            if (str_starts_with($commit, 'Merge ')) {
                continue;
            }

            // Extract and format user-friendly messages based on conventional commits
            if (preg_match('/^(feat|fix|chore|docs|refactor)\(.*?\):\s*(.*)/i', $commit, $matches)) {
                $type = $matches[1];
                $message = trim($matches[2]);

                switch (strtolower($type)) {
                    case 'feat':
                        $updates[] = "- New Feature: {$message}";
                        break;
                    case 'fix':
                        $updates[] = "- Bug Fix: {$message}";
                        break;
                    case 'chore':
                    case 'docs':
                    case 'refactor':
                        // Include these if they have a clear user benefit
                        if (str_contains(strtolower($message), 'user') || str_contains(strtolower($message), 'client') || str_contains(strtolower($message), 'interface') || str_contains(strtolower($message), 'performance')) {
                            $updates[] = "- Improvement: {$message}";
                        }
                        break;
                    default:
                        // Skip other types by default
                        break;
                }
            } else {
                // Include commits that don't follow the conventional format but might be important
                $updates[] = "- Update: {$commit}";
            }
        }

        if (empty($updates)) {
            return "No significant user-facing updates in the past week. Stay tuned for more!";
        }

        return "Here are some recent updates from Sippar:\n\n" . implode("\n", $updates) . "\n\nThank you for being part of the Sippar community!";
    }
}
