<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ProcessQueueWithMonitoring extends Command
{
    protected $signature = 'queue:process-monitored {--timeout=90} {--tries=3} {--worker-id=default}';
    protected $description = 'Process queue with monitoring and heartbeat';

    public function handle()
    {
        $workerId = $this->option('worker-id');
        $timeout = $this->option('timeout');
        $tries = $this->option('tries');
        
        $startTime = microtime(true);
        $this->info("🚀 Starting monitored queue processing (Worker: {$workerId})");
        
        Artisan::call('queue:heartbeat', ['--worker-id' => $workerId]);
        
        try {
            $exitCode = Artisan::call('queue:work', [
                '--stop-when-empty' => true,
                '--timeout' => $timeout,
                '--tries' => $tries,
                '--verbose' => true,
            ]);
            
            $duration = round(microtime(true) - $startTime, 2);
            $output = Artisan::output();
            
            // Record completion heartbeat
            Artisan::call('queue:heartbeat', ['--worker-id' => $workerId]);
            
            // Log performance metrics
            $this->logPerformanceMetrics($workerId, $duration, $exitCode, $output);
            
            if ($exitCode === 0) {
                $this->info("✅ Queue processing completed successfully in {$duration}s");
            } else {
                $this->error("❌ Queue processing failed with exit code: {$exitCode}");
            }
            
            return $exitCode;
            
        } catch (\Exception $e) {
            $duration = round(microtime(true) - $startTime, 2);
            
            $this->error("💥 Queue processing crashed: " . $e->getMessage());
            
            Log::error('Queue processing crashed', [
                'worker_id' => $workerId,
                'duration' => $duration,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true),
            ]);
            
            return 1;
        }
    }
    
    private function logPerformanceMetrics(string $workerId, float $duration, int $exitCode, string $output): void
    {
        // Parse output to count processed jobs
        $processedJobs = substr_count($output, 'Processing:');
        
        $metrics = [
            'worker_id' => $workerId,
            'duration' => $duration,
            'exit_code' => $exitCode,
            'processed_jobs' => $processedJobs,
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'timestamp' => now(),
        ];
        
        // Store metrics in cache for monitoring
        $metricsKey = "queue_metrics_" . now()->format('Y-m-d-H-i');
        Cache::put($metricsKey, $metrics, 86400); // 24 hours
        
        // Update daily counters
        $dailyKey = "queue_daily_" . now()->format('Y-m-d');
        $dailyStats = Cache::get($dailyKey, [
            'total_runs' => 0,
            'total_jobs' => 0,
            'total_duration' => 0,
            'failed_runs' => 0,
        ]);
        
        $dailyStats['total_runs']++;
        $dailyStats['total_jobs'] += $processedJobs;
        $dailyStats['total_duration'] += $duration;
        if ($exitCode !== 0) {
            $dailyStats['failed_runs']++;
        }
        
        Cache::put($dailyKey, $dailyStats, now()->endOfDay());
        
        // Log if debug mode or if there were issues
        if (config('app.debug') || $exitCode !== 0 || $duration > 60) {
            Log::info('Queue processing metrics', $metrics);
        }
    }
}
