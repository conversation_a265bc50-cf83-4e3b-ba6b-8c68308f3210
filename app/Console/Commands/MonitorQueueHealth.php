<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;

class MonitorQueueHealth extends Command
{
    protected $signature = 'queue:monitor-health {--alert-email=} {--max-failed=10} {--max-pending=100} {--max-age=300}';
    protected $description = 'Monitor queue health and send alerts if issues detected';

    public function handle()
    {
        $this->info('🔍 Starting queue health monitoring...');
        
        $issues = [];
        $stats = $this->getQueueStats();
        
        // Display current stats
        $this->displayStats($stats);
        
        // Check for issues
        $issues = array_merge($issues, $this->checkFailedJobs($stats));
        $issues = array_merge($issues, $this->checkPendingJobs($stats));
        $issues = array_merge($issues, $this->checkOldJobs($stats));
        $issues = array_merge($issues, $this->checkQueueWorkerHealth());
        
        // Handle alerts
        if (!empty($issues)) {
            $this->handleAlerts($issues, $stats);
            return 1; // Exit with error code
        }
        
        $this->info('✅ Queue health check passed!');
        
        // Update last successful check
        Cache::put('queue_health_last_check', now(), 3600);
        
        return 0;
    }
    
    private function getQueueStats(): array
    {
        return [
            'failed_jobs' => DB::table('failed_jobs')->count(),
            'pending_jobs' => DB::table('jobs')->count(),
            'oldest_pending' => DB::table('jobs')->min('created_at'),
            'newest_pending' => DB::table('jobs')->max('created_at'),
            'total_processed_today' => $this->getProcessedToday(),
            'last_processed' => Cache::get('queue_last_processed'),
            'worker_last_seen' => Cache::get('queue_worker_last_seen'),
        ];
    }
    
    private function displayStats(array $stats): void
    {
        $this->table(
            ['Metric', 'Value', 'Status'],
            [
                ['Failed Jobs', $stats['failed_jobs'], $stats['failed_jobs'] > 0 ? '⚠️' : '✅'],
                ['Pending Jobs', $stats['pending_jobs'], $stats['pending_jobs'] > 50 ? '⚠️' : '✅'],
                ['Oldest Pending', $stats['oldest_pending'] ? Carbon::parse($stats['oldest_pending'])->diffForHumans() : 'None', ''],
                ['Processed Today', $stats['total_processed_today'], ''],
                ['Last Processed', $stats['last_processed'] ? Carbon::parse($stats['last_processed'])->diffForHumans() : 'Never', ''],
                ['Worker Last Seen', $stats['worker_last_seen'] ? Carbon::parse($stats['worker_last_seen'])->diffForHumans() : 'Never', ''],
            ]
        );
    }
    
    private function checkFailedJobs(array $stats): array
    {
        $maxFailed = $this->option('max-failed');
        $issues = [];
        
        if ($stats['failed_jobs'] > $maxFailed) {
            $issues[] = "❌ Too many failed jobs: {$stats['failed_jobs']} (max: {$maxFailed})";
            
            // Get recent failed job details
            $recentFailed = DB::table('failed_jobs')
                ->orderBy('failed_at', 'desc')
                ->limit(5)
                ->get(['payload', 'exception', 'failed_at']);
                
            foreach ($recentFailed as $job) {
                $payload = json_decode($job->payload, true);
                $jobClass = $payload['displayName'] ?? 'Unknown';
                $issues[] = "  - {$jobClass} failed at {$job->failed_at}";
            }
        }
        
        return $issues;
    }
    
    private function checkPendingJobs(array $stats): array
    {
        $maxPending = $this->option('max-pending');
        $issues = [];
        
        if ($stats['pending_jobs'] > $maxPending) {
            $issues[] = "⚠️ Too many pending jobs: {$stats['pending_jobs']} (max: {$maxPending})";
        }
        
        return $issues;
    }
    
    private function checkOldJobs(array $stats): array
    {
        $maxAge = $this->option('max-age'); // seconds
        $issues = [];
        
        if ($stats['oldest_pending']) {
            $oldestAge = Carbon::parse($stats['oldest_pending'])->diffInSeconds(now());
            if ($oldestAge > $maxAge) {
                $issues[] = "🕐 Jobs too old: oldest pending job is " . Carbon::parse($stats['oldest_pending'])->diffForHumans();
            }
        }
        
        return $issues;
    }
    
    private function checkQueueWorkerHealth(): array
    {
        $issues = [];
        $lastSeen = Cache::get('queue_worker_last_seen');
        
        if (!$lastSeen) {
            $issues[] = "❓ Queue worker never reported status";
        } elseif (Carbon::parse($lastSeen)->diffInMinutes(now()) > 5) {
            $issues[] = "💀 Queue worker hasn't been seen for " . Carbon::parse($lastSeen)->diffForHumans();
        }
        
        return $issues;
    }
    
    private function getProcessedToday(): int
    {
        return Cache::get('queue_processed_today', 0);
    }
    
    private function handleAlerts(array $issues, array $stats): void
    {
        $this->error('🚨 Queue health issues detected:');
        foreach ($issues as $issue) {
            $this->error($issue);
        }
        
        // Log issues
        Log::warning('Queue health issues detected', [
            'issues' => $issues,
            'stats' => $stats,
            'timestamp' => now()
        ]);
        
        // Send email alert if configured
        $alertEmail = $this->option('alert-email');
        if ($alertEmail) {
            $this->sendEmailAlert($alertEmail, $issues, $stats);
        }
        
        // Store alert in cache to prevent spam
        $alertKey = 'queue_health_alert_' . md5(implode('', $issues));
        if (!Cache::has($alertKey)) {
            Cache::put($alertKey, true, 1800); 
            $this->info("📧 Alert sent to {$alertEmail}");
        } else {
            $this->info("📧 Alert suppressed (already sent recently)");
        }
    }
    
    private function sendEmailAlert(string $email, array $issues, array $stats): void
    {
        try {
            Mail::raw(
                "Queue Health Alert\n\n" .
                "Issues detected:\n" . implode("\n", $issues) . "\n\n" .
                "Current Stats:\n" .
                "- Failed Jobs: {$stats['failed_jobs']}\n" .
                "- Pending Jobs: {$stats['pending_jobs']}\n" .
                "- Oldest Pending: " . ($stats['oldest_pending'] ? Carbon::parse($stats['oldest_pending'])->diffForHumans() : 'None') . "\n" .
                "- Last Processed: " . ($stats['last_processed'] ? Carbon::parse($stats['last_processed'])->diffForHumans() : 'Never') . "\n\n" .
                "Please check the queue system.\n\n" .
                "Time: " . now()->format('Y-m-d H:i:s'),
                function ($message) use ($email) {
                    $message->to($email)
                           ->subject('🚨 Queue Health Alert - ' . config('app.name'));
                }
            );
        } catch (\Exception $e) {
            $this->error("Failed to send email alert: " . $e->getMessage());
        }
    }
}
