<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class QueueDashboard extends Command
{
    protected $signature = 'queue:dashboard {--refresh=5} {--watch}';
    protected $description = 'Display real-time queue dashboard';

    public function handle()
    {
        $watch = $this->option('watch');
        $refresh = $this->option('refresh');
        
        if ($watch) {
            $this->info("🔄 Starting queue dashboard (refreshing every {$refresh}s, press Ctrl+C to exit)");
            while (true) {
                $this->displayDashboard();
                sleep($refresh);
                // Clear screen for next update
                system('clear');
            }
        } else {
            $this->displayDashboard();
        }
        
        return 0;
    }
    
    private function displayDashboard(): void
    {
        $this->info('📊 Queue Dashboard - ' . now()->format('Y-m-d H:i:s'));
        $this->line('');
        
        // Current Status
        $this->displayCurrentStatus();
        $this->line('');
        
        // Recent Activity
        $this->displayRecentActivity();
        $this->line('');
        
        // Daily Statistics
        $this->displayDailyStats();
        $this->line('');
        
        // Failed Jobs
        $this->displayFailedJobs();
        $this->line('');
        
        // Worker Health
        $this->displayWorkerHealth();
    }
    
    private function displayCurrentStatus(): void
    {
        $pendingJobs = DB::table('jobs')->count();
        $failedJobs = DB::table('failed_jobs')->count();
        $oldestPending = DB::table('jobs')->min('created_at');
        
        $this->info('📈 Current Status');
        $this->table(
            ['Metric', 'Value', 'Status'],
            [
                ['Pending Jobs', $pendingJobs, $pendingJobs > 50 ? '⚠️ High' : ($pendingJobs > 0 ? '✅ Normal' : '✅ Empty')],
                ['Failed Jobs', $failedJobs, $failedJobs > 0 ? '❌ Issues' : '✅ Clean'],
                ['Oldest Pending', $oldestPending ? Carbon::parse($oldestPending)->diffForHumans() : 'None', $oldestPending && Carbon::parse($oldestPending)->diffInMinutes() > 5 ? '⚠️ Old' : '✅ Fresh'],
            ]
        );
    }
    
    private function displayRecentActivity(): void
    {
        $this->info('🕐 Recent Activity (Last 10 jobs)');
        
        $recentJobs = DB::table('jobs')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get(['id', 'queue', 'payload', 'created_at']);
            
        if ($recentJobs->isEmpty()) {
            $this->line('No recent jobs');
            return;
        }
        
        $rows = [];
        foreach ($recentJobs as $job) {
            $payload = json_decode($job->payload, true);
            $jobClass = $payload['displayName'] ?? 'Unknown';
            $rows[] = [
                $job->id,
                $job->queue ?: 'default',
                $jobClass,
                Carbon::parse($job->created_at)->diffForHumans(),
            ];
        }
        
        $this->table(['ID', 'Queue', 'Job', 'Created'], $rows);
    }
    
    private function displayDailyStats(): void
    {
        $this->info('📊 Daily Statistics');
        
        $today = now()->format('Y-m-d');
        $dailyStats = Cache::get("queue_daily_{$today}", [
            'total_runs' => 0,
            'total_jobs' => 0,
            'total_duration' => 0,
            'failed_runs' => 0,
        ]);
        
        $avgDuration = $dailyStats['total_runs'] > 0 
            ? round($dailyStats['total_duration'] / $dailyStats['total_runs'], 2) 
            : 0;
            
        $successRate = $dailyStats['total_runs'] > 0 
            ? round((($dailyStats['total_runs'] - $dailyStats['failed_runs']) / $dailyStats['total_runs']) * 100, 1)
            : 100;
        
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Runs', $dailyStats['total_runs']],
                ['Jobs Processed', $dailyStats['total_jobs']],
                ['Failed Runs', $dailyStats['failed_runs']],
                ['Success Rate', $successRate . '%'],
                ['Avg Duration', $avgDuration . 's'],
                ['Total Duration', round($dailyStats['total_duration'], 2) . 's'],
            ]
        );
    }
    
    private function displayFailedJobs(): void
    {
        $this->info('❌ Recent Failed Jobs');
        
        $failedJobs = DB::table('failed_jobs')
            ->orderBy('failed_at', 'desc')
            ->limit(5)
            ->get(['id', 'queue', 'payload', 'exception', 'failed_at']);
            
        if ($failedJobs->isEmpty()) {
            $this->line('✅ No failed jobs');
            return;
        }
        
        $rows = [];
        foreach ($failedJobs as $job) {
            $payload = json_decode($job->payload, true);
            $jobClass = $payload['displayName'] ?? 'Unknown';
            $exception = substr($job->exception, 0, 50) . '...';
            
            $rows[] = [
                $job->id,
                $job->queue ?: 'default',
                $jobClass,
                $exception,
                Carbon::parse($job->failed_at)->diffForHumans(),
            ];
        }
        
        $this->table(['ID', 'Queue', 'Job', 'Error', 'Failed'], $rows);
    }
    
    private function displayWorkerHealth(): void
    {
        $this->info('💓 Worker Health');
        
        $lastSeen = Cache::get('queue_worker_last_seen');
        $lastProcessed = Cache::get('queue_last_processed');
        $lastCheck = Cache::get('queue_health_last_check');
        
        $this->table(
            ['Metric', 'Value', 'Status'],
            [
                ['Last Seen', $lastSeen ? Carbon::parse($lastSeen)->diffForHumans() : 'Never', $lastSeen && Carbon::parse($lastSeen)->diffInMinutes() < 5 ? '✅ Healthy' : '⚠️ Stale'],
                ['Last Processed', $lastProcessed ? Carbon::parse($lastProcessed)->diffForHumans() : 'Never', ''],
                ['Last Health Check', $lastCheck ? Carbon::parse($lastCheck)->diffForHumans() : 'Never', ''],
            ]
        );
    }
}
