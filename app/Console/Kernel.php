<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Using direct cron jobs instead of Laravel scheduler for better reliability

        // Process queue jobs every minute with monitoring
        $schedule->command('queue:process-monitored --timeout=90 --tries=3 --worker-id=cron')
                 ->everyMinute()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Monitor queue health every 5 minutes
        $schedule->command('queue:monitor-health --max-failed=10 --max-pending=100 --max-age=300')
                 ->everyFiveMinutes()
                 ->withoutOverlapping();

        // Check inventory stock levels every hour
        $schedule->command('inventory:cron-check')
                 ->hourly()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Clean up old metrics daily
        $schedule->call(function () {
            $cutoff = now()->subDays(7);
            \Cache::forget('queue_metrics_' . $cutoff->format('Y-m-d-H-i'));
        })->daily();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}