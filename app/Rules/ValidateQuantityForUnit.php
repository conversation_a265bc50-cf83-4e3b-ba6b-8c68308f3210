<?php

namespace App\Rules;

use App\Models\InventoryItem;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidateQuantityForUnit implements ValidationRule
{
    private int $inventoryItemId;

    public function __construct(int $inventoryItemId)
    {
        $this->inventoryItemId = $inventoryItemId;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Find the inventory item
        $inventoryItem = InventoryItem::find($this->inventoryItemId);
        
        if (!$inventoryItem) {
            $fail('The selected inventory item is invalid.');
            return;
        }

        // Check if value is numeric
        if (!is_numeric($value)) {
            $fail("The {$attribute} must be a number.");
            return;
        }

        // Check if value is positive
        if ($value <= 0) {
            $fail("The {$attribute} must be greater than 0.");
            return;
        }

        // Validate the quantity based on the unit type
        if ($inventoryItem->shouldUseWholeNumbers() && $value != floor($value)) {
            $fail("The {$attribute} must be a whole number for {$inventoryItem->unit_of_measure}.");
        }
    }
}