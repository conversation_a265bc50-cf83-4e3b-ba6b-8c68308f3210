<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ChartOfAccount extends Model
{
    use HasFactory;

    // Allow mass assignment for these fields during creation/update
    protected $fillable = [
        'organization_id',
        'branch_id',
        'code',
        'name',
        'description',
        'spending_limit',
        'limit_period',
        'is_active',
        'parent_id',
        'account_type',
    ];

    // Cast attributes to native types
    protected $casts = [
        'is_active' => 'boolean',
        'spending_limit' => 'decimal:2', // Ensure precision
    ];

    // --- Relationships ---

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function parent(): BelongsTo
    {
        // Relationship to self for hierarchical structure
        return $this->belongsTo(ChartOfAccount::class, 'parent_id');
    }

    public function children(): HasMany
    {
        // Inverse of the parent relationship
        return $this->hasMany(ChartOfAccount::class, 'parent_id');
    }

    public function requisitionItems(): HasMany
    {
        // CoA can be linked to many requisition items
        return $this->hasMany(RequisitionItem::class);
    }

    public function transactionItems(): HasMany
    {
        // CoA can be linked to many transaction items
        return $this->hasMany(TransactionItem::class);
    }

    /**
     * Get all descendants (children, grandchildren, etc.) of this chart of account
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllDescendants()
    {
        $descendants = collect();

        // Get direct children
        $children = $this->children()->with('children')->get();

        foreach ($children as $child) {
            $descendants->push($child);
            // Recursively get descendants of each child
            $descendants = $descendants->merge($child->getAllDescendants());
        }

        return $descendants;
    }

    /**
     * Get chart of accounts filtered for regular employees (exclude only the 5 main organization accounts)
     *
     * @param int $organizationId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getEmployeeFilteredAccounts(int $organizationId)
    {
        // Get the organization's 5 main chart of accounts (direct children of platform accounts)
        $organizationMainAccountIds = static::where('organization_id', $organizationId)
            ->whereNotNull('parent_id')
            ->whereHas('parent', function ($query) {
                $query->whereNull('organization_id'); // Platform-level parents
            })
            ->pluck('id');

        // Return all organization accounts except the 5 main ones
        return static::where('organization_id', $organizationId)
            ->where('is_active', true)
            ->whereNotIn('id', $organizationMainAccountIds) // Exclude the 5 main accounts by ID
            ->orderBy('name')
            ->get();
    }

    /**
     * Get chart of accounts for finance managers and organization admins (all accounts with codes)
     *
     * @param int $organizationId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getFinanceFilteredAccounts(int $organizationId)
    {
        return static::where('organization_id', $organizationId)
            ->where('is_active', true)
            ->orderBy('code')
            ->get();
    }
}
