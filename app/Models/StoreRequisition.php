<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class StoreRequisition extends Model
{
    use HasFactory, SoftDeletes;

    // Status Constants
    public const STATUS_DRAFT = 'draft';
    public const STATUS_PENDING_APPROVAL = 'pending_approval';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_REJECTED = 'rejected';
    public const STATUS_RETURNED_FOR_REVISION = 'returned_for_revision';
    public const STATUS_ISSUED = 'issued';
    public const STATUS_PARTIALLY_ISSUED = 'partially_issued';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'organization_id',
        'branch_id',
        'department_id',
        'requester_user_id',
        'approver_user_id',
        'issuer_user_id',
        'purpose',
        'status',
        'rejection_reason',
        'approval_comments',
        'issuing_comments',
        'requested_at',
        'approved_at',
        'issued_at',
        'total_items',
        'priority',
        'expected_use_date',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'requested_at' => 'datetime',
        'approved_at' => 'datetime',
        'issued_at' => 'datetime',
        'expected_use_date' => 'date',
        'total_items' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */

    protected $hidden = [
        'deleted_at',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->requester_user_id && Auth::check()) {
                $model->requester_user_id = Auth::id();
            }
            if (!$model->status) {
                $model->status = self::STATUS_DRAFT;
            }
        });

        static::updating(function ($model) {
            // Auto-set timestamps based on status changes
            if ($model->isDirty('status')) {
                switch ($model->status) {
                    case self::STATUS_PENDING_APPROVAL:
                        if (!$model->requested_at) {
                            $model->requested_at = now();
                        }
                        break;
                    case self::STATUS_APPROVED:
                        if (!$model->approved_at) {
                            $model->approved_at = now();
                        }
                        if (!$model->approver_user_id && Auth::check()) {
                            $model->approver_user_id = Auth::id();
                        }
                        break;
                    case self::STATUS_ISSUED:
                    case self::STATUS_PARTIALLY_ISSUED:
                        if (!$model->issued_at) {
                            $model->issued_at = now();
                        }
                        if (!$model->issuer_user_id && Auth::check()) {
                            $model->issuer_user_id = Auth::id();
                        }
                        break;
                }
            }
        });
    }

    // ================================
    // RELATIONSHIPS
    // ================================

    /**
     * Get the organization that owns the store requisition.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the branch that owns the store requisition.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the department that owns the store requisition.
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the user who requested the store requisition.
     */
    public function requester(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requester_user_id');
    }

    /**
     * Get the user who approved the store requisition.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approver_user_id');
    }

    /**
     * Get the user who issued the store requisition.
     */
    public function issuer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'issuer_user_id');
    }

    /**
     * Get the items for the store requisition.
     */
    public function items(): HasMany
    {
        return $this->hasMany(StoreRequisitionItem::class);
    }

    /**
     * Get the history records for this store requisition.
     */
    public function histories(): HasMany
    {
        return $this->hasMany(StoreRequisitionHistory::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get the attachments for this store requisition.
     */
    public function attachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    /**
     * Get the approval records for this store requisition.
     */
    // public function approvals(): HasMany
    // {
    //     return $this->hasMany(StoreRequisitionApproval::class)->orderBy('created_at', 'desc');
    // }

    /**
     * Get the latest approval record.
     */
    public function latestApproval()
    {
        return $this->hasOne(StoreRequisitionApproval::class)->latestOfMany();
    }

    // ================================
    // STATUS CHECK METHODS
    // ================================

    /**
     * Check if the store requisition can be submitted.
     */
    public function canBeSubmitted(): bool
    {
        return $this->status === self::STATUS_DRAFT && $this->items()->count() > 0;
    }

    /**
     * Check if the store requisition can be approved.
     */
    public function canBeApproved(): bool
    {
        return $this->status === self::STATUS_PENDING_APPROVAL;
    }

    /**
     * Check if the store requisition can be rejected.
     */
    public function canBeRejected(): bool
    {
        return $this->status === self::STATUS_PENDING_APPROVAL;
    }

    /**
     * Check if the store requisition can be issued.
     */
    public function canBeIssued(): bool
    {
        return in_array($this->status, [self::STATUS_APPROVED, self::STATUS_PARTIALLY_ISSUED]);
    }

    /**
     * Check if the store requisition is fully issued.
     */
    public function isFullyIssued(): bool
    {
        return $this->status === self::STATUS_ISSUED;
    }

    /**
     * Check if the store requisition is pending.
     */
    public function isPending(): bool
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_PENDING_APPROVAL]);
    }

    /**
     * Check if the store requisition is completed.
     */
    public function isCompleted(): bool
    {
        return in_array($this->status, [self::STATUS_ISSUED, self::STATUS_REJECTED]);
    }

    /**
     * Check if the store requisition can be edited.
     */
    public function canBeEdited(): bool
    {
        return in_array($this->status, [
            self::STATUS_DRAFT,
            self::STATUS_REJECTED,
            self::STATUS_RETURNED_FOR_REVISION
        ]);
    }

    /**
     * Check if the store requisition can be returned for revision.
     */
    public function canBeReturnedForRevision(): bool
    {
        return $this->status === self::STATUS_PENDING_APPROVAL;
    }

    /**
     * Check if the store requisition can be deleted.
     */
    public function canBeDeleted(): bool
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_REJECTED]);
    }

    // ================================
    // BUSINESS LOGIC METHODS
    // ================================

    /**
     * Submit the store requisition for approval.
     */
    public function submit(): bool
    {
        if (!$this->canBeSubmitted()) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        $this->createHistory('submitted', 'Store requisition submitted for approval');

        return true;
    }

    /**
     * Approve the store requisition.
     */
    public function approve(string $comments = null): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_APPROVED,
            'approval_comments' => $comments,
            'approved_at' => now(),
            'approver_user_id' => Auth::id(),
        ]);

        $this->createHistory('approved', $comments ?: 'Store requisition approved');

        return true;
    }

    /**
     * Reject the store requisition.
     */
    public function reject(string $reason): bool
    {
        if (!$this->canBeRejected()) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_REJECTED,
            'rejection_reason' => $reason,
            'approver_user_id' => Auth::id(),
        ]);

        $this->createHistory('rejected', $reason);

        return true;
    }

    /**
     * Return the store requisition for revision.
     */
    public function returnForRevision(string $reason): bool
    {
        if (!$this->canBeReturnedForRevision()) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_RETURNED_FOR_REVISION,
            'rejection_reason' => $reason,
        ]);

        $this->createHistory('returned_for_revision', $reason);

        return true;
    }

    /**
     * Mark items as issued and update requisition status.
     */
    public function issueItems(array $itemsData, string $comments = null): bool
    {
        if (!$this->canBeIssued()) {
            return false;
        }

        $allItemsIssued = true;

        foreach ($itemsData as $itemId => $issuedQuantity) {
            $item = $this->items()->find($itemId);
            if ($item) {
                $item->update(['quantity_issued' => $issuedQuantity]);
                if ($item->quantity_issued < $item->quantity_requested) {
                    $allItemsIssued = false;
                }
            }
        }

        $newStatus = $allItemsIssued ? self::STATUS_ISSUED : self::STATUS_PARTIALLY_ISSUED;

        $this->update([
            'status' => $newStatus,
            'issuing_comments' => $comments,
            'issued_at' => now(),
            'issuer_user_id' => Auth::id(),
        ]);

        $historyMessage = $allItemsIssued ? 'All items issued' : 'Items partially issued';
        if ($comments) {
            $historyMessage .= ': ' . $comments;
        }

        $this->createHistory('issued', $historyMessage);

        return true;
    }

    /**
     * Calculate completion percentage.
     */
    public function getCompletionPercentage(): int
    {
        $items = $this->items;
        if ($items->isEmpty()) {
            return 0;
        }

        $totalRequested = $items->sum('quantity_requested');
        $totalIssued = $items->sum('quantity_issued');

        if ($totalRequested == 0) {
            return 0;
        }

        return (int) round(($totalIssued / $totalRequested) * 100);
    }

    /**
     * Get total value of requisition (if items have prices).
     */
    public function getTotalValue(): float
    {
        return $this->items->sum(function ($item) {
            return $item->quantity_requested * ($item->inventoryItem->unit_price ?? 0);
        });
    }

    /**
     * Create a history record.
     */
    protected function createHistory(string $action, string $description = null): void
    {
        $this->histories()->create([
            'user_id' => Auth::id(),
            'action' => $action,
            'description' => $description,
            'status_before' => $this->getOriginal('status'),
            'status_after' => $this->status,
        ]);
    }

    // ================================
    // SCOPES
    // ================================

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by organization.
     */
    public function scopeByOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Scope to filter by requester.
     */
    public function scopeByRequester($query, $userId)
    {
        return $query->where('requester_user_id', $userId);
    }

    /**
     * Scope to filter by department.
     */
    public function scopeByDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    /**
     * Scope to filter by branch.
     */
    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope for pending approvals.
     */
    public function scopePendingApproval($query)
    {
        return $query->where('status', self::STATUS_PENDING_APPROVAL);
    }

    /**
     * Scope for approved requisitions.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    /**
     * Scope for completed requisitions.
     */
    public function scopeCompleted($query)
    {
        return $query->whereIn('status', [self::STATUS_ISSUED, self::STATUS_REJECTED]);
    }

    

    public function inventoryItems()
    {
        return $this->belongsToMany(InventoryItem::class, 'store_requisition_items')
                    ->withPivot(['quantity', 'requested_quantity', 'approved_quantity']);
    }

    /**
     * Scope to include all related data.
     */
    public function scopeWithFullRelations($query)
    {
        return $query->with([
            'requester:id,first_name,last_name,email',
            'approver:id,first_name,last_name,email',
            'issuer:id,first_name,last_name,email',
            'department:id,name',
            'branch:id,name',
            'organization:id,name',
            'items.inventoryItem:id,name,item_code,unit,unit_price',
            'histories.user:id,first_name,last_name',
            'attachments',
        ]);
    }

    // ================================
    // ACCESSORS & MUTATORS
    // ================================

    /**
     * Get the formatted requisition ID.
     */
    public function getFormattedIdAttribute(): string
    {
        return 'SR-' . str_pad($this->id, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Get the requester's full name.
     */
    public function getRequesterNameAttribute(): string
    {
        return $this->requester ? $this->requester->first_name . ' ' . $this->requester->last_name : 'Unknown';
    }

    /**
     * Get the status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            self::STATUS_DRAFT => 'Draft',
            self::STATUS_PENDING_APPROVAL => 'Pending Approval',
            self::STATUS_APPROVED => 'Approved',
            self::STATUS_REJECTED => 'Rejected',
            self::STATUS_RETURNED_FOR_REVISION => 'Returned for Revision',
            self::STATUS_ISSUED => 'Issued',
            self::STATUS_PARTIALLY_ISSUED => 'Partially Issued',
            default => 'Unknown',
        };
    }

    /**
     * Get all possible statuses.
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_DRAFT => 'Draft',
            self::STATUS_PENDING_APPROVAL => 'Pending Approval',
            self::STATUS_APPROVED => 'Approved',
            self::STATUS_REJECTED => 'Rejected',
            self::STATUS_RETURNED_FOR_REVISION => 'Returned for Revision',
            self::STATUS_ISSUED => 'Issued',
            self::STATUS_PARTIALLY_ISSUED => 'Partially Issued',
        ];
    }
}