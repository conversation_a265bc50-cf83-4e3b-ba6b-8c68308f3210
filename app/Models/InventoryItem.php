<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class InventoryItem extends Model
{
    // Units that should be whole numbers (discrete/countable items)
    const DISCRETE_UNITS = [
        'pieces', 'piece', 'pcs', 'pc',
        'items', 'item',
        'units', 'unit',
        'bags', 'bag',
        'boxes', 'box',
        'packets', 'packet', 'pack', 'packs',
        'bottles', 'bottle',
        'cans', 'can',
        'containers', 'container',
        'sheets', 'sheet',
        'rolls', 'roll',
        'sets', 'set',
        'pairs', 'pair',
        'dozens', 'dozen',
        'cartons', 'carton',
        'tubes', 'tube',
        'sachets', 'sachet',
        'vials', 'vial',
        'tablets', 'tablet', 'tabs',
        'capsules', 'capsule', 'caps',
    ];

    // Units that should allow decimals (continuous/measurable quantities)
    const CONTINUOUS_UNITS = [
        'kilograms', 'kilogram', 'kg', 'kgs',
        'grams', 'gram', 'g', 'gms',
        'pounds', 'pound', 'lb', 'lbs',
        'ounces', 'ounce', 'oz',
        'liters', 'liter', 'litres', 'litre', 'l',
        'milliliters', 'milliliter', 'ml', 'mls',
        'gallons', 'gallon', 'gal',
        'meters', 'meter', 'metres', 'metre', 'm',
        'centimeters', 'centimeter', 'cm',
        'millimeters', 'millimeter', 'mm',
        'inches', 'inch', 'in',
        'feet', 'foot', 'ft',
        'yards', 'yard', 'yd',
        'square meters', 'square meter', 'sqm', 'm2',
        'square feet', 'square foot', 'sqft', 'ft2',
        'cubic meters', 'cubic meter', 'm3',
        'cubic feet', 'cubic foot', 'ft3',
        'tons', 'ton', 'tonnes', 'tonne',
    ];

    protected $fillable = [
        'organization_id',
        'branch_id',
        'sku',
        'name',
        'description',
        'unit_of_measure',
        'quantity_on_hand',
        'reorder_level',
        'last_low_stock_alert_sent_at',
    ];

    protected $casts = [
        'quantity_on_hand' => 'decimal:2',
        'reorder_level' => 'decimal:2',
        'last_low_stock_alert_sent_at' => 'datetime',
    ];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(InventoryTransaction::class)->orderBy('transaction_date', 'desc');
    }

    public function requisitionItems(): HasMany
    {
        return $this->hasMany(StoreRequisitionItem::class);
    }

    public function isLowStock(): bool
    {
        return $this->quantity_on_hand <= $this->reorder_level && $this->reorder_level > 0;
    }

    public function isOutOfStock(): bool
    {
        return $this->quantity_on_hand <= 0;
    }

    /**
     * Determine if a low stock notification should be sent.
     * Following the same pattern as CashFloat model.
     */
    public function shouldSendLowStockNotification(): bool
    {
        // Check if reorder_level is set
        if ($this->reorder_level === null || $this->reorder_level <= 0) {
            return false;
        }

        // Only send notification if current stock is below reorder level
        if ($this->quantity_on_hand > $this->reorder_level) {
            return false;
        }

        // Prevent duplicate alerts - don't send if alert was sent in last 24 hours
        if ($this->last_low_stock_alert_sent_at && $this->last_low_stock_alert_sent_at->diffInHours(now()) < 24) {
            return false;
        }

        return true;
    }

    /**
     * Mark that a low stock alert has been sent
     */
    public function markLowStockAlertSent(): void
    {
        $this->update(['last_low_stock_alert_sent_at' => now()]);
    }

    /**
     * Check if the unit of measure is discrete (whole numbers only)
     */
    public function isDiscreteUnit(): bool
    {
        return in_array(strtolower(trim($this->unit_of_measure)), self::DISCRETE_UNITS);
    }

    /**
     * Check if the unit of measure is continuous (allows decimals)
     */
    public function isContinuousUnit(): bool
    {
        return in_array(strtolower(trim($this->unit_of_measure)), self::CONTINUOUS_UNITS);
    }

    /**
     * Check if the unit of measure is custom (not in predefined lists)
     */
    public function isCustomUnit(): bool
    {
        return !$this->isDiscreteUnit() && !$this->isContinuousUnit();
    }

    /**
     * Determine if quantities should be whole numbers for this unit
     */
    public function shouldUseWholeNumbers(): bool
    {
        return $this->isDiscreteUnit();
    }

    /**
     * Get all available unit types for frontend
     */
    public static function getUnitTypes(): array
    {
        return [
            'discrete' => self::DISCRETE_UNITS,
            'continuous' => self::CONTINUOUS_UNITS,
        ];
    }

    /**
     * Validate quantity based on unit type
     */
    public function validateQuantity($quantity): bool
    {
        if ($this->shouldUseWholeNumbers()) {
            return is_numeric($quantity) && $quantity == floor($quantity) && $quantity >= 0;
        }
        
        return is_numeric($quantity) && $quantity >= 0;
    }
}
