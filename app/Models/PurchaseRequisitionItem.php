<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PurchaseRequisitionItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'purchase_requisition_id',
        'inventory_item_id',
        'item_name',
        'description',
        'specifications',
        'quantity',
        'unit_of_measure',
        'estimated_unit_price',
        'estimated_total_price',
        'urgency_level',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'estimated_unit_price' => 'decimal:2',
        'estimated_total_price' => 'decimal:2',
    ];

    // --- Relationships ---

    public function purchaseRequisition(): BelongsTo
    {
        return $this->belongsTo(PurchaseRequisition::class, 'purchase_requisition_id');
    }

    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(InventoryItem::class, 'inventory_item_id');
    }

    // --- Scopes ---

    public function scopeByUrgency($query, $urgencyLevel)
    {
        return $query->where('urgency_level', $urgencyLevel);
    }

    public function scopeHighPriority($query)
    {
        return $query->whereIn('urgency_level', ['high', 'critical']);
    }

    public function scopeCritical($query)
    {
        return $query->where('urgency_level', 'critical');
    }

    // --- Accessors ---

    public function getFormattedEstimatedUnitPriceAttribute()
    {
        return number_format($this->estimated_unit_price, 2);
    }

    public function getFormattedEstimatedTotalPriceAttribute()
    {
        return number_format($this->estimated_total_price, 2);
    }

    public function getFormattedQuantityAttribute()
    {
        return number_format($this->quantity, 2);
    }

    public function getUrgencyLabelAttribute()
    {
        return match($this->urgency_level) {
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High',
            'critical' => 'Critical',
            default => ucfirst($this->urgency_level)
        };
    }

    public function getUrgencyColorAttribute()
    {
        return match($this->urgency_level) {
            'low' => 'green',
            'medium' => 'yellow',
            'high' => 'orange',
            'critical' => 'red',
            default => 'gray'
        };
    }

    // --- Methods ---

    public function calculateTotalPrice()
    {
        $this->estimated_total_price = $this->quantity * $this->estimated_unit_price;
        return $this->estimated_total_price;
    }

    public function updatePurchaseRequisitionTotal()
    {
        $this->purchaseRequisition->calculateTotalAmount();
    }

    // --- Boot Method ---

    protected static function booted(): void
    {
        static::saving(function (PurchaseRequisitionItem $item) {
            // Automatically calculate total price
            $item->estimated_total_price = $item->quantity * $item->estimated_unit_price;
        });

        static::saved(function (PurchaseRequisitionItem $item) {
            // Update the parent purchase requisition total
            $item->updatePurchaseRequisitionTotal();
        });

        static::deleted(function (PurchaseRequisitionItem $item) {
            // Update the parent purchase requisition total after deletion
            $item->updatePurchaseRequisitionTotal();
        });
    }
}
