<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class Supplier extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'contact_person',
        'email',
        'phone',
        'address',
        'tax_number',
        'registration_number',
        'company_description',
        'established_year',
        'website',
        'password',
    ];

    // Security-sensitive fields that should not be mass assignable
    protected $guarded = [
        'status',
        'is_verified',
        'verification_date',
        'overall_rating',
        'total_orders',
        'successful_deliveries',
        'email_verified_at',
        'last_login_at',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'verification_date' => 'datetime',
        'last_login_at' => 'datetime',
        'is_verified' => 'boolean',
        'overall_rating' => 'decimal:2',
        'total_orders' => 'integer',
        'successful_deliveries' => 'integer',
        'established_year' => 'integer',
        'password' => 'hashed',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    public function scopeBlacklisted($query)
    {
        return $query->where('status', 'blacklisted');
    }

    // Accessors
    public function getSuccessRateAttribute()
    {
        if ($this->total_orders === 0) {
            return 0;
        }
        
        return round(($this->successful_deliveries / $this->total_orders) * 100, 2);
    }

    public function getIsActiveAttribute()
    {
        return $this->status === 'active';
    }

    // Methods
    public function canParticipateInTenders()
    {
        return $this->is_verified && $this->status === 'active';
    }

    public function updatePerformanceMetrics($ordersIncrement = 0, $successfulDeliveriesIncrement = 0)
    {
        $this->increment('total_orders', $ordersIncrement);
        $this->increment('successful_deliveries', $successfulDeliveriesIncrement);
    }

    public function blacklist($reason = null)
    {
        $this->update(['status' => 'blacklisted']);
    }

    public function verify()
    {
        $this->update([
            'is_verified' => true,
            'verification_date' => now(),
        ]);
    }
}
