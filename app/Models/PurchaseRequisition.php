<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class PurchaseRequisition extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'branch_id',
        'department_id',
        'requester_user_id',
        'approval_workflow_id',
        'current_approval_step_id',
        'requisition_number',
        'purpose',
        'required_by_date',
        'estimated_total_amount',
        'budget_code',
        'status',
        'requisition_type',
    ];

    protected $casts = [
        'estimated_total_amount' => 'decimal:2',
        'required_by_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // --- Relationships ---

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class, 'branch_id');
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

    public function requester(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requester_user_id');
    }

    public function approvalWorkflow(): BelongsTo
    {
        return $this->belongsTo(ApprovalWorkflow::class, 'approval_workflow_id');
    }

    public function currentApprovalStep(): BelongsTo
    {
        return $this->belongsTo(ApprovalWorkflowStep::class, 'current_approval_step_id');
    }

    public function items(): HasMany
    {
        return $this->hasMany(PurchaseRequisitionItem::class, 'purchase_requisition_id');
    }

    public function attachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    // --- Scopes ---

    public function scopeByOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeByDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByRequester($query, $userId)
    {
        return $query->where('requester_user_id', $userId);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending_approval');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    // --- Accessors ---

    public function getFormattedEstimatedTotalAttribute()
    {
        return number_format($this->estimated_total_amount, 2);
    }

    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'draft' => 'Draft',
            'pending_approval' => 'Pending Approval',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
            'converted_to_tender' => 'Converted to Tender',
            'cancelled' => 'Cancelled',
            default => ucfirst($this->status)
        };
    }

    public function getRequisitionTypeLabelAttribute()
    {
        return match($this->requisition_type) {
            'store_replenishment' => 'Store Replenishment',
            'capital_expenditure' => 'Capital Expenditure',
            'operational' => 'Operational',
            default => ucfirst(str_replace('_', ' ', $this->requisition_type))
        };
    }

    // --- Methods ---

    public function calculateTotalAmount()
    {
        $this->estimated_total_amount = $this->items()->sum('estimated_total_price');
        $this->save();
        
        return $this->estimated_total_amount;
    }

    public function canBeEdited(): bool
    {
        return in_array($this->status, ['draft']);
    }

    public function canBeSubmitted(): bool
    {
        return $this->status === 'draft' && $this->items()->count() > 0;
    }

    public function canBeApproved(): bool
    {
        return $this->status === 'pending_approval';
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['draft', 'pending_approval']);
    }

    // --- Boot Method ---

    protected static function booted(): void
    {
        static::creating(function (PurchaseRequisition $purchaseRequisition) {
            if (empty($purchaseRequisition->requisition_number)) {
                $purchaseRequisition->requisition_number = $purchaseRequisition->generateRequisitionNumber();
            }
        });
    }

    private function generateRequisitionNumber(): string
    {
        $prefix = 'PR';
        $year = date('Y');
        $month = date('m');
        
        // Get the last requisition number for this organization
        $lastRequisition = static::where('organization_id', $this->organization_id)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();
        
        $sequence = 1;
        if ($lastRequisition) {
            // Extract sequence number from last requisition
            $lastNumber = $lastRequisition->requisition_number;
            if (preg_match('/PR' . $year . $month . '(\d{4})/', $lastNumber, $matches)) {
                $sequence = intval($matches[1]) + 1;
            }
        }
        
        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
