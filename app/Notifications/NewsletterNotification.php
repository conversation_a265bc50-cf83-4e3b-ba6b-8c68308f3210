<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewsletterNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $updates;

    public function __construct($updates)
    {
        $this->updates = $updates;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Sippar Newsletter: Latest Updates')
            ->markdown('emails.newsletter', [
                'updates' => $this->updates,
            ]);
    }
}
