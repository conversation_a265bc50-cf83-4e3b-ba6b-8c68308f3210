<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class NewsletterMailable extends Mailable
{
    use Queueable, SerializesModels;

    public $updates;

    public function __construct($updates)
    {
        $this->updates = $updates;
    }

    public function build()
    {
        return $this->view('emails.newsletter')
            ->subject('Sippar Newsletter: Latest Updates')
            ->with([
                'updates' => $this->updates
            ]);
    }
}