<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        '/newsletter',
        'newsletter',
        '/api/newsletter',
        'api/newsletter',
        '*newsletter*', // wildcard for any newsletter route
    ];
}
