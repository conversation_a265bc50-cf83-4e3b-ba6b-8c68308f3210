<?php

namespace App\Http\Requests\StoreRequisition;

use App\Rules\ValidateQuantityForUnit;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use App\Models\InventoryItem;

class StoreStoreRequisitionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return Auth::user()->can('create-store-requisition') || Auth::user()->can('store-keep');
    }

    public function rules(): array
    {
        $rules = [
            'branch_id' => 'required|exists:branches,id',
            'department_id' => 'required|exists:departments,id',
            'purpose' => 'required|string|max:1000',
            'items' => 'required|array|min:1|max:50',
            'items.*.inventory_item_id' => 'required|exists:inventory_items,id',
            'items.*.quantity_requested' => 'required|numeric|gt:0|max:999999.99',
            'save_as_draft' => 'sometimes|boolean',
        ];

        // Add unit-specific quantity validation for each item
        $items = $this->input('items', []);
        foreach ($items as $index => $item) {
            if (isset($item['inventory_item_id'])) {
                $rules["items.{$index}.quantity_requested"][] = new ValidateQuantityForUnit($item['inventory_item_id']);
            }
        }

        return $rules;
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $this->validateNoDuplicateItems($validator);
            $this->validateStockAvailability($validator);
        });
    }

    protected function validateNoDuplicateItems($validator)
    {
        $items = $this->input('items', []);
        $inventoryItemIds = array_column($items, 'inventory_item_id');

        if (count($inventoryItemIds) !== count(array_unique($inventoryItemIds))) {
            $validator->errors()->add('items', 'Duplicate items are not allowed. Each inventory item can only be added once.');
        }
    }

    protected function validateStockAvailability($validator)
    {
        $items = $this->input('items', []);
        $user = Auth::user();
        $organization = $user->organizations()->first();

        if (!$organization) {
            return;
        }

        foreach ($items as $index => $item) {
            if (!isset($item['inventory_item_id'])) {
                continue;
            }

            $inventoryItem = InventoryItem::where('id', $item['inventory_item_id'])
                ->where('organization_id', $organization->id)
                ->first();

            if (!$inventoryItem) {
                $validator->errors()->add("items.{$index}.inventory_item_id", 'Selected inventory item not found.');
                continue;
            }

            if ($inventoryItem->quantity_on_hand <= 0) {
                $validator->errors()->add("items.{$index}.inventory_item_id", "Item '{$inventoryItem->name}' is out of stock and cannot be requested.");
            }
        }
    }

    public function messages(): array
    {
        return [
            'branch_id.required' => 'Branch is required.',
            'department_id.required' => 'Department is required.',
            'purpose.required' => 'Purpose is required.',
            'items.required' => 'At least one item is required.',
            'items.min' => 'At least one item is required.',
            'items.max' => 'Maximum 50 items allowed.',
            'items.*.inventory_item_id.required' => 'Inventory item is required.',
            'items.*.quantity_requested.required' => 'Quantity is required.',
            'items.*.quantity_requested.min' => 'Quantity must be greater than 0.',
        ];
    }
}
