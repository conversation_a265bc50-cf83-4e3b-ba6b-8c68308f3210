<?php

namespace App\Http\Requests\SupplierManagement;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class StoreSupplierRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Allow guests to register suppliers
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:100'],
            'last_name' => ['required', 'string', 'max:100'],
            'email' => ['required', 'email', 'max:255', 'unique:suppliers,email'],
            'phone' => ['required', 'string', 'max:20', 'unique:suppliers,phone'],
            'org_name' => ['required', 'string', 'max:255'],
            'address' => ['nullable', 'string', 'max:255'],
            'tax_number' => ['nullable', 'string', 'max:50'],
            'registration_number' => ['nullable', 'string', 'max:50'],
            'company_description' => ['nullable', 'string', 'max:1000'],
            'established_year' => ['nullable', 'integer', 'digits:4', 'before_or_equal:' . date('Y')],
            'website' => ['nullable', 'url', 'max:255'],
            'password' => ['required', 'confirmed', Password::defaults()],
        ];
    }

    /**
     * Custom error messages for validation.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'First name is required.',
            'last_name.required' => 'Last name is required.',
            'email.required' => 'An email address is required.',
            'email.email' => 'The email must be a valid address.',
            'email.unique' => 'That email is already registered.',
            'phone.required' => 'A phone number is required.',
            'phone.unique' => 'That phone number is already registered.',
            'org_name.required' => 'Organization name is required.',
            'password.confirmed' => 'Password confirmation does not match.',
            'established_year.before_or_equal' => 'Established year cannot be in the future.',
        ];
    }
}
