<?php

namespace App\Http\Controllers;

use App\Models\ChartOfAccount;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;
use Src\Disbursement\Application\Services\DisbursementService;

class DisbursementController extends Controller
{
    /**
     * @var DisbursementService
     */
    protected $disbursementService;

    /**
     * DisbursementController constructor.
     *
     * @param DisbursementService $disbursementService
     */
    public function __construct(DisbursementService $disbursementService)
    {
        $this->disbursementService = $disbursementService;
    }

    /**
     * Display a listing of transactions.
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $user = Auth::user();

        // Get filters from request
        $filters = [
            'search' => $request->input('search'),
            'status' => $request->input('status'),
            'sort' => $request->input('sort', 'created_at'),
            'direction' => $request->input('direction', 'desc'),
        ];

        // Get transactions where the user is the requisition creator
        $transactions = $this->disbursementService->getTransactionsForRequester(
            $user->id,
            $filters,
            10
        );

        return Inertia::render('Disbursement/Index', [
            'transactions' => $transactions,
            'filters' => $filters,
        ]);
    }

    /**
     * Display the specified transaction.
     *
     * @param int $id
     * @return Response
     */
    public function show(int $id): Response
    {
        $user = Auth::user();
        $transaction = $this->disbursementService->getTransactionWithItems($id);

        if (!$transaction) {
            abort(404, 'Transaction not found');
        }

        // Check if the user is the requisition creator
        if (!$this->disbursementService->isRequester($transaction, $user->id)) {
            abort(403, 'You are not authorized to view this transaction');
        }

        // Load attachments for the transaction
        $transaction->load('attachments.uploader');

        // Get chart of accounts for displaying item categories
        $org = $user->organizations()->first();
        if (!$org) {
            abort(400, 'User must belong to an organization to view requisitions');
        }
        $organizationId = $org->id;
        // Get chart of accounts filtered by user role
        $chartOfAccounts = $this->getFilteredChartOfAccounts($user, $organizationId);
        if ($chartOfAccounts->isEmpty()) {
            abort(400, 'No chart of accounts available for this organization');
        }

        // Check if user can attach files to this transaction
        $canAttachFiles = $user->is_platform_admin ||
                         $this->disbursementService->isRequester($transaction, $user->id) ||
                         $user->hasRole('Finance Manager') ||
                         $user->hasRole('Cashier') ||
                         $user->hasRole('Organization Admin');

        return Inertia::render('Disbursement/Show', [
            'transaction' => $transaction,
            'chartOfAccounts' => $chartOfAccounts,
            'canUpdate' => $transaction->status === 'opened',
            'canAttachFiles' => $canAttachFiles,
        ]);
    }

        /**
     * Get filtered chart of accounts based on user role
     *
     * @param \App\Models\User $user
     * @param int $organizationId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getFilteredChartOfAccounts($user, int $organizationId)
    {
        // Check if user is finance manager, organization admin, or platform admin
        if ($user->is_platform_admin ||
            $user->hasRole('Finance Manager') ||
            $user->hasRole('Finance Admin') ||
            $user->hasRole('Organization Admin')) {

            // Return all chart of accounts for the organization
            return ChartOfAccount::getFinanceFilteredAccounts($organizationId);
        }

        // For regular employees, return only descendant accounts (leaf nodes)
        return ChartOfAccount::getEmployeeFilteredAccounts($organizationId);
    }

    /**
     * Show the form for providing account details.
     *
     * @param int $id
     * @return Response
     */
    public function edit(int $id): Response
    {
        $user = Auth::user();
        $transaction = $this->disbursementService->getTransaction($id);

        if (!$transaction) {
            abort(404, 'Transaction not found');
        }

        // Check if the user is the requisition creator
        if (!$this->disbursementService->isRequester($transaction, $user->id)) {
            abort(403, 'You are not authorized to edit this transaction');
        }

        if ($transaction->status !== 'opened') {
            return redirect()->route('disbursement.show', $id)
                ->with('error', 'This transaction cannot be updated because it is not in opened status');
        }

        return Inertia::render('Disbursement/Edit', [
            'transaction' => $transaction,
        ]);
    }

    /**
     * Update the transaction with account details.
     *
     * @param Request $request
     * @param int $id
     * @return RedirectResponse
     */
    public function update(Request $request, int $id): RedirectResponse
    {
        try {
            $user = Auth::user();
            $transaction = $this->disbursementService->getTransaction($id);

            if (!$transaction) {
                abort(404, 'Transaction not found');
            }

            // Check if the user is the requisition creator
            if (!$this->disbursementService->isRequester($transaction, $user->id)) {
                abort(403, 'You are not authorized to update this transaction');
            }

            $validated = $request->validate([
                'account_name' => 'required|string|max:255',
                'account_number' => 'required|string|max:255',
                'additional_details' => 'nullable|string|max:1000',
            ]);

            $transaction = $this->disbursementService->updateTransactionWithAccountDetails($id, $validated);

            // Fire TransactionPaymentDetailsUpdated event since status becomes 'updated'
            event(new \App\Events\TransactionPaymentDetailsUpdated($transaction));

            return redirect()->route('disbursement.show', $id);
        } catch (\Exception $e) {
            Log::error('Error updating transaction with account details: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'An error occurred while updating the transaction: ' . $e->getMessage())
                ->withInput();
        }
    }


}
