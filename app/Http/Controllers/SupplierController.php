<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Inertia\Response;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use App\Models\Supplier;
use App\Http\Requests\SupplierManagement\StoreSupplierRequest;
use App\Http\Requests\Auth\SupplierLoginRequest;
use Src\SupplierManagement\Domain\Services\MetricServiceInterface;
use Src\SupplierManagement\Domain\Services\SupplierServiceInterface;

class SupplierController extends Controller
{
    public function __construct(
        private SupplierServiceInterface $supplierService,
        private MetricServiceInterface $metricsService
    ) {}

    /**
     * Initialize a new supplier registration form
     * This method is called when the user first accesses the "/supplier/register" route
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function create()
    {
        return Inertia::render('suppliers/register');
    }

    /**
     * Display the supplier dashboard.
     */
    public function dashboard()
    {
        $supplier = Auth::guard('supplier')->user();

        return Inertia::render('suppliers/dashboard', [
            'supplier' => $supplier,
        ]);
    }

    /**
     * Show the supplier login page.
     */
    public function login(): Response
    {
        return Inertia::render('suppliers/login', [
            'canResetPassword' => Route::has('password.request'),
            'status' => session('status'),
        ]);
    }

    /**
     * Handle an incoming supplier authentication request.
     */
    public function authorize(SupplierLoginRequest $request): RedirectResponse
    {
        $request->authenticate();

        $request->session()->regenerate();

        // Update last login timestamp
        Auth::guard('supplier')->user()->update([
            'last_login_at' => now(),
        ]);

        return redirect()->intended(route('suppliers.dashboard'));
    }

    /**
     * Destroy an authenticated supplier session.
     */
    public function logout(Request $request): RedirectResponse
    {
        Auth::guard('supplier')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('suppliers.login');
    }

    /**
     * Store a new supplier.
     */
    public function store(StoreSupplierRequest $request)
    {
        try {
            $supplierData = $request->validated();

            $supplier = $this->supplierService->createSupplier($supplierData);

            if (empty($supplier)) {
                return back()->withErrors(['error' => 'Failed to create supplier. Please try again.'])->withInput();
            }

            return redirect()->route('suppliers.dashboard')->with('success', 'Supplier account created successfully!');
        } catch (\Exception $e) {
            Log::error('Supplier registration failed in controller: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return back()->withErrors(['error' => 'An unexpected error occurred during registration. Please try again.'])->withInput();
        }
    }

    /**
     * Display supplier dashboard with overview data.
     */
    public function index(Request $request): Response|JsonResponse
    {
        if (!Auth::user()->can('view-suppliers')) {
            abort(403, 'Unauthorized to view suppliers');
        }

        $user = Auth::user();
        $organization = $user->organizations()->first();
        
        if (!$organization) {
            abort(400, 'User must belong to an organization to view suppliers');
        }

        try {
            // Get overview data
            $overview = $this->supplierService->getSupplierOverview($organization->id);
            $recentActivity = $this->supplierService->getRecentActivity($organization->id, 10);
            $topSuppliers = $this->metricsService->getTopSuppliers($organization->id, 5);

            // Get quick stats for dashboard
            $stats = [
                'total_suppliers' => $overview['total_suppliers'] ?? 0,
                'active_suppliers' => $overview['active_suppliers'] ?? 0,
                'pending_approvals' => $overview['pending_approvals'] ?? 0,
                'total_quotations' => $overview['total_quotations'] ?? 0,
            ];

            // Check if this is an API request
            if ($request->expectsJson() || $request->is('api/*')) {
                return response()->json([
                    'overview' => $overview,
                    'recent_activity' => $recentActivity,
                    'top_suppliers' => $topSuppliers,
                    'stats' => $stats,
                ]);
            }

            // Return Inertia view for web interface
            return Inertia::render('Suppliers/SupplierDashboard', [
                'overview' => $overview,
                'recent_activity' => $recentActivity,
                'top_suppliers' => $topSuppliers,
                'stats' => $stats,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->first_name . ' ' . $user->last_name,
                    'email' => $user->email,
                    'permissions' => $user->getAllPermissions()->pluck('name')->toArray(),
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to load supplier dashboard: ' . $e->getMessage());
            
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Failed to load supplier data'], 500);
            }
            
            return redirect()->back()->with('error', 'Failed to load supplier dashboard');
        }
    }

    /**
     * Display list of all suppliers.
     */
   public function suppliers(Request $request): Response|JsonResponse
    {
        if (!Auth::user()->can('view-suppliers')) {
            abort(403, 'Unauthorized to view suppliers');
        }

        $user = Auth::user();
        $organization = $user->organizations()->first();

        if (!$organization) {
            abort(400, 'User must belong to an organization');
        }

        $filters = $request->only(['status', 'category', 'search', 'sort_by', 'sort_order']);
        $perPage = $request->input('per_page', 15);

        try {
            $suppliers = $this->supplierService->getSuppliers($organization->id, $filters, $perPage);
            $categories = $this->supplierService->getSupplierCategories($organization->id);
            $stats = $this->metricsService->getSupplierStats($organization->id);

            if ($request->expectsJson()) {
                return response()->json([
                    'suppliers' => $suppliers,
                    'categories' => $categories,
                    'stats' => $stats,
                    'filters' => $filters,
                ]);
            }

            return Inertia::render('Suppliers/SupplierList', [
                'suppliers' => $suppliers,
                'categories' => $categories,
                'stats' => $stats,
                'filters' => $filters,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to load suppliers: ' . $e->getMessage());
            
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Failed to load suppliers'], 500);
            }
            
            return redirect()->back()->with('error', 'Failed to load suppliers');
        }
    }

    /**
     * Show specific supplier details.
     */
   public function show(Supplier $supplier, Request $request): Response|JsonResponse
    {
        if (!Auth::user()->can('view-suppliers')) {
            abort(403, 'Unauthorized to view supplier details');
        }

        $user = Auth::user();
        $organization = $user->organizations()->first();

        // Check if user belongs to user's organization or is accessible
        if (!$organization || !$this->canAccessSupplier($supplier, $organization)) {
            abort(403, 'Unauthorized to view this supplier');
        }

        try {
            // Load supplier with relationships
            $supplier->load([
                'contacts',
                'addresses',
                'documents',
                'quotations' => function ($query) {
                    $query->latest()->limit(5);
                },
                'purchaseOrders' => function ($query) {
                    $query->latest()->limit(5);
                },
                'performanceHistory' => function ($query) {
                    $query->latest()->limit(12);
                }
            ]);

            // Get performance metrics for this supplier
            $performance = $this->metricsService->getSupplierPerformanceDetails($supplier->id);
            $recentActivity = $this->supplierService->getSupplierActivity($supplier->id, 10);

            if ($request->expectsJson()) {
                return response()->json([
                    'supplier' => $supplier,
                    'performance' => $performance,
                    'recent_activity' => $recentActivity,
                ]);
            }

            return Inertia::render('Suppliers/SupplierDetails', [
                'supplier' => $supplier,
                'performance' => $performance,
                'recent_activity' => $recentActivity,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to load supplier details: ' . $e->getMessage());
            
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Failed to load supplier details'], 500);
            }
            
            return redirect()->back()->with('error', 'Failed to load supplier details');
        }
    }

    /**
     * Get supplier metrics for dashboard.
     */
    public function metrics(Request $request): JsonResponse
    {
        if (!Auth::user()->can('view-suppliers')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $user = Auth::user();
        $organization = $user->organizations()->first();

        if (!$organization) {
            return response()->json(['error' => 'No organization found'], 400);
        }

        try {
            $metrics = $this->metricsService->getSupplierMetrics($organization->id, [
                'period' => $request->input('period', '30_days'),
                'include_performance' => $request->boolean('include_performance', true),
                'include_financial' => $request->boolean('include_financial', true),
            ]);

            return response()->json($metrics);

        } catch (\Exception $e) {
            Log::error('Failed to load supplier metrics: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to load metrics'], 500);
        }
    }

     /**
     * Get supplier quotations.
     */
    public function quotations(Request $request): JsonResponse
    {
        if (!Auth::user()->can('view-suppliers')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $user = Auth::user();
        $organization = $user->organizations()->first();

        if (!$organization) {
            return response()->json(['error' => 'No organization found'], 400);
        }

        try {
            $filters = $request->only(['status', 'supplier_id', 'date_from', 'date_to']);
            $perPage = $request->input('per_page', 15);

            $quotations = $this->supplierService->getSupplierQuotations($organization->id, $filters, $perPage);

            return response()->json([
                'quotations' => $quotations,
                'filters' => $filters,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to load supplier quotations: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to load quotations'], 500);
        }
    }

    /**
     * Get purchase order statuses.
     */
    public function purchaseOrders(Request $request): JsonResponse
    {
        if (!Auth::user()->can('view-suppliers')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $user = Auth::user();
        $organization = $user->organizations()->first();

        if (!$organization) {
            return response()->json(['error' => 'No organization found'], 400);
        }

        try {
            $filters = $request->only(['status', 'supplier_id', 'date_from', 'date_to']);
            $perPage = $request->input('per_page', 15);

            $purchaseOrders = $this->supplierService->getSupplierPurchaseOrders($organization->id, $filters, $perPage);

            return response()->json([
                'purchase_orders' => $purchaseOrders,
                'filters' => $filters,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to load purchase orders: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to load purchase orders'], 500);
        }
    }

    /**
     * Update supplier status (approve/reject registrations).
     */
    public function updateStatus(Supplier $supplier, Request $request): JsonResponse
    {
        if (!Auth::user()->can('manage-suppliers')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $request->validate([
            'status' => 'required|in:active,inactive,pending_approval,rejected',
            'rejection_reason' => 'required_if:status,rejected|string|max:500',
        ]);

        $user = Auth::user();
        $organization = $user->organizations()->first();

        if (!$organization || !$this->canAccessSupplier($supplier, $organization)) {
            return response()->json(['error' => 'Unauthorized to modify this supplier'], 403);
        }

        try {
            DB::beginTransaction();

            $supplier->update([
                'status' => $request->status,
                'rejection_reason' => $request->rejection_reason,
                'approved_by' => $request->status === 'active' ? Auth::id() : null,
                'approved_at' => $request->status === 'active' ? now() : null,
            ]);

            // Log the status change
            $this->supplierService->logStatusChange($supplier, $request->status, Auth::id());

            DB::commit();

            return response()->json([
                'message' => 'Supplier status updated successfully',
                'supplier' => $supplier->fresh(),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to update supplier status: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update supplier status'], 500);
        }
    }

    /**
     * Show the document upload page for suppliers.
     */
    public function documents(): Response
    {
        $supplier = Auth::guard('supplier')->user();

        return Inertia::render('suppliers/document-upload', [
            'supplier' => $supplier,
        ]);
    }

    /**
     * Handle document upload for suppliers (frontend-only for now).
     */
    public function uploadDocuments(Request $request): RedirectResponse
    {
        // For frontend-only implementation, just redirect back with success message
        return redirect()->route('suppliers.dashboard')
            ->with('success', 'Documents uploaded successfully! We will review them within 2-3 business days.');
    }

    /**
     * Check if user can access supplier data.
     */
    private function canAccessSupplier(Supplier $supplier, $organization): bool
    {
        // Supplier belongs to the organization or organization has access
        return $supplier->organization_id === $organization->id || 
               $supplier->accessible_by_organizations->contains($organization->id);
    }
}
