<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Notifications\AnonymousNotifiable;
use App\Notifications\NewsletterNotification;
use App\Models\NewsletterSubscription;

class NewsletterController extends Controller
{
    public function subscribe(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email:rfc,dns',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => 'Invalid email address.'], 422);
        }

        $email = $request->input('email');

        // Generate dynamic updates using the Artisan command
        $updates = \Illuminate\Support\Facades\Artisan::call('newsletter:generate');
        $updates = \Illuminate\Support\Facades\Artisan::output();

        // Add to subscription list if not already present
        NewsletterSubscription::firstOrCreate(['email' => $email]);

        // Send the newsletter notification using Laravel Notification system
        try {
            (new AnonymousNotifiable)->route('mail', $email)
                ->notify(new NewsletterNotification($updates));
        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to send newsletter: ' . $e->getMessage()], 500);
        }

        return response()->json(['message' => 'Newsletter sent successfully!']);
    }
}
