<?php

namespace App\Http\Controllers;

use App\Models\CashFloat;
use App\Models\Organization;
use App\Models\Department;
use App\Models\ChartOfAccount;
use App\Models\Branch;
use App\Models\User;
use App\Traits\ValidatesCashFloatTransactions;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;

class CashFloatController extends Controller
{
    use ValidatesCashFloatTransactions;

    /**
     * Display a listing of cash floats.
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // Check if user has permission to view cash floats
        if (!$this->hasFloatPermission($user, ['view-cash-floats', 'manage-cash-floats', 'view-float', 'manage-floats'])) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'You do not have permission to view cash floats.',
                'user' => $user,
            ]);
        }

        // Get organization from user's roles with cash float permissions
        $organization = $this->getUserOrganization($user);

        if (!$organization) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'No organization found for your account with cash float permissions.',
                'user' => $user,
            ]);
        }

        $cashFloats = CashFloat::with(['department', 'branch', 'user'])
            ->where('organization_id', $organization->id)
            ->latest()
            ->paginate(10);

        return Inertia::render('CashFloats/Index', [
            'cashFloats' => $cashFloats,
            'organization' => $organization,
        ]);
    }

    /**
     * Show the form for creating a new cash float.
     */
    public function create(Request $request)
    {
        $user = $request->user();

        // Check if user has permission to create cash floats
        if (!$this->hasFloatPermission($user, ['create-cash-float', 'manage-cash-floats', 'create-float', 'manage-floats'])) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'You do not have permission to create cash floats.',
                'user' => $user,
            ]);
        }

        // Get organization from user's roles with cash float permissions
        $organization = $this->getUserOrganization($user);

        if (!$organization) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'No organization found for your account with cash float permissions.',
                'user' => $user,
            ]);
        }

        // Get departments, branches, and users for the organization
        $departments = Department::where('organization_id', $organization->id)->get();
        $branches = Branch::where('organization_id', $organization->id)->get();
        $users = User::whereHas('roles', function ($query) use ($organization) {
            $query->where('organization_id', $organization->id);
        })->get();

        return Inertia::render('CashFloats/Create', [
            'departments' => $departments,
            'branches' => $branches,
            'users' => $users,
            'organization' => $organization,
        ]);
    }

    /**
     * Store a newly created cash float in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'organization_id' => 'required|exists:organizations,id',
            'branch_id' => 'nullable|exists:branches,id',
            'department_id' => 'nullable|exists:departments,id',
            'user_id' => 'nullable|exists:users,id',
            'name' => 'required|string|max:255',
            'initial_amount' => 'required|numeric|min:0',
            'issued_at' => 'required|date',
            'alert_threshold' => 'nullable|numeric|min:0',
            'status' => 'required|in:active,inactive,reconciled,closed',
            'transaction_cost' => 'nullable|numeric|min:0',
            'payment_method' => 'nullable|in:mpesa,bank,cash',
            'account_details' => 'nullable|string|max:65535',
            'reference_number' => 'nullable|string|max:255',
        ]);

        // Validate mutual exclusivity of assignment
        $assignmentCount = collect([
            $validated['branch_id'],
            $validated['department_id'],
            $validated['user_id']
        ])->filter()->count();

        if ($assignmentCount > 1) {
            return back()->withErrors([
                'assignment' => 'Cash float can only be assigned to one of: branch, department, or user.'
            ])->withInput();
        }

        try {
            DB::beginTransaction();

            // Create cash float first
            $cashFloat = CashFloat::create([
                'organization_id' => $validated['organization_id'],
                'branch_id' => $validated['branch_id'],
                'department_id' => $validated['department_id'],
                'user_id' => $validated['user_id'],
                'name' => $validated['name'],
                'initial_amount' => $validated['initial_amount'],
                'issued_at' => $validated['issued_at'],
                'alert_threshold' => $validated['alert_threshold'],
                'status' => $validated['status'],
            ]);

            // Create initial transaction record with cash_float_id
            $cashFloat->transactions()->create([
                'transaction_type' => 'float_issuance',
                'total_amount' => $validated['initial_amount'],
                'transaction_cost' => $validated['transaction_cost'] ?? 0,
                'payment_method' => $validated['payment_method'] ?? 'cash',
                'account_details' => $validated['account_details'],
                'disbursement_transaction_id' => $validated['reference_number'],
                'status' => 'completed',
                'created_by' => $request->user()->id,
            ]);

            DB::commit();

            return redirect()->route('cash-floats.show', $cashFloat)
                ->with('success', 'Cash float created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Failed to create cash float: ' . $e->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified cash float.
     */
    // public function edit(CashFloat $cashFloat)
    // {
    //     $organization = $cashFloat->organization;

    //     $departments = Department::where('organization_id', $organization->id)->get();
    //     $branches = Branch::where('organization_id', $organization->id)->get();
    //     $users = User::whereHas('roles', function ($query) use ($organization) {
    //         $query->where('organization_id', $organization->id);
    //     })->get();

    //     return Inertia::render('CashFloats/Edit', [
    //         'cashFloat' => $cashFloat,
    //         'departments' => $departments,
    //         'branches' => $branches,
    //         'users' => $users,
    //     ]);
    // }

    /**
     * Update the specified cash float in storage.
     */
    public function update(Request $request, CashFloat $cashFloat)
    {
        $validated = $request->validate([
            'branch_id' => 'nullable|exists:branches,id',
            'department_id' => 'nullable|exists:departments,id',
            'user_id' => 'nullable|exists:users,id',
            'name' => 'required|string|max:255',
            'alert_threshold' => 'nullable|numeric|min:0',
            'status' => 'required|in:active,inactive,reconciled,closed',
        ]);

        try {
            DB::beginTransaction();

            $cashFloat->update($validated);

            // No need to update is_low status here since it's not stored in database
            // The alert status is checked via the isBelowAlertThreshold() method

            DB::commit();

            return redirect()->route('cash-floats.show', $cashFloat)
                ->with('success', 'Cash float updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Failed to update cash float: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified cash float from storage.
     */
    public function destroy(CashFloat $cashFloat)
    {
        try {
            DB::beginTransaction();

            // Only allow deletion if there are no transactions
            if ($cashFloat->transactions()->count() > 0) {
                throw new \Exception('Cannot delete cash float with existing transactions.');
            }

            $cashFloat->delete();

            DB::commit();

            return redirect()->route('cash-floats.index')
                ->with('success', 'Cash float deleted successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Failed to delete cash float: ' . $e->getMessage()]);
        }
    }

    /**
     * Store a new transaction for the cash float.
     */
   public function storeTransaction(Request $request, CashFloat $cashFloat)
{
    $validated = $request->validate([
        'transaction_type' => 'required|in:reimbursement,expense,float_return,disbursement,float_issuance',
        'total_amount' => 'required|numeric|min:0.01',
        'description' => 'required|string|max:1000',
        'payment_method' => 'nullable|in:mpesa,bank,cash',
        'reference_number' => 'nullable|string|max:255',
        'vendor_details' => 'nullable|string|max:500',
        'account_details' => 'nullable|string|max:500',
        'transaction_cost' => 'nullable|numeric|min:0',
    ]);

    try {
        DB::beginTransaction();

        // Define which transaction types reduce the float balance (cash out)
        $cashOutTypes = ['expense', 'disbursement', 'float_return'];
        $cashInTypes = ['reimbursement', 'float_issuance'];

        // For cash out transactions, validate against current float balance
        if (in_array($validated['transaction_type'], $cashOutTypes)) {
            $totalDeduction = $validated['total_amount'] + ($validated['transaction_cost'] ?? 0);

            if ($cashFloat->current_balance < $totalDeduction) {
                return back()->withErrors([
                    'error' => 'Insufficient float balance. Available: ' . number_format($cashFloat->current_balance, 2) .
                              ', Required: ' . number_format($totalDeduction, 2)
                ]);
            }
        }

        // Create the transaction
        $transaction = $cashFloat->transactions()->create([
            'transaction_type' => $validated['transaction_type'],
            'total_amount' => $validated['total_amount'],
            'transaction_cost' => $validated['transaction_cost'] ?? 0,
            'payment_method' => $validated['payment_method'],
            'account_details' => $validated['account_details'] ?? $validated['vendor_details'] ?? null,
            'disbursement_transaction_id' => $validated['reference_number'],
            'description' => $validated['description'],
            'status' => 'completed',
            'created_by' => $request->user()->id,
        ]);

        // The cash float balance is calculated dynamically via the current_balance attribute
        // No need to update remaining_amount as it doesn't exist in the database
        // The balance calculation happens in the CashFloat model's getCurrentBalanceAttribute method

        $cashFloat->save();

        DB::commit();

        return redirect()->route('cash-floats.show', $cashFloat)
            ->with('success', ucfirst($validated['transaction_type']) . ' transaction recorded successfully.');

    } catch (\Exception $e) {
        DB::rollBack();
        return back()->withErrors(['error' => 'Failed to record transaction: ' . $e->getMessage()]);
    }
}

    /**
     * Debug method to check chart of accounts
     */
    public function debugChartOfAccounts(CashFloat $cashFloat)
    {
        $allAccounts = ChartOfAccount::all(['id', 'name', 'organization_id', 'is_active']);
        $orgSpecific = ChartOfAccount::where('organization_id', $cashFloat->organization_id)->get(['id', 'name', 'organization_id', 'is_active']);
        $shared = ChartOfAccount::whereNull('organization_id')->get(['id', 'name', 'organization_id', 'is_active']);
        $filtered = ChartOfAccount::where(function ($query) use ($cashFloat) {
                $query->where('organization_id', $cashFloat->organization_id)
                      ->orWhereNull('organization_id');
            })
            ->where('is_active', true)
            ->get(['id', 'name', 'organization_id', 'is_active']);

        return response()->json([
            'cash_float_org_id' => $cashFloat->organization_id,
            'all_accounts' => $allAccounts,
            'org_specific_accounts' => $orgSpecific,
            'shared_accounts' => $shared,
            'filtered_accounts' => $filtered,
        ]);
    }

    /**
     * Check if user has any of the specified float permissions.
      */
    private function hasFloatPermission(User $user, array $permissions): bool
    {
        // First check hardcoded roles for backward compatibility
        if ($user->hasRole('Finance Manager') ||
            $user->hasRole('Organization Admin') ||
            $user->hasRole('Cashier') ||
            $user->is_platform_admin) {
            return true;
        }

        // Then check for specific permissions (for custom roles)
        foreach ($permissions as $permission) {
            if ($user->can($permission)) {
                return true;
            }
        }

        // Also check for comprehensive financial permissions
        $financialPermissions = [
            'manage-finances',
            'manage-transactions',
            'manage-reconciliations'
        ];

        foreach ($financialPermissions as $permission) {
            if ($user->can($permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get user's organization based on their roles.
      */
    private function getUserOrganization(User $user): ?Organization
    {
        // First try to get organization from Finance Manager role (backward compatibility)
        $financeManagerRole = $user->roles()
            ->where('name', 'Finance Manager')
            ->first();

        if ($financeManagerRole && $financeManagerRole->organization_id) {
            return Organization::find($financeManagerRole->organization_id);
        }

        // Try Organization Admin role
        $orgAdminRole = $user->roles()
            ->where('name', 'Organization Admin')
            ->first();

        if ($orgAdminRole && $orgAdminRole->organization_id) {
            return Organization::find($orgAdminRole->organization_id);
        }

        // Try Cashier role
        $cashierRole = $user->roles()
            ->where('name', 'Cashier')
            ->first();

        if ($cashierRole && $cashierRole->organization_id) {
            return Organization::find($cashierRole->organization_id);
        }

        // Then try to get organization from any role with financial permissions (for custom roles)
        $rolesWithOrganization = $user->roles()
            ->whereNotNull('organization_id')
            ->with('permissions')
            ->get();

        foreach ($rolesWithOrganization as $role) {
            $permissions = $role->permissions->pluck('name')->toArray();

            $hasFinancialPermission = array_intersect($permissions, [
                'manage-cash-floats',
                'view-cash-floats',
                'create-cash-float',
                'manage-floats',
                'view-float',
                'create-float',
                'manage-finances',
                'manage-transactions'
            ]);

            if (!empty($hasFinancialPermission)) {
                return Organization::find($role->organization_id);
            }
        }

        return null;
    }

    /**
     * Prepare cash float data for rendering or exporting.
     */
    private function prepareCashFloatData(CashFloat $cashFloat, $startDate, $endDate)
    {

        $cashFloat->load([
            'department',
            'branch',
            'user',
            'organization',
            'transactions' => function ($query) use ($startDate, $endDate) {
                $query->with([
                    'requisition.requester',
                    'items.chartOfAccount',
                    'creator'
                ]);

                // Apply date filters if provided
                if ($startDate && $endDate) {
                    $query->whereBetween('created_at', [
                        $startDate . ' 00:00:00',
                        $endDate . ' 23:59:59'
                    ]);
                }
            }
        ]);

        // Calculate running balances for each transaction
        $runningBalance = $cashFloat->initial_amount;
        $firstIssuanceId = $cashFloat->transactions->where('transaction_type', 'float_issuance')->first()?->id;

        $transactionsWithBalance = $cashFloat->transactions->map(function ($transaction) use (&$runningBalance, $firstIssuanceId) {
            $isCashIn = $transaction->transaction_type === 'float_issuance';
            $isFirstIssuance = $isCashIn && $transaction->id === $firstIssuanceId;

            $cashIn = ($isCashIn && !$isFirstIssuance) ? $transaction->total_amount : 0;
            $cashOut = !$isCashIn ? ($transaction->total_amount + ($transaction->transaction_cost ?? 0)) : 0;

            $runningBalance = $runningBalance + $cashIn - $cashOut;

            $transaction->running_balance = $runningBalance;
            $transaction->cash_in = $cashIn;
            $transaction->cash_out = $cashOut;
            $transaction->is_initial_issuance = $isFirstIssuance;

            return $transaction;
        });

        // Append calculated attributes
        $cashFloat->append(['current_balance']);

        // Get finance manager for the organization
        $financeManager = null;
        if ($cashFloat->organization) {
            $financeManager = User::whereHas('roles', function ($query) use ($cashFloat) {
                $query->where('name', 'Finance Manager')
                    ->where('organization_id', $cashFloat->organization_id);
            })->first();
        }

        // Fetch chart of accounts
        $chartOfAccounts = ChartOfAccount::where(function ($query) use ($cashFloat) {
            $query->where('organization_id', $cashFloat->organization_id);
        })
            ->where('is_active', true)
            ->where('account_type', 'expense')
            ->where('name', '!=', 'Expense')
            ->orderBy('name')
            ->get(['id', 'name', 'code']);

        return [
            'cashFloat' => $cashFloat->setRelation('transactions', $transactionsWithBalance),
            'transactions' => $transactionsWithBalance,
            'chartOfAccounts' => $chartOfAccounts,
            'financeManager' => $financeManager,
            'organization' => $cashFloat->organization,
            'filters' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ],
        ];
    }

    /**
     * Export cash float as PDF
     */
    public function exportPDF(Request $request, CashFloat $cashFloat)
    {
        // Get date filters if provided
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $filename = 'petty-cash-log-' . $cashFloat->name . '-' . now()->format('Y-m-d') . '.pdf';
        $data = $this->prepareCashFloatData($cashFloat, $startDate, $endDate);
        $pdf = Pdf::loadView('pdf.cash-float-log', $data);

        return $pdf->download($filename);
    }

    /**
     * Display the specified cash float.
     */
    public function show(Request $request, CashFloat $cashFloat)
    {
        // Get date filters from the request
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');

        // Use the prepareCashFloatData method to centralize data preparation
        $data = $this->prepareCashFloatData($cashFloat, $startDate, $endDate);

        // Pass the prepared data to the Inertia render function
        return Inertia::render('CashFloats/Show', [
            'cashFloat' => $data['cashFloat'],
            'chartOfAccounts' => $data['chartOfAccounts'],
            'financeManager' => $data['financeManager'],
            'organization' => $data['organization'],
            'filters' => $data['filters'],
        ]);
    }
}
