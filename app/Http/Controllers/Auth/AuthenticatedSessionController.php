<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Inertia\Response;

class AuthenticatedSessionController extends Controller
{
    /**
     * Show the login page.
     */
    public function create(Request $request): Response
    {
        return Inertia::render('auth/login', [
            'canResetPassword' => Route::has('password.request'),
            'status' => $request->session()->get('status'),
        ]);
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();

        $request->session()->regenerate();

        // Get the authenticated user type from the request
        $userType = $request->getAuthenticatedUserType();

        // Update last login timestamp and redirect based on user type
        if ($userType === 'supplier') {
            // Update supplier's last login timestamp
            Auth::guard('supplier')->user()->update([
                'last_login_at' => now(),
            ]);

            return redirect()->intended(route('suppliers.dashboard'));
        } else {
            // For regular users, redirect to the main dashboard
            // Note: User model may not have last_login_at field, so we'll check first
            $user = Auth::guard('web')->user();
            if ($user && in_array('last_login_at', $user->getFillable())) {
                $user->update(['last_login_at' => now()]);
            }

            return redirect()->intended(route('dashboard', absolute: false));
        }
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }
}
