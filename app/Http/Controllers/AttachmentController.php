<?php

namespace App\Http\Controllers;

use App\Models\Attachment;
use App\Models\Requisition;
use App\Models\StoreRequisition;
use App\Models\Transaction;
use Src\Attachment\Application\Services\AttachmentService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\ValidationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Inertia\Inertia;

class AttachmentController extends Controller
{
    use AuthorizesRequests;

    protected AttachmentService $attachmentService;

    public function __construct(AttachmentService $attachmentService)
    {
        $this->attachmentService = $attachmentService;
    }

    /**
     * Handles file uploads for various models.
     */
    public function uploadToAttachable(Request $request, string $attachableType, int $attachableId): JsonResponse|RedirectResponse
    {
        $modelClass = $this->getAttachableModelClass($attachableType);
        $model = $modelClass::findOrFail($attachableId);

        // Check if user can attach files to this model
        $this->authorize('attachFiles', $model);

        $request->validate([
            'files' => 'required|array|max:5',
            'files.*' => 'required|file|max:10240', // 10MB max per file
            'descriptions' => 'nullable|array',
            'descriptions.*' => 'nullable|string|max:500',
            'uploaded_at_step' => 'nullable|string|max:100',
        ]);

        try {
            $files = $request->file('files');
            $descriptions = $request->input('descriptions', []);
            $uploadedAtStep = $request->input('uploaded_at_step', $model->status);

            // Validate each file
            foreach ($files as $file) {
                $errors = $this->attachmentService->validateFile($file);
                if (!empty($errors)) {
                    throw ValidationException::withMessages(['files' => $errors]);
                }
            }

            // Upload files
            $attachments = $this->attachmentService->uploadMultipleFiles(
                $files,
                $model,
                $descriptions,
                true, // is_evidence
                $uploadedAtStep
            );

            // Load relationships for response
            foreach ($attachments as $attachment) {
                $attachment->load('uploader');
            }

            // Check if this is an Inertia request
            if ($request->header('X-Inertia')) {
                return redirect()->back()->with('success', 'Files uploaded successfully');
            }

            return response()->json([
                'message' => 'Files uploaded successfully',
                'attachments' => $attachments,
            ], 201);

        } catch (\Exception $e) {
            // Check if this is an Inertia request
            if ($request->header('X-Inertia')) {
                return redirect()->back()->withErrors(['upload' => 'Failed to upload files: ' . $e->getMessage()]);
            }

            return response()->json([
                'message' => 'Failed to upload files',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Download an attachment.
     */
    public function download(int $attachmentId): Response
    {
        $attachment = Attachment::findOrFail($attachmentId);

        // Check if user can download this attachment
        $this->authorize('download', $attachment);

        $fileContent = $this->attachmentService->getFileContent($attachment->id);

        if (!$fileContent) {
            abort(404, 'File not found');
        }

        return response($fileContent)
            ->header('Content-Type', $attachment->mime_type)
            ->header('Content-Disposition', 'attachment; filename="' . $attachment->original_name . '"')
            ->header('Content-Length', $attachment->file_size);
    }

    /**
     * View an attachment inline.
     */
    public function view(int $attachmentId): Response
    {
        $attachment = Attachment::findOrFail($attachmentId);
        
        // Authorization
        $this->authorize('download', $attachment);

        $fileContent = $this->attachmentService->getFileContent($attachment->id);

        if (!$fileContent) {
            abort(404, 'File not found');
        }

        $response = response($fileContent)
            ->header('Content-Type', $attachment->mime_type)
            ->header('Content-Length', $attachment->file_size)
            ->header('X-Frame-Options', 'SAMEORIGIN')
            ->header('Cache-Control', 'private, max-age=3600')
            ->header('X-Content-Type-Options', 'nosniff');

        // For PDFs, ensure inline viewing without filename to prevent download
        if ($attachment->mime_type === 'application/pdf') {
            $response->header('Content-Disposition', 'inline');
        } else {
            $response->header('Content-Disposition', 'inline; filename="' . $attachment->original_name . '"');
        }

        return $response;
    }


    /**
     * Delete an attachment.
     */
    public function destroy(int $attachmentId): JsonResponse|RedirectResponse
    {
        $attachment = Attachment::findOrFail($attachmentId);

        // Check if user can delete this attachment
        $this->authorize('delete', $attachment);

        try {
            $deleted = $this->attachmentService->deleteAttachment($attachment->id);

            if ($deleted) {
                // Check if this is an Inertia request
                if (request()->header('X-Inertia')) {
                    return redirect()->back()->with('success', 'Attachment deleted successfully');
                }

                return response()->json([
                    'message' => 'Attachment deleted successfully',
                ]);
            } else {
                // Check if this is an Inertia request
                if (request()->header('X-Inertia')) {
                    return redirect()->back()->withErrors(['delete' => 'Failed to delete attachment']);
                }

                return response()->json([
                    'message' => 'Failed to delete attachment',
                ], 500);
            }
        } catch (\Exception $e) {
            // Check if this is an Inertia request
            if (request()->header('X-Inertia')) {
                return redirect()->back()->withErrors(['delete' => 'Failed to delete attachment: ' . $e->getMessage()]);
            }

            return response()->json([
                'message' => 'Failed to delete attachment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get attachments for an attachable model.
     */
    public function getAttachmentsForAttachable(string $attachableType, int $attachableId): JsonResponse
    {
        $modelClass = $this->getAttachableModelClass($attachableType);
        $model = $modelClass::findOrFail($attachableId);

        // Check if user can view this model
        $this->authorize('view', $model);

        $attachments = $model->attachments()->with('uploader')->get();

        return response()->json([
            'attachments' => $attachments,
        ]);
    }

    /**
     * Get the model class for a given attachable type.
     */
    private function getAttachableModelClass(string $attachableType): string
    {
        $map = [
            'requisitions' => Requisition::class,
            'store-requisitions' => StoreRequisition::class,
            'transactions' => Transaction::class,
        ];

        if (!isset($map[$attachableType])) {
            abort(404, 'Invalid attachable type');
        }

        return $map[$attachableType];
    }
}
