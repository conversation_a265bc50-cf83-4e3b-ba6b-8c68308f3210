<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Role;
use App\Models\Department;
use App\Models\Organization;
use App\Models\Branch;
use App\Models\Requisition;
use App\Models\CashFloat;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class FlexibleDashboardController extends Controller
{
    /**
     * Display a flexible dashboard for custom roles based on permissions.
     */
    public function index(Request $request, array $context, array $availableContexts = []): Response
    {
        $user = $request->user();
        $role = Role::find($context['role_id']);

        if (!$role) {
            abort(404, 'Role not found');
        }

        // Get user permissions for this role
        $permissions = $role->permissions->pluck('name')->toArray();

        // Get organization and department context
        $organization = $context['organization'];
        $department = $context['department'];

        // Build dashboard data based on permissions
        $dashboardData = $this->buildDashboardData($user, $role, $permissions, $organization, $department);

        // Get available cash floats for export functionality
        $cashFloats = $this->getUserAvailableCashFloats($user, $organization);

        return Inertia::render('Dashboard/FlexibleDashboard', [
            'role' => [
                'id' => $role->id,
                'name' => $role->name,
                'description' => $role->description,
            ],
            'permissions' => $permissions,
            'organization' => $organization,
            'department' => $department,
            'availableContexts' => $availableContexts,
            'dashboardData' => $dashboardData,
            'cashFloats' => $cashFloats,
            'user' => [
                'id' => $user->id,
                'name' => $user->name ?? $user->first_name . ' ' . $user->last_name,
                'email' => $user->email,
                'permissions' => $user->getPermissionNames()->toArray(),
            ],
        ]);
    }

    /**
     * Safely get organization ID from user's roles.
     */
    private function safeGetOrganizationId(User $user): ?int
    {
        $orgAdminRole = $user->roles()
            ->where('name', 'Organization Admin')
            ->first();

        return $orgAdminRole ? $orgAdminRole->organization_id : null;
    }

    /**
     * Check if user has financial access permissions.
     */
    private function hasFinancialAccess(array $permissions, User $user): bool
    {
        // First check hardcoded roles for backward compatibility
        if ($user->hasRole('Finance Manager') ||
            $user->hasRole('Organization Admin') ||
            $user->hasRole('Cashier') ||
            $user->is_platform_admin) {
            return true;
        }

        // Check for direct financial permissions - this is the primary method for custom roles
        $directFinancialPermissions = [
            'manage-finances',
            'view-finances',
            'manage-cash-floats',
            'view-cash-floats',
            'manage-floats',
            'view-float',
            'create-float',
            'create-cash-float',
            'manage-transactions',
            'view-transactions',
            'create-transaction',
            'view-reports',
            'export-reports',
            'view-reconciliation',
            'manage-reconciliations',
            'view-audit-logs'
        ];

        foreach ($directFinancialPermissions as $permission) {
            if (in_array($permission, $permissions)) {
                return true;
            }
        }

        // Check for management/oversight permissions that typically include financial access
        $managementPermissions = [
            'manage-users',
            'manage-departments',
            'manage-roles',
            'manage-branches',
            'approver',
            'approve-requisition',
            'reject-requisition',
            'manage-requisitions'
        ];

        $managementPermissionCount = 0;
        foreach ($managementPermissions as $permission) {
            if (in_array($permission, $permissions)) {
                $managementPermissionCount++;
            }
        }

        // If user has multiple management permissions, they likely need financial oversight
        if ($managementPermissionCount >= 3) { // Reduced from 4 to 3 for more flexibility
            return true;
        }

        // If user has a comprehensive set of permissions, they likely need financial access
        if (count($permissions) >= 10) {
            return true;
        }

        $generalAccessPermissions = [
            'view-dashboard',
            'access-dashboard',
            'view-all',
            'manage-all'
        ];

        foreach ($generalAccessPermissions as $permission) {
            if (in_array($permission, $permissions)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user can manage cash floats (create/edit/delete).
     */
    private function canManageCashFloats(array $permissions, User $user): bool
    {
        // First check hardcoded roles for backward compatibility
        if ($user->hasRole('Finance Manager') ||
            $user->hasRole('Organization Admin') ||
            $user->hasRole('Cashier') ||
            $user->is_platform_admin) {
            return true;
        }

        // Check for direct cash float management permissions (for custom roles)
        $cashFloatManagementPermissions = [
            'manage-cash-floats',
            'create-float',
            'edit-float',
            'delete-float',
            'manage-floats',
            'create-cash-float'
        ];

        foreach ($cashFloatManagementPermissions as $permission) {
            if (in_array($permission, $permissions)) {
                return true;
            }
        }

        // Check for comprehensive financial management permissions
        $comprehensiveFinancialPermissions = [
            'manage-finances',
            'manage-transactions',
            'manage-reconciliations',
            'manage-reports'
        ];

        $financialPermissionCount = 0;
        foreach ($comprehensiveFinancialPermissions as $permission) {
            if (in_array($permission, $permissions)) {
                $financialPermissionCount++;
            }
        }

        if ($financialPermissionCount >= 2) {
            return true;
        }

        // Check for general management permissions that might include cash float management
        $generalManagementPermissions = [
            'manage-all',
            'manage-departments'
        ];

        foreach ($generalManagementPermissions as $permission) {
            if (in_array($permission, $permissions)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Build dashboard data based on user permissions and context.
     */
    private function buildDashboardData(User $user, Role $role, array $permissions, $organization, $department): array
    {
        $data = [
            'stats' => [],
            'actions' => [],
            'recentItems' => [],
            'widgets' => [],
            'pendingTransactions' => [],
            'recentTransactions' => [],
            'floatStatus' => [],
            'pendingApprovals' => [],
            'canManageCashFloats' => $this->canManageCashFloats($permissions, $user),
            'layout' => [
                'statCardsLayout' => 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8',
                'actionsLayout' => 'flex flex-wrap gap-2 mb-8'
            ]
        ];

        // Build stats based on permissions
        $data['stats'] = $this->buildStatsData($user, $permissions, $organization, $department);

        // Build available actions based on permissions
        $data['actions'] = $this->buildActionsData($user, $role, $permissions, $organization, $department);

        // Build recent items based on permissions
        $data['recentItems'] = $this->buildRecentItemsData($user, $permissions, $organization, $department);

        // Build widgets based on permissions
        $data['widgets'] = $this->buildWidgetsData($user, $permissions, $organization, $department);

        // Build financial data for manager dashboard
        $data['pendingTransactions'] = $this->buildPendingTransactionsData($permissions, $organization, $department);
        $data['recentTransactions'] = $this->buildRecentTransactionsData($permissions, $organization, $department);

        // Build float status data and extract the floats array for frontend compatibility
        $floatStatusData = $this->buildFloatStatusData($permissions, $organization, $department);
        $data['floatStatus'] = $floatStatusData['floats'] ?? [];
        $data['floatStatusActions'] = $floatStatusData['actions'] ?? [];

        $data['pendingApprovals'] = $this->buildPendingApprovalsData($user, $permissions, $organization, $department);
        $data['cashFloatManagement'] = $this->buildCashFloatManagementData($permissions, $organization, $department);

        return $data;
    }

    /**
     * Build statistics data based on permissions.
     */
    private function buildStatsData(User $user, array $permissions, $organization, $department): array
    {
        $stats = [];

        // Requisition-related stats
        if (in_array('view-requisition', $permissions) || in_array('view-requisitions', $permissions) ||
            in_array('manage-requisitions', $permissions) || in_array('create-requisition', $permissions) ||
            in_array('approver', $permissions)) {
            $query = Requisition::query();

            if ($organization) {
                $query->where('organization_id', $organization->id);
            }

            if ($department) {
                $query->where('department_id', $department->id);
            }

            // Get pending approvals count
            $pendingApprovalsCount = (clone $query)->where('status', 'pending_approval')->count();

            $stats['requisitions'] = [
                'total' => $query->count(),
                'pending' => (clone $query)->where('status', 'pending')->count(),
                'approved' => (clone $query)->where('status', 'approved')->count(),
                'rejected' => (clone $query)->where('status', 'rejected')->count(),
                'pendingApprovals' => $pendingApprovalsCount,
            ];
        }

        // User management stats
        if (in_array('manage-users', $permissions) || in_array('view-users', $permissions)) {
            $userQuery = User::query();

            if ($organization) {
                $userQuery->whereHas('roles', function ($query) use ($organization) {
                    $query->where('organization_id', $organization->id);
                });
            }

            $stats['users'] = [
                'total' => $userQuery->count(),
                'active' => (clone $userQuery)->where('status', 'active')->count(),
            ];
        }

        // Department stats
        if (in_array('manage-departments', $permissions) || in_array('view-departments', $permissions)) {
            $deptQuery = Department::query();

            if ($organization) {
                $deptQuery->where('organization_id', $organization->id);
            }

            $stats['departments'] = [
                'total' => $deptQuery->count(),
            ];
        }

        // Branch stats
        if (in_array('manage-branches', $permissions) || in_array('view-branches', $permissions)) {
            $branchQuery = Branch::query();

            if ($organization) {
                $branchQuery->where('organization_id', $organization->id);
            }

            $stats['branches'] = [
                'total' => $branchQuery->count(),
            ];
        }

        // Role stats
        if (in_array('manage-roles', $permissions) || in_array('view-roles', $permissions)) {
            $roleQuery = Role::query();

            if ($organization) {
                $roleQuery->where('organization_id', $organization->id);
            }

            $stats['roles'] = [
                'total' => $roleQuery->count(),
            ];
        }

        // Transaction stats
        if (in_array('manage-transactions', $permissions) || in_array('view-transactions', $permissions)) {
            $transactionQuery = Transaction::query();

            if ($organization) {
                $transactionQuery->where(function ($query) use ($organization) {
                    $query->whereHas('cashFloat', function ($subQuery) use ($organization) {
                        $subQuery->where('organization_id', $organization->id);
                    })
                    ->orWhereHas('requisition', function ($subQuery) use ($organization) {
                        $subQuery->where('organization_id', $organization->id);
                    });
                });
            }

            $stats['transactions'] = [
                'total' => $transactionQuery->count(),
                'pending' => (clone $transactionQuery)->whereIn('status', ['opened', 'updated'])->count(),
                'completed' => (clone $transactionQuery)->where('status', 'completed')->count(),
            ];
        }

        // Financial stats
        $hasFinancialAccess = $this->hasFinancialAccess($permissions, $user);

        if ($hasFinancialAccess) {
            if ($organization) {
                try {
                    // Get cash floats using the same approach as Finance Manager dashboard
                    // Load transactions relationship for current_balance calculation
                    $cashFloats = CashFloat::where('organization_id', $organization->id)
                        ->where('status', 'active')
                        ->with(['transactions', 'department', 'branch', 'user'])
                        ->get();

                    // Calculate detailed float statistics using the model's current_balance attribute
                    $totalFloat = $cashFloats->sum('initial_amount');
                    $totalRemaining = $cashFloats->sum('current_balance');

                    // Get low float alerts using current_balance computed attribute
                    $lowFloatDepartments = $cashFloats->filter(function ($float) {
                        return $float->alert_threshold && $float->current_balance <= $float->alert_threshold;
                    });

                    $stats['finances'] = [
                        'totalFloats' => $cashFloats->count(),
                        'activeFloats' => $cashFloats->count(),
                        'totalFloat' => $totalFloat,
                        'totalRemaining' => $totalRemaining,
                        'lowFloatDepartments' => $lowFloatDepartments->count(),
                    ];

                } catch (\Exception $e) {
                    // Fallback stats on error
                    $stats['finances'] = [
                        'totalFloats' => 0,
                        'activeFloats' => 0,
                        'totalFloat' => 0,
                        'totalRemaining' => 0,
                        'lowFloatDepartments' => 0,
                    ];
                }
            } else {
                // Fallback if no organization
                $stats['finances'] = [
                    'totalFloats' => 0,
                    'activeFloats' => 0,
                    'totalFloat' => 0,
                    'totalRemaining' => 0,
                    'lowFloatDepartments' => 0,
                ];
            }
        }

        return $stats;
    }

    /**
     * Build available actions based on permissions.
     * This method now provides comprehensive actions for both predefined and custom roles.
     */
    private function buildActionsData($user, $role, $permissions, $organization, $department): array
    {
        $actions = [];
        $addedActions = []; // Track added actions to prevent duplicates
        $user = auth()->user();

        // Helper function to add action only if not already added
        $addAction = function($key, $action) use (&$actions, &$addedActions) {
            if (!in_array($key, $addedActions)) {
                $actions[] = $action;
                $addedActions[] = $key;
            }
        };

        // Requisition actions
        if (in_array('create-requisition', $permissions) || in_array('create-requisitions', $permissions)) {
            $addAction('create-requisition', [
                'label' => 'Create Requisition',
                'href' => '/requisitions/create',
                'icon' => 'PlusCircle',
                'variant' => 'default'
            ]);
        }

        if (in_array('manage-requisitions', $permissions)) {
            $addAction('manage-requisitions', [
                'label' => 'Manage Requisitions',
                'href' => '/requisitions',
                'icon' => 'ClipboardList',
                'variant' => 'outline'
            ]);
        }

        // User management actions
        if (in_array('manage-users', $permissions) || in_array('view-users', $permissions)) {
            $addAction('manage-users', [
                'label' => 'Manage Users',
                'href' => '/users',
                'icon' => 'Users',
                'variant' => 'outline'
            ]);
        }

        // Department actions
        if (in_array('manage-departments', $permissions) || in_array('view-departments', $permissions)) {
            $addAction('manage-departments', [
                'label' => 'Manage Departments',
                'href' => '/departments',
                'icon' => 'Building',
                'variant' => 'outline'
            ]);
        }

        // Role management actions
        if (in_array('manage-roles', $permissions) || in_array('view-roles', $permissions)) {
            $addAction('manage-roles', [
                'label' => 'Manage Roles',
                'href' => '/roles',
                'icon' => 'UserCog',
                'variant' => 'outline'
            ]);
        }

        // Branch management actions - expanded permissions check
        if (in_array('manage-branches', $permissions) ||
            in_array('view-branches', $permissions) ||
            in_array('create-branch', $permissions) ||
            in_array('edit-branch', $permissions) ||
            $user->hasRole('Organization Admin') ||
            $user->hasRole('Platform Admin')) {

            $addAction('manage-branches', [
                'label' => 'Manage Branches',
                'href' => '/branches',
                'icon' => 'GitBranch',
                'variant' => 'outline'
            ]);
        }

        // Remove cash float management actions from here as they will be in the dedicated cash float management card
        // Transaction management actions
        if (in_array('manage-transactions', $permissions) || in_array('view-transactions', $permissions)) {
            $addAction('manage-transactions', [
                'label' => 'Manage Transactions',
                'href' => '/transactions',
                'icon' => 'Receipt',
                'variant' => 'outline'
            ]);
        }

        // Chart of accounts actions
        if (in_array('manage-chart-of-accounts', $permissions) || in_array('view-chart-of-accounts', $permissions)) {
            $addAction('chart-of-accounts', [
                'label' => 'Chart of Accounts',
                'href' => $organization ? "/chart-of-accounts?organization_id={$organization->id}" : '/chart-of-accounts',
                'icon' => 'BookOpen',
                'variant' => 'outline'
            ]);
        }

        // // Reconciliation actions
        // if (in_array('manage-reconciliations', $permissions) || in_array('view-reconciliation', $permissions)) {
        //     $addAction('reconciliations', [
        //         'label' => 'Reconciliations',
        //         'href' => '/reconciliations',
        //         'icon' => 'ListOrdered',
        //         'variant' => 'outline'
        //     ]);
        // }


        // Approval workflow actions
        if (in_array('manage-roles', $permissions) || in_array('manage-departments', $permissions)) {
            $addAction('approval-workflows', [
                'label' => 'Approval Workflows',
                'href' => $organization ? "/approval-workflows?organization_id={$organization->id}" : '/approval-workflows',
                'icon' => 'ListOrdered',
                'variant' => 'outline'
            ]);
        }

        // Approver actions
        if (in_array('approver', $permissions)) {
            $addAction('pending-approvals', [
                'label' => 'Pending Approvals',
                'href' => '/requisitions/my-approvals',
                'icon' => 'CheckCircle',
                'variant' => 'secondary'
            ]);
        }

        // Report and export actions
        if ((in_array('export-reports', $permissions) ||
             in_array('view-reports', $permissions) ||
             in_array('manage-reports', $permissions)) && $organization) {
            $hasCashFloats = CashFloat::where('organization_id', $organization->id)
                ->where('status', 'active')
                ->exists();

            if ($hasCashFloats) {
                $addAction('export-report', [
                    'label' => 'Export Report',
                    'href' => '#',
                    'icon' => 'FileSpreadsheet',
                    'variant' => 'outline',
                    'type' => 'export'
                ]);
            }
        }

        return $actions;
    }

    /**
     * Build recent items data based on permissions.
     * This method now provides comprehensive recent items for both predefined and custom roles.
     */
    private function buildRecentItemsData(User $user, array $permissions, $organization, $department): array
    {
        $recentItems = [];

        // Recent requisitions
        if (in_array('view-requisition', $permissions) || in_array('view-requisitions', $permissions) ||
            in_array('manage-requisitions', $permissions) || in_array('create-requisition', $permissions)) {
            $query = Requisition::with(['requester', 'department']);

            if ($organization) {
                $query->where('organization_id', $organization->id);
            }

            if ($department) {
                $query->where('department_id', $department->id);
            }

            $recentItems['requisitions'] = $query->latest()->take(5)->get();
        }

        // Recent users
        if (in_array('view-users', $permissions) || in_array('manage-users', $permissions)) {
            $userQuery = User::query();

            if ($organization) {
                $userQuery->whereHas('roles', function ($query) use ($organization) {
                    $query->where('organization_id', $organization->id);
                });
            }

            $recentItems['users'] = $userQuery->latest()->take(5)->get();
        }

        // Recent transactions
        if (in_array('view-transactions', $permissions) || in_array('manage-transactions', $permissions)) {
            $transactionQuery = Transaction::with(['creator', 'cashFloat', 'requisition']);

            if ($organization) {
                $transactionQuery->where(function ($query) use ($organization) {
                    $query->whereHas('cashFloat', function ($subQuery) use ($organization) {
                        $subQuery->where('organization_id', $organization->id);
                    })
                    ->orWhereHas('requisition', function ($subQuery) use ($organization) {
                        $subQuery->where('organization_id', $organization->id);
                    });
                });
            }

            $recentItems['transactions'] = $transactionQuery->latest()->take(5)->get();
        }

        // Recent departments
        if (in_array('view-departments', $permissions) || in_array('manage-departments', $permissions)) {
            $deptQuery = Department::with(['organization', 'branch']);

            if ($organization) {
                $deptQuery->where('organization_id', $organization->id);
            }

            $recentItems['departments'] = $deptQuery->latest()->take(5)->get();
        }

        // Recent cash floats
        if (in_array('view-float', $permissions) || in_array('view-cash-floats', $permissions) ||
            in_array('manage-floats', $permissions) || in_array('manage-cash-floats', $permissions)) {
            $floatQuery = CashFloat::with(['department', 'branch', 'user']);

            if ($organization) {
                $floatQuery->where('organization_id', $organization->id);
            }

            $recentItems['cashFloats'] = $floatQuery->latest()->take(5)->get();
        }

        // Recent roles (for role managers)
        if (in_array('view-roles', $permissions) || in_array('manage-roles', $permissions)) {
            $roleQuery = Role::query();

            if ($organization) {
                $roleQuery->where('organization_id', $organization->id);
            }

            $recentItems['roles'] = $roleQuery->latest()->take(5)->get();
        }

        return $recentItems;
    }

    /**
     * Build widgets data based on permissions.
     */
    private function buildWidgetsData(User $user, array $permissions, $organization, $department): array
    {
        $widgets = [];

        // My requisitions widget
        if (in_array('create-requisition', $permissions) || in_array('create-requisitions', $permissions)) {
            $myRequisitions = Requisition::where('requester_user_id', $user->id);

            if ($department) {
                $myRequisitions->where('department_id', $department->id);
            }

            $widgets['myRequisitions'] = [
                'title' => 'My Requisitions',
                'count' => $myRequisitions->count(),
                'href' => '/requisitions?filter=my',
                'icon' => 'FileText',
                'action' => [
                    'label' => 'Create New',
                    'href' => '/requisitions/create',
                    'icon' => 'PlusCircle'
                ]
            ];
        }

        // My approvals widget
        if (in_array('approver', $permissions)) {
            $pendingApprovals = Requisition::where('status', 'pending_approval');

            if ($organization) {
                $pendingApprovals->where('organization_id', $organization->id);
            }

            if ($department) {
                $pendingApprovals->where('department_id', $department->id);
            }

            $widgets['myApprovals'] = [
                'title' => 'Pending Approvals',
                'count' => $pendingApprovals->count(),
                'href' => '/requisitions/my-approvals',
                'icon' => 'CheckCircle',
                'action' => [
                    'label' => 'View All',
                    'href' => '/requisitions/my-approvals',
                    'icon' => 'ArrowRight'
                ]
            ];
        }

        // Cash floats widget
        if (in_array('view-float', $permissions) || in_array('view-cash-floats', $permissions) ||
            in_array('manage-floats', $permissions) || in_array('manage-cash-floats', $permissions) ||
            in_array('create-float', $permissions) || in_array('create-cash-float', $permissions)) {

            $floatQuery = CashFloat::where('status', 'active');

            if ($organization) {
                $floatQuery->where('organization_id', $organization->id);
            }

            $widgets['cashFloats'] = [
                'title' => 'Active Cash Floats',
                'count' => $floatQuery->count(),
                'href' => '/cash-floats',
                'icon' => 'Wallet',
                'action' => [
                    'label' => 'Manage Floats',
                    'href' => '/cash-floats',
                    'icon' => 'ArrowRight'
                ],
                'createAction' => [
                    'label' => 'Create New Cash Float',
                    'href' => '/cash-floats/create',
                    'icon' => 'PlusCircle'
                ]
            ];
        }

        return $widgets;
    }

    /**
     * Build pending transactions data for manager dashboard.
     */
    private function buildPendingTransactionsData(array $permissions, $organization, $department): array
    {
        $hasFinancialAccess = $this->hasFinancialAccessByPermissions($permissions);

        if (!$hasFinancialAccess) {
            return [];
        }

        if (!$organization) {
            return [];
        }

        $pendingTransactions = Transaction::with([
                'cashFloat.organization',
                'cashFloat.department',
                'cashFloat.branch',
                'cashFloat.user',
                'creator',
                'requisition.requester',
                'requisition.department'
            ])
            ->where(function ($query) use ($organization) {
                // Transactions with cash floats in this organization
                $query->whereHas('cashFloat', function ($subQuery) use ($organization) {
                    $subQuery->where('organization_id', $organization->id);
                })
                // OR transactions with requisitions from this organization
                ->orWhereHas('requisition', function ($subQuery) use ($organization) {
                    $subQuery->where('organization_id', $organization->id);
                });
            })
            ->whereIn('status', ['opened', 'updated']) // Pending processing
            ->latest()
            ->take(5)
            ->get();

        return $pendingTransactions->toArray();
    }

    /**
     * Build recent transactions data for manager dashboard.
     */
    private function buildRecentTransactionsData(array $permissions, $organization, $department): array
    {
        $hasFinancialAccess = $this->hasFinancialAccessByPermissions($permissions);

        if (!$hasFinancialAccess) {
            return [];
        }

        if (!$organization) {
            return [];
        }

        // Use the same approach as Finance Manager dashboard
        $recentTransactions = Transaction::with([
                'cashFloat.organization',
                'cashFloat.department',
                'cashFloat.branch',
                'cashFloat.user',
                'creator',
                'requisition.requester',
                'requisition.department'
            ])
            ->where(function ($query) use ($organization) {
                // Transactions with cash floats in this organization
                $query->whereHas('cashFloat', function ($subQuery) use ($organization) {
                    $subQuery->where('organization_id', $organization->id);
                })
                // OR transactions with requisitions from this organization
                ->orWhereHas('requisition', function ($subQuery) use ($organization) {
                    $subQuery->where('organization_id', $organization->id);
                });
            })
            ->latest()
            ->take(5)
            ->get();

        return $recentTransactions->toArray();
    }

    /**
     * Build float status data for manager dashboard.
     */
    private function buildFloatStatusData(array $permissions, $organization, $department): array
    {
        // Check for financial access based on permissions - be more inclusive with permissions
        $user = auth()->user();
        $hasFinancialAccess = $this->hasFinancialAccess($permissions, $user) ||
                             $this->hasFinancialAccessByPermissions($permissions) ||
                             in_array('view-float', $permissions) ||
                             in_array('view-cash-floats', $permissions) ||
                             in_array('manage-floats', $permissions) ||
                             in_array('manage-cash-floats', $permissions) ||
                             in_array('create-float', $permissions) ||
                             in_array('create-cash-float', $permissions);

        \Log::info('FlexibleDashboard: buildFloatStatusData permission check', [
            'user_id' => $user->id,
            'permissions' => $permissions,
            'hasFinancialAccess' => $hasFinancialAccess,
            'organization_id' => $organization ? $organization->id : null
        ]);

        if (!$hasFinancialAccess) {
            \Log::info('FlexibleDashboard: No financial access for buildFloatStatusData', [
                'user_id' => $user->id,
                'permissions' => $permissions,
                'hasFinancialAccess' => $hasFinancialAccess
            ]);
            return [];
        }

        if (!$organization) {
            \Log::info('FlexibleDashboard: No organization for buildFloatStatusData');
            return [];
        }

        $result = [
            'floats' => [],
            'actions' => []
        ];

        // Add Manage Floats button for Float Status card
        if (in_array('view-float', $permissions) ||
            in_array('view-cash-floats', $permissions) ||
            in_array('manage-floats', $permissions) ||
            in_array('manage-cash-floats', $permissions)) {
            $result['actions'][] = [
                'label' => 'Manage Floats',
                'href' => '/cash-floats',
                'icon' => 'ArrowRight'
            ];
        }

        try {
            // Load transactions relationship for current_balance calculation
            $cashFloats = CashFloat::where('organization_id', $organization->id)
                ->where('status', 'active')
                ->with(['transactions', 'department', 'branch', 'user'])
                ->get();

            \Log::info('FlexibleDashboard: Found cash floats', [
                'organization_id' => $organization->id,
                'cash_floats_count' => $cashFloats->count(),
                'cash_float_ids' => $cashFloats->pluck('id')->toArray()
            ]);

            // Get float status for each department with corrected calculations
            $result['floats'] = $cashFloats->map(function ($float) {
                try {
                    $remaining = $float->current_balance; // Use computed attribute
                    $allocated = $float->initial_amount;
                    $used = max(0, $allocated - $remaining); // Ensure used is not negative
                    $remainingPercentage = $allocated > 0 ? ($remaining / $allocated) * 100 : 0;

                    // Check if low based on alert threshold
                    $isLow = ($float->alert_threshold && $remaining <= $float->alert_threshold) ||
                             ($remainingPercentage < 20);

                    return [
                        'id' => $float->id,
                        'department' => $float->department ? $float->department->name :
                                      ($float->branch ? $float->branch->name :
                                      ($float->user ? $float->user->name ?? $float->user->first_name . ' ' . $float->user->last_name : 'Unassigned')),
                        'allocated' => $allocated,
                        'used' => $used,
                        'remaining' => $remaining,
                        'alert_threshold' => $float->alert_threshold ?? ($allocated * 0.2), // Use actual threshold or 20% fallback
                        'is_low' => $isLow,
                        'utilization_percentage' => $allocated > 0 ? round(($used / $allocated) * 100, 1) : 0,
                        'remaining_percentage_safe' => max(0, min(100, $remainingPercentage)),
                        'last_transaction_at' => $float->transactions()->latest()->first()?->created_at?->toISOString(),
                        'monthly_transactions' => $float->transactions()
                            ->whereMonth('created_at', now()->month)
                            ->count(),
                        'monthly_total' => $float->transactions()
                            ->whereMonth('created_at', now()->month)
                            ->sum('total_amount'),
                    ];
                } catch (\Exception $e) {
                    \Log::error('FlexibleDashboard: Error processing float', [
                        'float_id' => $float->id,
                        'error' => $e->getMessage()
                    ]);
                    return [
                        'id' => $float->id,
                        'department' => 'Error loading data',
                        'allocated' => 0,
                        'used' => 0,
                        'remaining' => 0,
                        'alert_threshold' => 0,
                        'is_low' => false,
                        'utilization_percentage' => 0,
                        'remaining_percentage_safe' => 0,
                        'last_transaction_at' => null,
                        'monthly_transactions' => 0,
                        'monthly_total' => 0,
                    ];
                }
            })->toArray();

            \Log::info('FlexibleDashboard: buildFloatStatusData result', [
                'user_id' => $user->id,
                'organization_id' => $organization->id,
                'cash_floats_count' => $cashFloats->count(),
                'result_floats_count' => count($result['floats']),
                'result_sample' => array_slice($result['floats'], 0, 2) // Log first 2 floats for debugging
            ]);

            return $result;
        } catch (\Exception $e) {
            \Log::error('FlexibleDashboard: Error in buildFloatStatusData', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $result;
        }
    }

    /**
     * Build pending approvals data based on permissions.
     */
    private function buildPendingApprovalsData(User $user, array $permissions, $organization, $department): array
    {
        // Check if user has any approval-related permissions
        if (!in_array('approver', $permissions) &&
            !in_array('manage-requisitions', $permissions) &&
            !in_array('approve-requisition', $permissions) &&
            !in_array('reject-requisition', $permissions) &&
            !in_array('review-requisition', $permissions)) {
            return [];
        }

        $query = Requisition::with(['requester', 'department'])
            ->where('status', 'pending_approval');

        if ($organization) {
            $query->where('organization_id', $organization->id);
        }

        if ($department) {
            $query->where('department_id', $department->id);
        }

        return $query->latest()
            ->take(10)
            ->get()
            ->map(function ($requisition) {
                return [
                    'id' => $requisition->id,
                    'purpose' => $requisition->purpose,
                    'total_amount' => $requisition->total_amount,
                    'status' => $requisition->status,
                    'created_at' => $requisition->created_at->toISOString(),
                    'requester' => $requisition->requester ? [
                        'first_name' => $requisition->requester->first_name,
                        'last_name' => $requisition->requester->last_name,
                    ] : null,
                    'department' => $requisition->department ? [
                        'id' => $requisition->department->id,
                        'name' => $requisition->department->name,
                    ] : null,
                ];
            })
            ->toArray();
    }

    /**
     * Get user's available cash floats for export functionality.
     */
    private function getUserAvailableCashFloats(User $user, $organization)
    {
        if (!$organization) {
            return [];
        }

        return CashFloat::where('organization_id', $organization->id)
            ->where('status', 'active')
            ->with(['department', 'branch'])
            ->get()
            ->map(function ($float) {
                return [
                    'id' => $float->id,
                    'name' => $float->name,
                    'department' => $float->department ? $float->department->name : null,
                    'branch' => $float->branch ? $float->branch->name : null,
                    'initial_amount' => $float->initial_amount,
                    'current_balance' => $float->current_balance,
                ];
            });
    }

    /**
     * Helper method to check financial access by permissions only
     */
    private function hasFinancialAccessByPermissions(array $permissions): bool
    {
        // Direct financial permissions
        $directFinancialPermissions = [
            'manage-finances',
            'view-finances',
            'manage-cash-floats',
            'view-cash-floats',
            'manage-floats',
            'view-float',
            'create-float',
            'create-cash-float',
            'manage-transactions',
            'view-transactions',
            'create-transaction',
            'view-reports',
            'export-reports',
            'view-reconciliation',
            'manage-reconciliations',
            'view-audit-logs'
        ];

        // Check for any financial permission
        foreach ($directFinancialPermissions as $permission) {
            if (in_array($permission, $permissions)) {
                return true;
            }
        }

        // Check for role-based permissions that might imply financial access (backward compatibility)
        $financialRolePermissions = [
            'finance-manager',
            'cashier',
            'approver',
            'organization-admin'
        ];

        foreach ($financialRolePermissions as $rolePermission) {
            if (in_array($rolePermission, $permissions)) {
                return true;
            }
        }

        // Check for admin permissions that might imply financial access
        $adminPermissions = [
            'admin',
            'platform-admin',
            'manage-all',
            'view-all'
        ];

        foreach ($adminPermissions as $adminPermission) {
            if (in_array($adminPermission, $permissions)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Build cash float management data for manager dashboard.
     */
    private function buildCashFloatManagementData(array $permissions, $organization, $department): array
    {
        // Check for financial access based on permissions
        $user = auth()->user();
        $hasFinancialAccess = $this->hasFinancialAccess($permissions, $user) ||
                             $this->hasFinancialAccessByPermissions($permissions) ||
                             in_array('view-float', $permissions) ||
                             in_array('view-cash-floats', $permissions) ||
                             in_array('manage-floats', $permissions) ||
                             in_array('manage-cash-floats', $permissions) ||
                             in_array('create-float', $permissions) ||
                             in_array('create-cash-float', $permissions);

        \Log::info('FlexibleDashboard: buildCashFloatManagementData permission check', [
            'user_id' => $user->id,
            'permissions' => $permissions,
            'hasFinancialAccess' => $hasFinancialAccess,
            'organization_id' => $organization ? $organization->id : null
        ]);

        if (!$hasFinancialAccess) {
            \Log::info('FlexibleDashboard: No financial access for buildCashFloatManagementData', [
                'user_id' => $user->id,
                'permissions' => $permissions,
                'hasFinancialAccess' => $hasFinancialAccess
            ]);
            return [];
        }

        if (!$organization) {
            \Log::info('FlexibleDashboard: No organization for buildCashFloatManagementData');
            return [];
        }

        $result = [
            'showCreateButton' => false,
            'stats' => [
                'totalFloat' => 0,
                'lowFloatDepartments' => 0
            ]
        ];

        // Add Create New Cash Float button if user has permission
        if (in_array('create-float', $permissions) ||
            in_array('create-cash-float', $permissions) ||
            in_array('manage-floats', $permissions) ||
            in_array('manage-cash-floats', $permissions)) {
            $result['showCreateButton'] = true;
        }

        try {
            // Get cash floats stats
            $cashFloats = CashFloat::where('organization_id', $organization->id)
                ->where('status', 'active')
                ->with(['transactions'])
                ->get();

            \Log::info('FlexibleDashboard: buildCashFloatManagementData found floats', [
                'organization_id' => $organization->id,
                'cash_floats_count' => $cashFloats->count(),
                'cash_float_ids' => $cashFloats->pluck('id')->toArray()
            ]);

            $totalFloat = $cashFloats->sum('initial_amount');
            $lowFloatDepartments = $cashFloats->filter(function ($float) {
                return $float->alert_threshold && $float->current_balance <= $float->alert_threshold;
            })->count();

            $result['stats'] = [
                'totalFloat' => $totalFloat,
                'lowFloatDepartments' => $lowFloatDepartments
            ];

            \Log::info('FlexibleDashboard: buildCashFloatManagementData result', [
                'user_id' => $user->id,
                'organization_id' => $organization->id,
                'result' => $result
            ]);

        } catch (\Exception $e) {
            \Log::error('FlexibleDashboard: Error in buildCashFloatManagementData', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // Fallback stats on error
            $result['stats'] = [
                'totalFloat' => 0,
                'lowFloatDepartments' => 0
            ];
        }

        return $result;
    }

    /**
     * Build quick actions data based on permissions.
     * This method now provides comprehensive actions for both predefined and custom roles.
     */
    private function buildQuickActionsData(User $user, array $permissions, $organization, $department): array
    {
        $actions = [];

        // Helper function to add an action with a unique key
        $addAction = function ($key, $action) use (&$actions) {
            // Only add the action if it doesn't already exist
            if (!isset($actions[$key])) {
                $actions[$key] = $action;
            }
        };

        // Requisition actions
        if (in_array('create-requisition', $permissions) || in_array('create-requisitions', $permissions)) {
            $addAction('create-requisition', [
                'label' => 'Create Requisition',
                'href' => '/requisitions/create',
                'icon' => 'FileText',
                'variant' => 'default'
            ]);
        }

        // User management actions
        if (in_array('manage-users', $permissions) || in_array('view-users', $permissions)) {
            $addAction('manage-users', [
                'label' => 'Manage Users',
                'href' => '/users',
                'icon' => 'Users',
                'variant' => 'outline'
            ]);
        }

        // Department management actions
        if (in_array('manage-departments', $permissions) || in_array('view-departments', $permissions)) {
            $addAction('manage-departments', [
                'label' => 'Manage Departments',
                'href' => '/departments',
                'icon' => 'Building',
                'variant' => 'outline'
            ]);
        }

        // Role management actions
        if (in_array('manage-roles', $permissions) || in_array('view-roles', $permissions)) {
            $addAction('manage-roles', [
                'label' => 'Manage Roles',
                'href' => '/roles',
                'icon' => 'UserCog',
                'variant' => 'outline'
            ]);
        }

        // Branch management actions - expanded permissions check
        if (in_array('manage-branches', $permissions) ||
            in_array('view-branches', $permissions) ||
            in_array('create-branch', $permissions) ||
            in_array('edit-branch', $permissions) ||
            $user->hasRole('Organization Admin') ||
            $user->hasRole('Platform Admin')) {

            $addAction('manage-branches', [
                'label' => 'Manage Branches',
                'href' => '/branches',
                'icon' => 'GitBranch',
                'variant' => 'outline'
            ]);
        }

        // Chart of accounts actions
        if (in_array('manage-chart-of-accounts', $permissions) || in_array('view-chart-of-accounts', $permissions)) {
            $addAction('chart-of-accounts', [
                'label' => 'Chart of Accounts',
                'href' => $organization ? "/chart-of-accounts?organization_id={$organization->id}" : '/chart-of-accounts',
                'icon' => 'BookOpen',
                'variant' => 'outline'
            ]);
        }

        return $actions;
    }
}
