<?php

namespace App\Http\Controllers;

use App\Models\InventoryItem;
use App\Models\InventoryTransaction;
use App\Models\StoreRequisition;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class InventoryController extends Controller
{

    /**
     * Get the user's organization or abort if none found.
     */
    private function getUserOrganization()
    {
        $organization = Auth::user()->organizations()->first();
        if (!$organization) {
            abort(400, 'User must belong to an organization to access inventory');
        }
        return $organization;
    }

    /**
     * Check if user can access the given inventory item.
     */
    private function checkInventoryItemAccess(InventoryItem $inventoryItem)
    {
        $organization = $this->getUserOrganization();
        if ($inventoryItem->organization_id !== $organization->id) {
            abort(403, 'Unauthorized to access this inventory item');
        }
    }

    public function index()
    {
        $user = Auth::user();

        if (!$user->can('view-inventory') && !$user->can('store-keep')) {
            abort(403, 'Unauthorized to view inventory');
        }

        // Get user's organization
        $organization = $this->getUserOrganization();

        $items = InventoryItem::where('organization_id', $organization->id)
            ->with(['organization', 'branch'])
            ->orderBy('name')
            ->paginate(20);

        // Add unit information to each item
        $items->getCollection()->transform(function ($item) {
            $item->unit_info = [
                'is_discrete' => $item->isDiscreteUnit(),
                'is_continuous' => $item->isContinuousUnit(),
                'is_custom' => $item->isCustomUnit(),
                'should_use_whole_numbers' => $item->shouldUseWholeNumbers(),
            ];
            return $item;
        });

        return response()->json($items);
    }

    /**
     * Get available measurement units for frontend
     */
    public function getUnitTypes()
    {
        return response()->json([
            'unit_types' => InventoryItem::getUnitTypes(),
            'discrete_units' => InventoryItem::DISCRETE_UNITS,
            'continuous_units' => InventoryItem::CONTINUOUS_UNITS,
        ]);
    }

    public function store(Request $request)
    {
        if (!Auth::user()->can('manage-inventory') && !Auth::user()->can('store-keep')) {
            abort(403, 'Unauthorized to create inventory items');
        }

        $validated = $request->validate([
            'branch_id' => 'nullable|exists:branches,id',
            'sku' => 'required|string|unique:inventory_items,sku',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'unit_of_measure' => 'required|string|max:50',
            'quantity_on_hand' => 'nullable|numeric|min:0',
            'reorder_level' => 'nullable|numeric|min:0',
        ]);

        // Create a temporary item to check unit type for validation
        $tempItem = new InventoryItem(['unit_of_measure' => $validated['unit_of_measure']]);
        
        // Validate quantities based on unit type
        if (isset($validated['quantity_on_hand']) && !$tempItem->validateQuantity($validated['quantity_on_hand'])) {
            return response()->json([
                'error' => $tempItem->shouldUseWholeNumbers() 
                    ? 'Quantity on hand must be a whole number for this unit of measure.'
                    : 'Invalid quantity on hand value.'
            ], 422);
        }

        if (isset($validated['reorder_level']) && !$tempItem->validateQuantity($validated['reorder_level'])) {
            return response()->json([
                'error' => $tempItem->shouldUseWholeNumbers() 
                    ? 'Reorder level must be a whole number for this unit of measure.'
                    : 'Invalid reorder level value.'
            ], 422);
        }

        // Get user's organization
        $organization = $this->getUserOrganization();

        $item = InventoryItem::create([
            'organization_id' => $organization->id,
            'branch_id' => $validated['branch_id'],        
            'sku' => $validated['sku'],
            'name' => $validated['name'],
            'description' => $validated['description'] ?? null,
            'unit_of_measure' => $validated['unit_of_measure'],
            'quantity_on_hand' => $validated['quantity_on_hand'] ?? 0,
            'reorder_level' => $validated['reorder_level'] ?? 0,
        ]);

        if ($item->quantity_on_hand > 0) {
            $item->transactions()->create([
                'user_id' => Auth::id(),
                'transaction_type' => 'receipt',
                'quantity_change' => $item->quantity_on_hand,
                'transaction_date' => now(),
                'notes' => 'Initial stock entry',
            ]);
        }

        return redirect()->route('inventory.show', $item)->with('success', 'Inventory item created successfully');
    }

    public function show(InventoryItem $inventoryItem)
    {
        $user = Auth::user();

        if (!$user->can('view-inventory') && !$user->can('store-keep')) {
            abort(403, 'Unauthorized to view inventory items');
        }

        $this->checkInventoryItemAccess($inventoryItem);

        $inventoryItem->load(['organization', 'branch', 'transactions.user']);

        return \Inertia\Inertia::render('Inventory/ShowInventory', [
            'inventory_item' => array_merge($inventoryItem->toArray(), [
                'unit_info' => [
                    'is_discrete' => $inventoryItem->isDiscreteUnit(),
                    'is_continuous' => $inventoryItem->isContinuousUnit(),
                    'is_custom' => $inventoryItem->isCustomUnit(),
                    'should_use_whole_numbers' => $inventoryItem->shouldUseWholeNumbers(),
                ]
            ]),
            'user' => [
                'id' => $user->id,
                'name' => $user->first_name . ' ' . $user->last_name,
                'email' => $user->email,
                'permissions' => $user->getAllPermissions()->pluck('name')->toArray(),
            ],
        ]);
    }

    public function update(Request $request, InventoryItem $inventoryItem)
    {
        if (!Auth::user()->can('manage-inventory') && !Auth::user()->can('store-keep')) {
            abort(403, 'Unauthorized to update inventory items');
        }

        $this->checkInventoryItemAccess($inventoryItem);

        $validated = $request->validate([
            'branch_id' => 'nullable|exists:branches,id',
            'sku' => 'required|string|unique:inventory_items,sku,' . $inventoryItem->id,
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'unit_of_measure' => 'required|string|max:50',
            'reorder_level' => 'nullable|numeric|min:0',
        ]);

        // Create a temporary item to check unit type for validation
        $tempItem = new InventoryItem(['unit_of_measure' => $validated['unit_of_measure']]);
        
        // Validate reorder level based on unit type
        if (isset($validated['reorder_level']) && !$tempItem->validateQuantity($validated['reorder_level'])) {
            return response()->json([
                'error' => $tempItem->shouldUseWholeNumbers() 
                    ? 'Reorder level must be a whole number for this unit of measure.'
                    : 'Invalid reorder level value.'
            ], 422);
        }

        $inventoryItem->update($validated);

        return redirect()->route('inventory.show', $inventoryItem)->with('success', 'Inventory item updated successfully');
    }

    public function destroy(InventoryItem $inventoryItem)
    {
        if (!Auth::user()->can('manage-inventory') && !Auth::user()->can('store-keep')) {
            abort(403, 'Unauthorized to delete inventory items');
        }

        $this->checkInventoryItemAccess($inventoryItem);

        if ($inventoryItem->requisitionItems()->count() > 0) {
            return response()->json(['error' => 'Cannot delete inventory item that has been used in requisitions'], 400);
        }

        $inventoryItem->delete();

        return response()->json(['message' => 'Inventory item deleted successfully']);
    }

    public function adjustStock(Request $request, InventoryItem $inventoryItem)
    {
        if (!Auth::user()->can('manage-inventory') && !Auth::user()->can('store-keep')) {
            abort(403, 'Unauthorized to adjust inventory');
        }

        $this->checkInventoryItemAccess($inventoryItem);

        $validated = $request->validate([
            'quantity_change' => 'required|numeric',
            'notes' => 'nullable|string|max:500',
        ]);

        // Validate quantity change based on unit type
        if (!$inventoryItem->validateQuantity(abs($validated['quantity_change']))) {
            return response()->json([
                'error' => $inventoryItem->shouldUseWholeNumbers() 
                    ? 'Quantity change must be a whole number for this unit of measure.'
                    : 'Invalid quantity change value.'
            ], 422);
        }

        DB::beginTransaction();
        try {
            // Re-fetch the inventory item with a pessimistic lock
            $lockedInventoryItem = InventoryItem::find($inventoryItem->id)->lockForUpdate();

            // Prevent negative stock
            if ($lockedInventoryItem->quantity_on_hand + $validated['quantity_change'] < 0) {
                DB::rollBack();
                return response()->json(['error' => 'Quantity on hand cannot go below zero.'], 400);
            }

            $lockedInventoryItem->transactions()->create([
                'user_id' => Auth::id(),
                'transaction_type' => 'adjustment',
                'quantity_change' => $validated['quantity_change'],
                'transaction_date' => now(),
                'notes' => $validated['notes'] ?? 'Manual stock adjustment',
            ]);

            $lockedInventoryItem->increment('quantity_on_hand', $validated['quantity_change']);

            DB::commit();

            return response()->json([
                'message' => 'Stock adjusted successfully',
                'new_quantity' => $inventoryItem->fresh()->quantity_on_hand
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Failed to adjust stock'], 500);
        }
    }

    public function lowStock()
    {
        $user = Auth::user();

        if (!$user->can('view-inventory') && !$user->can('store-keep')) {
            abort(403, 'Unauthorized to view inventory');
        }

        // Get user's organization
        $organization = $this->getUserOrganization();

        $items = InventoryItem::where('organization_id', $organization->id)
            ->whereColumn('quantity_on_hand', '<=', 'reorder_level')
            ->where('reorder_level', '>', 0)
            ->with(['organization', 'branch'])
            ->orderBy('quantity_on_hand')
            ->get();

        return response()->json($items);
    }

    public function transactionHistory(InventoryItem $inventoryItem)
    {
        if (!Auth::user()->can('view-inventory') && !Auth::user()->can('store-keep')) {
            abort(403, 'Unauthorized to view inventory transactions');
        }

        $this->checkInventoryItemAccess($inventoryItem);

        $transactions = $inventoryItem->transactions()
            ->with('user')
            ->orderBy('transaction_date', 'desc')
            ->paginate(20);

        return response()->json($transactions);
    }

    public function receiveGoods(Request $request, InventoryItem $inventoryItem)
    {
        if (!Auth::user()->can('manage-inventory') && !Auth::user()->can('store-keep')) {
            abort(403, 'Unauthorized to receive goods');
        }

        $this->checkInventoryItemAccess($inventoryItem);

        $validated = $request->validate([
            'quantity_received' => 'required|numeric|min:0.01',
            'reference_document' => 'nullable|string|max:100', // PO number, delivery note, etc.
            'supplier' => 'nullable|string|max:255',
            'unit_cost' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:500',
            'quality_notes' => 'nullable|string|max:500',
            'condition' => 'required|in:good,damaged,partial',
        ]);

        // Validate quantity received based on unit type
        if (!$inventoryItem->validateQuantity($validated['quantity_received'])) {
            return response()->json([
                'error' => $inventoryItem->shouldUseWholeNumbers() 
                    ? 'Quantity received must be a whole number for this unit of measure.'
                    : 'Invalid quantity received value.'
            ], 422);
        }

        DB::beginTransaction();
        try {
            $transaction = $inventoryItem->transactions()->create([
                'user_id' => Auth::id(),
                'transaction_type' => 'receipt',
                'quantity_change' => $validated['quantity_received'],
                'related_document_type' => 'GoodsReceipt',
                'transaction_date' => now(),
                'notes' => $this->buildReceiptNotes($validated),
            ]);

            $inventoryItem->increment('quantity_on_hand', $validated['quantity_received']);

            DB::commit();

            return response()->json([
                'message' => 'Goods received successfully',
                'transaction_id' => $transaction->id,
                'new_quantity' => $inventoryItem->fresh()->quantity_on_hand,
                'condition' => $validated['condition'],
                'quality_notes' => $validated['quality_notes'] ?? null,
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Failed to receive goods'], 500);
        }
    }

    public function replenishmentSuggestions()
    {
        $user = Auth::user();

        if (!$user->can('view-inventory') && !$user->can('store-keep')) {
            abort(403, 'Unauthorized to view replenishment suggestions');
        }

        // Get user's organization
        $organization = $this->getUserOrganization();

        $suggestions = InventoryItem::where('organization_id', $organization->id)
            ->whereColumn('quantity_on_hand', '<=', 'reorder_level')
            ->where('reorder_level', '>', 0)
            ->with(['organization', 'branch'])
            ->get()
            ->map(function ($item) {
                $suggestedQuantity = ($item->reorder_level * 2) - $item->quantity_on_hand; // Suggest double reorder level

                return [
                    'item_id' => $item->id,
                    'sku' => $item->sku,
                    'name' => $item->name,
                    'current_stock' => $item->quantity_on_hand,
                    'reorder_level' => $item->reorder_level,
                    'suggested_order_quantity' => max($suggestedQuantity, $item->reorder_level),
                    'unit_of_measure' => $item->unit_of_measure,
                    'branch' => $item->branch?->name,
                    'days_out_of_stock' => $item->quantity_on_hand <= 0 ? $this->calculateDaysOutOfStock($item) : 0,
                    'priority' => $item->quantity_on_hand <= 0 ? 'critical' : 'normal',
                ];
            })
            ->sortBy(function ($item) {
                return $item['priority'] === 'critical' ? 0 : 1; // Critical items first
            })
            ->values();

        return response()->json([
            'suggestions' => $suggestions,
            'total_items' => $suggestions->count(),
            'critical_items' => $suggestions->where('priority', 'critical')->count(),
        ]);
    }

    public function dashboardSummary()
    {
        $user = Auth::user();

        if (!$user->can('view-inventory') && !$user->can('store-keep')) {
            abort(403, 'Unauthorized to view inventory dashboard');
        }

        // Get user's organization
        $organization = $this->getUserOrganization();
        $organizationId = $organization->id;

        $totalItems = InventoryItem::where('organization_id', $organizationId)->count();
        $lowStockItems = InventoryItem::where('organization_id', $organizationId)
            ->whereColumn('quantity_on_hand', '<=', 'reorder_level')
            ->where('reorder_level', '>', 0)
            ->count();
        $outOfStockItems = InventoryItem::where('organization_id', $organizationId)
            ->where('quantity_on_hand', '<=', 0)
            ->count();

        $pendingRequisitions = StoreRequisition::where('organization_id', $organizationId)
            ->where('status', StoreRequisition::STATUS_PENDING_APPROVAL)
            ->count();
        $approvedRequisitions = StoreRequisition::where('organization_id', $organizationId)
            ->where('status', StoreRequisition::STATUS_APPROVED)
            ->count();

        $recentTransactions = InventoryTransaction::whereHas('inventoryItem', function ($query) use ($organizationId) {
                $query->where('organization_id', $organizationId);
            })
            ->with(['inventoryItem', 'user'])
            ->orderBy('transaction_date', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'inventory_summary' => [
                'total_items' => $totalItems,
                'low_stock_items' => $lowStockItems,
                'out_of_stock_items' => $outOfStockItems,
                'stock_health' => $totalItems > 0 ? round((($totalItems - $lowStockItems) / $totalItems) * 100, 2) : 0,
            ],
            'requisition_summary' => [
                'pending_approval' => $pendingRequisitions,
                'awaiting_fulfillment' => $approvedRequisitions,
            ],
            'recent_activity' => $recentTransactions->map(function ($transaction) {
                return [
                    'type' => $transaction->transaction_type,
                    'item' => $transaction->inventoryItem->name,
                    'quantity' => $transaction->quantity_change,
                    'user' => $transaction->user->name,
                    'date' => $transaction->transaction_date,
                ];
            }),
        ]);
    }

    private function buildReceiptNotes($validated): string
    {
        $notes = "Goods received: {$validated['quantity_received']} units";

        if (!empty($validated['reference_document'])) {
            $notes .= " | Ref: {$validated['reference_document']}";
        }

        if (!empty($validated['supplier'])) {
            $notes .= " | Supplier: {$validated['supplier']}";
        }

        if (!empty($validated['unit_cost'])) {
            $notes .= " | Unit Cost: {$validated['unit_cost']}";
        }

        if (!empty($validated['condition']) && $validated['condition'] !== 'good') {
            $notes .= " | Condition: {$validated['condition']}";
        }

        if (!empty($validated['quality_notes'])) {
            $notes .= " | Quality Notes: {$validated['quality_notes']}";
        }

        if (!empty($validated['notes'])) {
            $notes .= " | Notes: {$validated['notes']}";
        }

        return $notes;
    }

    private function calculateDaysOutOfStock($item): int
    {
        $lastReceiptTransaction = $item->transactions()
            ->where('transaction_type', 'receipt')
            ->where('quantity_change', '>', 0)
            ->orderBy('transaction_date', 'desc')
            ->first();

        if (!$lastReceiptTransaction) {
            return 0; // Can't determine
        }

        $daysOutOfStock = now()->diffInDays($lastReceiptTransaction->transaction_date);
        return max(0, $daysOutOfStock);
    }
}
