# fly.toml app configuration file generated for sippar-winter-sound-5467 on 2025-06-20T16:17:21+03:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'sippar-winter-sound-5467'
primary_region = 'jnb'
console_command = 'php /var/www/html/artisan tinker'

[build]
  [build.args]
    NODE_VERSION = '18'
    PHP_VERSION = '8.2'

[env]
  APP_ENV = 'production'
  LOG_CHANNEL = 'stderr'
  LOG_LEVEL = 'info'
  LOG_STDERR_FORMATTER = 'Monolog\Formatter\JsonFormatter'
  SESSION_DRIVER = 'cookie'
  SESSION_SECURE_COOKIE = 'true'
  APP_URL = "https://www.getsippar.com"
  ASSET_URL = "https://www.getsippar.com"

[deploy]
  release_command = "./scripts/release.sh"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1

[processes]
  queue = "php /var/www/html/artisan queue:work --sleep=3 --tries=3"


