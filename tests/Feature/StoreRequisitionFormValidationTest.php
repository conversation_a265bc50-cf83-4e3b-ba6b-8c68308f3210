<?php

namespace Tests\Feature;

use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class StoreRequisitionFormValidationTest extends TestCase
{
    use RefreshDatabase;

    private User $employee;
    private User $storeKeeper;
    private Organization $organization;
    private Branch $branch;
    private Department $department;
    private InventoryItem $inventoryItem;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
        $this->department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id
        ]);

        // Create users
        $this->employee = User::factory()->create();
        $this->employee->organizations()->attach($this->organization->id);

        $this->storeKeeper = User::factory()->create();
        $this->storeKeeper->organizations()->attach($this->organization->id);

        // Create inventory item
        $this->inventoryItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'TEST-ITEM-001',
            'name' => 'Test Item',
            'description' => 'Test inventory item for validation',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 100,
            'reorder_level' => 10,
        ]);

        // Create permissions
        Permission::firstOrCreate(['name' => 'view-store-requisitions']);
        Permission::firstOrCreate(['name' => 'create-store-requisition']);
        Permission::firstOrCreate(['name' => 'edit-store-requisition']);
        Permission::firstOrCreate(['name' => 'store-keep']);

        // Assign permissions
        $this->employee->givePermissionTo([
            'view-store-requisitions',
            'create-store-requisition',
            'edit-store-requisition'
        ]);
        
        $this->storeKeeper->givePermissionTo(['store-keep']);
    }

    public function test_quantity_input_accepts_string_and_converts_to_number()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test requisition with string quantity',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => '15' // String input
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertRedirect();

        $this->assertDatabaseHas('store_requisition_items', [
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 15 // Should be stored as number
        ]);
    }

    public function test_quantity_input_validates_positive_numbers()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test requisition with positive quantity',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => 10
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertRedirect();
        $response->assertSessionHasNoErrors();
    }

    public function test_quantity_input_handles_decimal_values()
    {
        // Create an inventory item with a continuous unit that allows decimals
        $continuousItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'CONTINUOUS-001',
            'name' => 'Continuous Item',
            'description' => 'Test item with continuous unit',
            'unit_of_measure' => 'kg', // Continuous unit that allows decimals
            'quantity_on_hand' => 100,
            'reorder_level' => 10,
        ]);
        
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test requisition with decimal quantity',
                'items' => [
                    [
                        'inventory_item_id' => $continuousItem->id,
                        'quantity_requested' => 5.5
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertRedirect();

        $this->assertDatabaseHas('store_requisition_items', [
            'inventory_item_id' => $continuousItem->id,
            'quantity_requested' => 5.5
        ]);
    }

    public function test_quantity_input_rejects_invalid_strings()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test requisition with invalid quantity',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => 'invalid_string'
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertSessionHasErrors(['items.0.quantity_requested']);
    }

    public function test_empty_quantity_input_shows_validation_error()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test requisition with empty quantity',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => ''
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertSessionHasErrors(['items.0.quantity_requested']);
    }

    public function test_zero_quantity_input_shows_validation_error()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test requisition with zero quantity',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => 0
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertSessionHasErrors(['items.0.quantity_requested']);
    }

    public function test_negative_quantity_input_shows_validation_error()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test requisition with negative quantity',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => -5
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertSessionHasErrors(['items.0.quantity_requested']);
    }

    public function test_form_submission_converts_string_quantities_to_numbers()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test multiple items with string quantities',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => '12'
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertRedirect();

        $requisition = StoreRequisition::latest()->first();
        $item = $requisition->items->first();
        
        $this->assertIsNumeric($item->quantity_requested);
        $this->assertEquals(12, $item->quantity_requested);
    }

    public function test_total_quantity_calculation_handles_mixed_types()
    {
        // This test would be for frontend JavaScript functionality
        // Since we're testing backend, we'll test that mixed string/number inputs are handled correctly
        
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test mixed quantity types',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => '8'
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertRedirect();
        $response->assertSessionHasNoErrors();
    }

    public function test_item_validation_works_with_new_quantity_type()
    {
        // Test that item validation still works correctly with string quantities
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test item validation',
                'items' => [
                    [
                        'inventory_item_id' => 99999, // Non-existent item
                        'quantity_requested' => '5'
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertSessionHasErrors(['items.0.inventory_item_id']);
    }

    public function test_draft_saving_works_with_string_quantities()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test draft with string quantity',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => '7'
                    ]
                ],
                'save_as_draft' => true
            ]);

        $response->assertRedirect();

        $this->assertDatabaseHas('store_requisitions', [
            'purpose' => 'Test draft with string quantity',
            'status' => StoreRequisition::STATUS_DRAFT
        ]);

        $this->assertDatabaseHas('store_requisition_items', [
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 7
        ]);
    }

    

    public function test_purpose_field_validation()
    {
        // Test purpose field validation
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => '', // Empty purpose
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => 5
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertSessionHasErrors(['purpose']);
    }

    public function test_purpose_field_length_validation()
    {
        $longPurpose = str_repeat('a', 1001); // Exceeds typical length limit

        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => $longPurpose,
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => 5
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertSessionHasErrors(['purpose']);
    }

    public function test_branch_and_department_validation()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => 99999, // Non-existent branch
                'department_id' => 99999, // Non-existent department
                'purpose' => 'Test validation',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => 5
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertSessionHasErrors(['branch_id', 'department_id']);
    }

    public function test_items_array_validation()
    {
        // Test empty items array
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test empty items',
                'items' => [],
                'save_as_draft' => false
            ]);

        $response->assertSessionHasErrors(['items']);
    }

    public function test_duplicate_items_validation()
    {
        // Test that duplicate items are rejected
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test duplicate items',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => 5
                    ],
                    [
                        'inventory_item_id' => $this->inventoryItem->id, // Duplicate item
                        'quantity_requested' => 3
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertSessionHasErrors(['items']);
    }

    public function test_out_of_stock_items_validation()
    {
        // Create an out of stock item
        $outOfStockItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'OUT-OF-STOCK-001',
            'name' => 'Out of Stock Item',
            'description' => 'Test out of stock item',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 0, // Out of stock
            'reorder_level' => 10,
        ]);

        // Test that out of stock items are rejected
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test out of stock item',
                'items' => [
                    [
                        'inventory_item_id' => $outOfStockItem->id,
                        'quantity_requested' => 5
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertSessionHasErrors(['items.0.inventory_item_id']);
    }

    public function test_mixed_valid_and_invalid_items_validation()
    {
        // Create an out of stock item
        $outOfStockItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'OUT-OF-STOCK-002',
            'name' => 'Another Out of Stock Item',
            'description' => 'Test out of stock item',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 0,
            'reorder_level' => 5,
        ]);

        // Test mixed valid and invalid items
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test mixed items',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id, // Valid item
                        'quantity_requested' => 5
                    ],
                    [
                        'inventory_item_id' => $outOfStockItem->id, // Out of stock item
                        'quantity_requested' => 3
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertSessionHasErrors(['items.1.inventory_item_id']);
        $response->assertSessionDoesntHaveErrors(['items.0.inventory_item_id']);
    }

    public function test_valid_items_with_stock_pass_validation()
    {
        // Create another in-stock item
        $inStockItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'IN-STOCK-001',
            'name' => 'In Stock Item',
            'description' => 'Test in stock item',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 50,
            'reorder_level' => 10,
        ]);

        // Test that valid items with stock pass validation
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test valid items',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => 5
                    ],
                    [
                        'inventory_item_id' => $inStockItem->id,
                        'quantity_requested' => 3
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertRedirect();
        $response->assertSessionHasNoErrors();
    }
}
