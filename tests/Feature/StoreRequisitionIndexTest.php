<?php

namespace Tests\Feature;

use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\StoreRequisitionItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class StoreRequisitionIndexTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;
    private Branch $branch;
    private Department $department;
    private InventoryItem $inventoryItem;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
        $this->department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id
        ]);
        
        $this->user = User::factory()->create();
        $this->user->organizations()->attach($this->organization->id);

        $this->inventoryItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'TEST-ITEM-001',
            'name' => 'Test Item',
            'description' => 'Test inventory item for testing',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 100,
            'reorder_level' => 10,
        ]);

        // Create permissions
        Permission::firstOrCreate(['name' => 'view-store-requisitions']);
        Permission::firstOrCreate(['name' => 'create-store-requisition']);
        Permission::firstOrCreate(['name' => 'edit-store-requisition']);
        
        // Assign permissions to user
        $this->user->givePermissionTo(['view-store-requisitions', 'create-store-requisition']);
    }

    public function test_draft_requisitions_appear_in_index()
    {
        // Create a draft store requisition
        $draftRequisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->user->id,
            'purpose' => 'Test draft requisition',
            'status' => StoreRequisition::STATUS_DRAFT,
            'requested_at' => null, // Draft requisitions have null requested_at
        ]);

        // Add an item to the requisition
        StoreRequisitionItem::create([
            'store_requisition_id' => $draftRequisition->id,
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 5,
        ]);

        // Sleep for a moment to ensure different created_at timestamps
        sleep(1);

        // Create a submitted store requisition for comparison
        $submittedRequisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->user->id,
            'purpose' => 'Test submitted requisition',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        // Add an item to the submitted requisition
        StoreRequisitionItem::create([
            'store_requisition_id' => $submittedRequisition->id,
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 3,
        ]);

        // Act as the user and visit the store requisitions index
        $response = $this->actingAs($this->user)
            ->get('/store-requisitions');

        // Assert the response is successful
        $response->assertStatus(200);

        // Assert both draft and submitted requisitions appear in the list
        $response->assertInertia(fn (Assert $page) => $page
            ->component('StoreRequisitions/StoreRequisitionManagement')
            ->has('my_requisitions', 2) // Should have both requisitions
            ->where('my_requisitions.0.id', $submittedRequisition->id) // Most recent first (created_at desc)
            ->where('my_requisitions.0.status', 'pending_approval')
            ->where('my_requisitions.1.id', $draftRequisition->id) // Draft created earlier
            ->where('my_requisitions.1.status', 'draft')
            ->where('my_requisitions.1.requested_at', null) // Draft should have null requested_at
        );
    }

    public function test_user_can_only_see_own_requisitions_without_general_permission()
    {
        // Create another user
        $otherUser = User::factory()->create();
        $otherUser->organizations()->attach($this->organization->id);
        $otherUser->givePermissionTo('create-store-requisition'); // Only create permission, not view

        // Create requisitions for both users
        $userRequisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->user->id,
            'purpose' => 'User requisition',
            'status' => StoreRequisition::STATUS_DRAFT,
            'requested_at' => null,
        ]);

        $otherUserRequisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $otherUser->id,
            'purpose' => 'Other user requisition',
            'status' => StoreRequisition::STATUS_DRAFT,
            'requested_at' => null,
        ]);

        // Act as the other user (without general view permission)
        $response = $this->actingAs($otherUser)
            ->get('/store-requisitions');

        // Should only see their own requisition
        $response->assertInertia(fn (Assert $page) => $page
            ->component('StoreRequisitions/StoreRequisitionManagement')
            ->has('my_requisitions', 1)
            ->where('my_requisitions.0.id', $otherUserRequisition->id)
        );
    }

    public function test_user_can_edit_own_draft_requisition()
    {
        // Create a draft store requisition
        $draftRequisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->user->id,
            'purpose' => 'Test draft for editing',
            'status' => StoreRequisition::STATUS_DRAFT,
            'requested_at' => null,
        ]);

        // Add an item to the requisition
        StoreRequisitionItem::create([
            'store_requisition_id' => $draftRequisition->id,
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 3,
        ]);

        // Give user edit permission
        $this->user->givePermissionTo('edit-store-requisition');

        // User should be able to access the edit page
        $response = $this->actingAs($this->user)
            ->get("/store-requisitions/{$draftRequisition->id}/edit");

        $response->assertStatus(200);
        $response->assertInertia(fn (Assert $page) => $page
            ->component('StoreRequisitions/EditStoreRequisition')
            ->where('store_requisition.id', $draftRequisition->id)
            ->where('store_requisition.status', 'draft')
        );
    }

    public function test_user_can_submit_draft_requisition_for_approval()
    {
        // Create a draft store requisition
        $draftRequisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->user->id,
            'purpose' => 'Test draft for submission',
            'status' => StoreRequisition::STATUS_DRAFT,
            'requested_at' => null,
        ]);

        // Add an item to the requisition
        StoreRequisitionItem::create([
            'store_requisition_id' => $draftRequisition->id,
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 2,
        ]);

        // Submit the draft for approval
        $response = $this->actingAs($this->user)
            ->post("/store-requisitions/{$draftRequisition->id}/submit");

        $response->assertRedirect();

        // Verify the requisition status changed to pending_approval
        $draftRequisition->refresh();
        $this->assertEquals(StoreRequisition::STATUS_PENDING_APPROVAL, $draftRequisition->status);
        $this->assertNotNull($draftRequisition->requested_at);

        // Verify history record was created
        $this->assertDatabaseHas('store_requisition_histories', [
            'store_requisition_id' => $draftRequisition->id,
            'action' => 'submitted_for_approval',
            'user_id' => $this->user->id,
        ]);
    }
}
