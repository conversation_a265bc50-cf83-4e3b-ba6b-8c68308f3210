<?php

namespace Tests\Feature;

use App\Models\Organization;
use App\Models\ChartOfAccount;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ChartOfAccountTypeFromParentTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create platform-level chart of accounts
        $this->createPlatformAccounts();
    }

    private function createPlatformAccounts(): void
    {
        $platformTypes = [
            ['code' => 'AST-000', 'name' => 'asset', 'account_type' => 'asset'],
            ['code' => 'LBT-000', 'name' => 'liability', 'account_type' => 'liability'],
            ['code' => 'EQT-000', 'name' => 'equity', 'account_type' => 'equity'],
            ['code' => 'REV-000', 'name' => 'revenue', 'account_type' => 'revenue'],
            ['code' => 'EXP-000', 'name' => 'expense', 'account_type' => 'expense'],
        ];

        foreach ($platformTypes as $type) {
            ChartOfAccount::create([
                'organization_id' => null,
                'code' => $type['code'],
                'name' => $type['name'],
                'account_type' => $type['account_type'],
                'is_active' => true,
                'description' => 'Platform-level ' . $type['name'] . ' account',
            ]);
        }
    }

    public function test_account_type_is_determined_from_parent_account(): void
    {
        // Create a user and authenticate
        $user = User::factory()->create(['is_platform_admin' => true]);
        $this->actingAs($user);

        // Create an organization
        $organization = Organization::create([
            'name' => 'Test Organization',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+************',
            'address' => '123 Test Street',
            'status' => 'active',
        ]);

        // Get the expense parent account created by organization setup
        $expenseParent = ChartOfAccount::where('organization_id', $organization->id)
            ->where('account_type', 'expense')
            ->first();

        $this->assertNotNull($expenseParent, 'Expense parent account should exist');

        // Create a child account without specifying account_type
        $response = $this->post('/chart-of-accounts', [
            'organization_id' => $organization->id,
            'name' => 'Office Supplies',
            'description' => 'Supplies for the office',
            'parent_id' => $expenseParent->id,
            'is_active' => true,
        ]);

        $response->assertRedirect('/chart-of-accounts');
        $response->assertSessionHas('success');

        // Verify the child account was created with the correct account type
        $childAccount = ChartOfAccount::where('organization_id', $organization->id)
            ->where('name', 'Office Supplies')
            ->first();

        $this->assertNotNull($childAccount);
        $this->assertEquals('expense', $childAccount->account_type);
        $this->assertEquals($expenseParent->id, $childAccount->parent_id);
    }

    public function test_account_type_defaults_to_expense_when_no_parent(): void
    {
        // Create a user and authenticate
        $user = User::factory()->create(['is_platform_admin' => true]);
        $this->actingAs($user);

        // Create an organization
        $organization = Organization::create([
            'name' => 'Test Organization',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+************',
            'address' => '123 Test Street',
            'status' => 'active',
        ]);

        // Create an account without parent_id
        $response = $this->post('/chart-of-accounts', [
            'organization_id' => $organization->id,
            'name' => 'Top Level Account',
            'description' => 'A top level account',
            'is_active' => true,
        ]);

        $response->assertRedirect('/chart-of-accounts');
        $response->assertSessionHas('success');

        // Verify the account was created with expense type (default)
        $account = ChartOfAccount::where('organization_id', $organization->id)
            ->where('name', 'Top Level Account')
            ->first();

        $this->assertNotNull($account);
        $this->assertEquals('expense', $account->account_type);
        $this->assertNull($account->parent_id);
    }

    public function test_update_account_type_is_determined_from_new_parent(): void
    {
        // Create a user and authenticate
        $user = User::factory()->create(['is_platform_admin' => true]);
        $this->actingAs($user);

        // Create an organization
        $organization = Organization::create([
            'name' => 'Test Organization',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+************',
            'address' => '123 Test Street',
            'status' => 'active',
        ]);

        // Get parent accounts
        $expenseParent = ChartOfAccount::where('organization_id', $organization->id)
            ->where('account_type', 'expense')
            ->first();
        
        $assetParent = ChartOfAccount::where('organization_id', $organization->id)
            ->where('account_type', 'asset')
            ->first();

        // Create a child account under expense
        $childAccount = ChartOfAccount::create([
            'organization_id' => $organization->id,
            'name' => 'Test Account',
            'account_type' => 'expense',
            'parent_id' => $expenseParent->id,
            'is_active' => true,
        ]);

        // Update the account to move it under asset parent
        $response = $this->put("/chart-of-accounts/{$childAccount->id}", [
            'organization_id' => $organization->id,
            'name' => 'Test Account Updated',
            'description' => 'Updated description',
            'parent_id' => $assetParent->id,
            'is_active' => true,
        ]);

        $response->assertRedirect('/chart-of-accounts');
        $response->assertSessionHas('success');

        // Verify the account type was updated to match the new parent
        $childAccount->refresh();
        $this->assertEquals('asset', $childAccount->account_type);
        $this->assertEquals($assetParent->id, $childAccount->parent_id);
    }
}
