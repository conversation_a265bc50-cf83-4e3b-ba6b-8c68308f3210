<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use App\Models\Supplier;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class UnifiedLoginTest extends TestCase
{
    use RefreshDatabase;

    public function test_regular_user_can_login_through_unified_login()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/login', [
                'email' => '<EMAIL>',
                'password' => 'password123',
                'remember' => false,
            ]);

        $response->assertRedirect(route('dashboard', absolute: false));
        $this->assertAuthenticatedAs($user, 'web');
    }

    public function test_supplier_can_login_through_unified_login()
    {
        $supplier = Supplier::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/login', [
                'email' => '<EMAIL>',
                'password' => 'password123',
                'remember' => false,
            ]);

        $response->assertRedirect(route('suppliers.dashboard'));
        $this->assertAuthenticatedAs($supplier, 'supplier');
    }

    public function test_user_takes_precedence_over_supplier_with_same_email()
    {
        // Create both user and supplier with same email
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $supplier = Supplier::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/login', [
                'email' => '<EMAIL>',
                'password' => 'password123',
                'remember' => false,
            ]);

        // Should authenticate as user (first priority) and redirect to user dashboard
        $response->assertRedirect(route('dashboard', absolute: false));
        $this->assertAuthenticatedAs($user, 'web');
        $this->assertGuest('supplier');
    }

    public function test_invalid_credentials_fail_for_both_user_types()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $supplier = Supplier::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/login', [
                'email' => '<EMAIL>',
                'password' => 'wrongpassword',
                'remember' => false,
            ]);

        $response->assertSessionHasErrors('email');
        $this->assertGuest('web');
        $this->assertGuest('supplier');
    }

    public function test_nonexistent_user_fails_authentication()
    {
        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/login', [
                'email' => '<EMAIL>',
                'password' => 'password123',
                'remember' => false,
            ]);

        $response->assertSessionHasErrors('email');
        $this->assertGuest('web');
        $this->assertGuest('supplier');
    }

    public function test_remember_functionality_works_for_users()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/login', [
                'email' => '<EMAIL>',
                'password' => 'password123',
                'remember' => true,
            ]);

        $response->assertRedirect(route('dashboard', absolute: false));
        $this->assertAuthenticatedAs($user, 'web');
        
        // Check that remember token was set
        $this->assertNotNull($user->fresh()->remember_token);
    }

    public function test_remember_functionality_works_for_suppliers()
    {
        $supplier = Supplier::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/login', [
                'email' => '<EMAIL>',
                'password' => 'password123',
                'remember' => true,
            ]);

        $response->assertRedirect(route('suppliers.dashboard'));
        $this->assertAuthenticatedAs($supplier, 'supplier');
        
        // Check that remember token was set
        $this->assertNotNull($supplier->fresh()->remember_token);
    }

    public function test_rate_limiting_works_across_both_user_types()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        // Make 5 failed attempts (the rate limit)
        for ($i = 0; $i < 5; $i++) {
            $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
                ->post('/login', [
                    'email' => '<EMAIL>',
                    'password' => 'wrongpassword',
                ]);
        }

        // 6th attempt should be rate limited
        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/login', [
                'email' => '<EMAIL>',
                'password' => 'wrongpassword',
            ]);

        $response->assertSessionHasErrors('email');
        $errors = session('errors')->get('email');
        $this->assertStringContainsString('Too many login attempts', $errors[0]);
    }
}
