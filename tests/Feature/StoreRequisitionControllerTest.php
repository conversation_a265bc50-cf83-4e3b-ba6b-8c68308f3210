<?php

namespace Tests\Feature;

use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\StoreRequisitionItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class StoreRequisitionControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $employee;
    private User $storeKeeper;
    private User $overseer;
    private Organization $organization;
    private Branch $branch;
    private Department $department;
    private InventoryItem $inventoryItem;

    protected function setUp(): void
    {
        parent::setUp();

        
        $this->organization = Organization::factory()->create();
        $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
        $this->department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id
        ]);

        
        $this->employee = User::factory()->create();
        $this->employee->organizations()->attach($this->organization->id);
        $this->employee->departments()->attach($this->department->id);

        $this->storeKeeper = User::factory()->create();
        $this->storeKeeper->organizations()->attach($this->organization->id);
        $this->storeKeeper->departments()->attach($this->department->id);

        $this->overseer = User::factory()->create();
        $this->overseer->organizations()->attach($this->organization->id);
        $this->overseer->departments()->attach($this->department->id);

        $this->financeManager = User::factory()->create();
        $this->financeManager->organizations()->attach($this->organization->id);
        $this->financeManager->departments()->attach($this->department->id);

        $this->organizationAdmin = User::factory()->create();
        $this->organizationAdmin->organizations()->attach($this->organization->id);
        $this->organizationAdmin->departments()->attach($this->department->id);

        
        $this->inventoryItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'TEST-ITEM-001',
            'name' => 'Test Item',
            'description' => 'Test inventory item for testing',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 100,            
            'reorder_level' => 10,
        ]);

        
        Permission::firstOrCreate(['name' => 'view-store-requisitions']);
        Permission::firstOrCreate(['name' => 'create-store-requisition']);
        Permission::firstOrCreate(['name' => 'edit-store-requisition']);
        Permission::firstOrCreate(['name' => 'approve-store-requisition']);
        Permission::firstOrCreate(['name' => 'issue-store-items']);
        Permission::firstOrCreate(['name' => 'store-keep']);

        
        $financeManagerRole = \Spatie\Permission\Models\Role::firstOrCreate([
            'name' => 'Finance Manager',
            'organization_id' => $this->organization->id
        ]);

        
        $this->employee->givePermissionTo([
            'view-store-requisitions',
            'create-store-requisition',
            'edit-store-requisition'
        ]);
        $this->storeKeeper->givePermissionTo(['store-keep']);
        
        $this->overseer->givePermissionTo(['view-store-requisitions', 'create-store-requisition', 'edit-store-requisition']);
        
        $this->financeManager->givePermissionTo(['view-store-requisitions', 'create-store-requisition', 'edit-store-requisition', 'approve-store-requisition']);

        
        $this->financeManager->assignRole($financeManagerRole);
    }

    public function test_employee_can_create_store_requisition()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test requisition for office supplies',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => 5,
                    ]
                ],
                'save_as_draft' => false,
            ]);

        $response->assertRedirect();

        
        $this->assertDatabaseHas('store_requisitions', [
            'organization_id' => $this->organization->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Test requisition for office supplies',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        ]);

        
        $requisition = StoreRequisition::where('requester_user_id', $this->employee->id)->first();
        $this->assertDatabaseHas('store_requisition_items', [
            'store_requisition_id' => $requisition->id,
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 5,
        ]);
    }

    public function test_employee_can_save_requisition_as_draft()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Draft requisition',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => 3,
                    ]
                ],
                'save_as_draft' => true,
            ]);

        $response->assertRedirect();

        
        $this->assertDatabaseHas('store_requisitions', [
            'organization_id' => $this->organization->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Draft requisition',
            'status' => StoreRequisition::STATUS_DRAFT,
            'requested_at' => null,
        ]);
    }

    public function test_validation_prevents_empty_items()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Requisition without items',
                'items' => [],
                'save_as_draft' => false,
            ]);

        $response->assertSessionHasErrors(['items']);
    }

    public function test_validation_enforces_quantity_limits()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Requisition with invalid quantity',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => 0, 
                    ]
                ],
                'save_as_draft' => false,
            ]);

        $response->assertSessionHasErrors(['items.0.quantity_requested']);
    }

    public function test_validation_enforces_purpose_length_limit()
    {
        $longPurpose = str_repeat('a', 1001); 

        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => $longPurpose,
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => 5,
                    ]
                ],
                'save_as_draft' => false,
            ]);

        $response->assertSessionHasErrors(['purpose']);
    }

    public function test_validation_enforces_items_limit()
    {
        
        $items = [];
        for ($i = 0; $i < 51; $i++) {
            $items[] = [
                'inventory_item_id' => $this->inventoryItem->id,
                'quantity_requested' => 1,
            ];
        }

        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Requisition with too many items',
                'items' => $items,
                'save_as_draft' => false,
            ]);

        $response->assertSessionHasErrors(['items']);
    }

    public function test_employee_can_edit_own_draft_requisition()
    {
        $draftRequisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Original purpose',
            'status' => StoreRequisition::STATUS_DRAFT,
        ]);

        StoreRequisitionItem::create([
            'store_requisition_id' => $draftRequisition->id,
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 3,
        ]);

        $response = $this->actingAs($this->employee)
            ->put("/store-requisitions/{$draftRequisition->id}", [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Updated purpose',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => 5,
                    ]
                ],
                'save_as_draft' => false,
            ]);

        $response->assertRedirect();

        
        $draftRequisition->refresh();
        $this->assertEquals('Updated purpose', $draftRequisition->purpose);
        $this->assertEquals(StoreRequisition::STATUS_PENDING_APPROVAL, $draftRequisition->status);
        $this->assertNotNull($draftRequisition->requested_at);
    }

    public function test_employee_cannot_edit_submitted_requisition()
    {
        $submittedRequisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Submitted requisition',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        $response = $this->actingAs($this->employee)
            ->put("/store-requisitions/{$submittedRequisition->id}", [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Attempted update',
                'items' => [
                    [
                        'inventory_item_id' => $this->inventoryItem->id,
                        'quantity_requested' => 5,
                    ]
                ],
            ]);

        $response->assertStatus(403);
    }

    public function test_store_keeper_can_approve_employee_requisition()
    {
        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Requisition for approval',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Approved for processing',
            ]);

        $response->assertRedirect();

        
        $requisition->refresh();
        $this->assertEquals(StoreRequisition::STATUS_APPROVED, $requisition->status);
        $this->assertEquals($this->storeKeeper->id, $requisition->approver_user_id);
        $this->assertNotNull($requisition->approved_at);

        
        $this->assertDatabaseHas('store_requisition_histories', [
            'store_requisition_id' => $requisition->id,
            'action' => 'approved',
            'user_id' => $this->storeKeeper->id,
            'comments' => 'Approved for processing',
        ]);
    }

    public function test_store_keeper_can_reject_employee_requisition()
    {
        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Requisition for rejection',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/reject", [
                'rejection_reason' => 'Insufficient justification provided',
            ]);

        $response->assertRedirect();

        
        $requisition->refresh();
        $this->assertEquals(StoreRequisition::STATUS_REJECTED, $requisition->status);
        $this->assertEquals('Insufficient justification provided', $requisition->rejection_reason);

        
        $this->assertDatabaseHas('store_requisition_histories', [
            'store_requisition_id' => $requisition->id,
            'action' => 'rejected',
            'user_id' => $this->storeKeeper->id,
        ]);
    }

    public function test_overseer_cannot_approve_store_keeper_requisition()
    {
        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->storeKeeper->id,
            'purpose' => 'Store keeper requisition',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        $response = $this->actingAs($this->overseer)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Attempted approval by overseer',
            ]);

        $response->assertStatus(403);
    }

    public function test_store_keeper_can_issue_approved_requisition()
    {
        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Approved requisition for issuing',
            'status' => StoreRequisition::STATUS_APPROVED,
            'requested_at' => now(),
            'approved_at' => now(),
        ]);

        $requisitionItem = StoreRequisitionItem::create([
            'store_requisition_id' => $requisition->id,
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 10,
        ]);

        $originalQuantity = $this->inventoryItem->quantity_on_hand;

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/issue", [
                'items' => [
                    [
                        'id' => $requisitionItem->id,
                        'quantity_issued' => 8,
                    ]
                ],
            ]);

        $response->assertRedirect();

        
        $requisitionItem->refresh();
        $this->assertEquals(8, $requisitionItem->quantity_issued);

        
        $this->inventoryItem->refresh();
        $this->assertEquals($originalQuantity - 8, $this->inventoryItem->quantity_on_hand);

        
        $this->assertDatabaseHas('inventory_transactions', [
            'inventory_item_id' => $this->inventoryItem->id,
            'transaction_type' => 'issuance',
            'quantity_change' => -8,
        ]);
    }

    public function test_unauthorized_user_cannot_access_store_requisitions()
    {
        $unauthorizedUser = User::factory()->create();

        $response = $this->actingAs($unauthorizedUser)
            ->get('/store-requisitions');

        $response->assertStatus(400); 
    }

    public function test_user_cannot_access_other_organization_requisitions()
    {
        $otherOrganization = Organization::factory()->create();
        $otherUser = User::factory()->create();
        $otherUser->organizations()->attach($otherOrganization->id);
        $otherUser->givePermissionTo(['view-store-requisitions']);

        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Private requisition',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        $response = $this->actingAs($otherUser)
            ->get("/store-requisitions/{$requisition->id}");

        $response->assertStatus(403);
    }

    public function test_no_inventory_items_returns_error()
    {
        
        $this->inventoryItem->delete();

        $response = $this->actingAs($this->employee)
            ->get('/store-requisitions/create');

        $response->assertRedirect(route('store-requisitions.index'));
        $response->assertSessionHas('error', 'No inventory items found. Please contact your administrator for assistance.');
    }

    public function test_api_rate_limiting_is_enforced()
    {
        
        for ($i = 0; $i < 65; $i++) {
            $response = $this->actingAs($this->storeKeeper)
                ->post('/api/store-requisitions/validate-stock', [
                    'requisition_id' => 1,
                    'items' => [
                        [
                            'id' => 1,
                            'quantity_issued' => 1,
                        ]
                    ],
                ]);

            if ($i < 60) {
                
                $this->assertNotEquals(429, $response->getStatusCode());
            } else {
                
                $response->assertStatus(429);
                break;
            }
        }
    }

    public function test_finance_manager_can_approve_store_keeper_requisition()
    {
        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->storeKeeper->id,
            'purpose' => 'Store keeper requisition for finance manager approval',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        $response = $this->actingAs($this->financeManager)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Approved by finance manager',
            ]);

        $response->assertRedirect();

        
        $requisition->refresh();
        $this->assertEquals(StoreRequisition::STATUS_APPROVED, $requisition->status);
        $this->assertEquals($this->financeManager->id, $requisition->approver_user_id);
        $this->assertNotNull($requisition->approved_at);
    }

    public function test_finance_manager_cannot_approve_employee_requisition()
    {
        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Employee requisition',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        $response = $this->actingAs($this->financeManager)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Attempted approval by finance manager',
            ]);

        $response->assertStatus(403);
    }

    public function test_overseer_cannot_approve_employee_requisition()
    {
        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Employee requisition',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        $response = $this->actingAs($this->overseer)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Attempted approval by overseer',
            ]);

        $response->assertStatus(403);
    }
}
