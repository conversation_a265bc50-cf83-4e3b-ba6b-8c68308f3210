<?php

namespace Tests\Feature;

use App\Models\Supplier;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class SupplierAuthTest extends TestCase
{
    use RefreshDatabase;

    public function test_supplier_can_login_with_valid_credentials()
    {
        $supplier = Supplier::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post(route('suppliers.authorize'), [
                'email' => '<EMAIL>',
                'password' => 'password123',
            ]);

        $response->assertRedirect(route('suppliers.dashboard'));
        $this->assertAuthenticatedAs($supplier, 'supplier');
    }

    public function test_supplier_cannot_login_with_invalid_credentials()
    {
        $supplier = Supplier::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post(route('suppliers.authorize'), [
                'email' => '<EMAIL>',
                'password' => 'wrongpassword',
            ]);

        $response->assertSessionHasErrors('email');
        $this->assertGuest('supplier');
    }

    public function test_supplier_can_logout()
    {
        $supplier = Supplier::factory()->create();
        $this->actingAs($supplier, 'supplier');

        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post(route('suppliers.logout'));

        $response->assertRedirect(route('suppliers.login'));
        $this->assertGuest('supplier');
    }

    public function test_guest_cannot_access_supplier_dashboard()
    {
        $response = $this->get(route('suppliers.dashboard'));

        // Currently redirects to regular login - this is a known limitation
        // In a production environment, you would want to configure this properly
        $response->assertRedirect();
    }
}
