<?php

namespace Tests\Feature;

use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class StoreRequisitionUnitValidationTest extends TestCase
{
    use RefreshDatabase;

    private User $employee;
    private Organization $organization;
    private Branch $branch;
    private Department $department;
    private InventoryItem $discreteItem;
    private InventoryItem $continuousItem;
    private InventoryItem $customItem;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
        $this->department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id
        ]);

        // Create user
        $this->employee = User::factory()->create();
        $this->employee->organizations()->attach($this->organization->id);

        // Create inventory items with different unit types
        $this->discreteItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'DISCRETE-001',
            'name' => 'Office Chairs',
            'description' => 'Discrete unit item - should only accept whole numbers',
            'unit_of_measure' => 'pieces',
            'quantity_on_hand' => 50,
            'reorder_level' => 10,
        ]);

        $this->continuousItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'CONTINUOUS-001',
            'name' => 'Cleaning Solution',
            'description' => 'Continuous unit item - should accept decimal numbers',
            'unit_of_measure' => 'liters',
            'quantity_on_hand' => 25.5,
            'reorder_level' => 5.0,
        ]);

        $this->customItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'CUSTOM-001',
            'name' => 'Custom Unit Item',
            'description' => 'Custom unit item - should accept decimal numbers by default',
            'unit_of_measure' => 'custom_unit',
            'quantity_on_hand' => 15.75,
            'reorder_level' => 3.25,
        ]);

        // Create permissions
        Permission::firstOrCreate(['name' => 'create-store-requisition']);
        $this->employee->givePermissionTo('create-store-requisition');
    }

    public function test_discrete_unit_accepts_whole_numbers()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test discrete unit with whole number',
                'items' => [
                    [
                        'inventory_item_id' => $this->discreteItem->id,
                        'quantity_requested' => 5
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertRedirect();
        $response->assertSessionHasNoErrors();

        $this->assertDatabaseHas('store_requisition_items', [
            'inventory_item_id' => $this->discreteItem->id,
            'quantity_requested' => 5
        ]);
    }

    public function test_discrete_unit_rejects_decimal_numbers()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test discrete unit with decimal',
                'items' => [
                    [
                        'inventory_item_id' => $this->discreteItem->id,
                        'quantity_requested' => 5.5
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertSessionHasErrors(['items.0.quantity_requested']);
        
        // Verify the error message mentions the unit type
        $errors = session('errors');
        $this->assertStringContainsString('whole number', $errors->first('items.0.quantity_requested'));
        $this->assertStringContainsString('pieces', $errors->first('items.0.quantity_requested'));
    }

    public function test_discrete_unit_rejects_string_decimal_numbers()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test discrete unit with string decimal',
                'items' => [
                    [
                        'inventory_item_id' => $this->discreteItem->id,
                        'quantity_requested' => '3.7'
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertSessionHasErrors(['items.0.quantity_requested']);
        
        // Verify the error message mentions the unit type
        $errors = session('errors');
        $this->assertStringContainsString('whole number', $errors->first('items.0.quantity_requested'));
        $this->assertStringContainsString('pieces', $errors->first('items.0.quantity_requested'));
    }

    public function test_continuous_unit_accepts_whole_numbers()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test continuous unit with whole number',
                'items' => [
                    [
                        'inventory_item_id' => $this->continuousItem->id,
                        'quantity_requested' => 3
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertRedirect();
        $response->assertSessionHasNoErrors();

        $this->assertDatabaseHas('store_requisition_items', [
            'inventory_item_id' => $this->continuousItem->id,
            'quantity_requested' => 3
        ]);
    }

    public function test_continuous_unit_accepts_decimal_numbers()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test continuous unit with decimal',
                'items' => [
                    [
                        'inventory_item_id' => $this->continuousItem->id,
                        'quantity_requested' => 2.5
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertRedirect();
        $response->assertSessionHasNoErrors();

        $this->assertDatabaseHas('store_requisition_items', [
            'inventory_item_id' => $this->continuousItem->id,
            'quantity_requested' => 2.5
        ]);
    }

    public function test_custom_unit_accepts_decimal_numbers_by_default()
    {
        $response = $this->actingAs($this->employee)
            ->post('/store-requisitions', [
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'purpose' => 'Test custom unit with decimal',
                'items' => [
                    [
                        'inventory_item_id' => $this->customItem->id,
                        'quantity_requested' => 7.25
                    ]
                ],
                'save_as_draft' => false
            ]);

        $response->assertRedirect();
        $response->assertSessionHasNoErrors();

        $this->assertDatabaseHas('store_requisition_items', [
            'inventory_item_id' => $this->customItem->id,
            'quantity_requested' => 7.25
        ]);
    }
}