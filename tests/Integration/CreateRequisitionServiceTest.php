<?php
/*
* date: 2025-07-03
* description: Integration test for RequisitionService::createRequisition
* file: tests/Integration/Requisition/CreateRequisitionServiceTest.php
*/

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Src\Requisition\Application\Services\RequisitionService;
use Src\ApprovalWorkflow\Application\Services\ApprovalWorkflowService;
use Src\Requisition\Domain\Repositories\RequisitionRepositoryInterface;
use Src\Requisition\Domain\Repositories\RequisitionItemRepositoryInterface;
use Src\Requisition\Domain\Repositories\RequisitionHistoryRepositoryInterface;
use Src\Disbursement\Application\Services\DisbursementService;
use App\Models\User;
use App\Models\ChartOfAccount;
use App\Models\RequisitionFormDetails;
use App\Models\Organization;
use App\Models\Branch;
use App\Models\Department;
use App\Models\ApprovalWorkflow;
use App\Models\ApprovalWorkflowStep;
use Tests\TestCase;

uses(TestCase::class, RefreshDatabase::class);

it('creates a requisition successfully through the service', function () {
    // Authenticate test user
    $user = User::factory()->create();
    Auth::login($user);

    // Create organization structure
    $organization = Organization::factory()->create();
    $branch = Branch::factory()->create(['organization_id' => $organization->id]);
    $department = Department::factory()->create([
        'organization_id' => $organization->id,
        'branch_id' => $branch->id,
    ]);

    // Chart of Account
    $chartOfAccount = ChartOfAccount::factory()->create();

    // Requisition form details
    $formDetails = RequisitionFormDetails::factory()->create([
        'organization_id' => $organization->id,
        'branch_id' => $branch->id,
        'department_id' => $department->id,
        'requester_user_id' => $user->id,
    ]);

    // Create workflow and step using factories
    $workflow = ApprovalWorkflow::factory()->create([
        'organization_id' => $organization->id,
        'branch_id' => $branch->id,
        'department_id' => $department->id,
    ]);

    $step = ApprovalWorkflowStep::factory()->create([
        'approval_workflow_id' => $workflow->id,
        'step_number' => 1,
    ]);

    // Mock ApprovalWorkflowService
    $workflowServiceMock = Mockery::mock(ApprovalWorkflowService::class);
    $workflowServiceMock->shouldReceive('findWorkflowAndStep')
        ->once()
        ->with($organization->id, $branch->id, $department->id)
        ->andReturn(['workflow' => $workflow, 'step' => $step]);

    $workflowServiceMock->shouldReceive('findApproverForStep')
        ->once()
        ->with($step->id, $organization->id, $user->id)
        ->andReturn(999);

    $workflowServiceMock->shouldReceive('createApprovalRecord')
        ->once();

    // Bind mock into container
    app()->instance(ApprovalWorkflowService::class, $workflowServiceMock);

    // Create service instance with real repositories and mocked workflow service
    $service = new RequisitionService(
        app(RequisitionRepositoryInterface::class),
        app(RequisitionItemRepositoryInterface::class),
        app(RequisitionHistoryRepositoryInterface::class),
        app(ApprovalWorkflowService::class),
        app(DisbursementService::class)
    );

    // Define requisition data
    $requisitionData = [
        'requisition_form_uuid' => $formDetails->requisition_form_uuid,
        'purpose' => 'Integration test requisition',
        'notes' => 'Created during test',
        'requisition_items' => [
            [
                'chart_of_account_id' => $chartOfAccount->id,
                'description' => 'Test item',
                'quantity' => 2,
                'unit_price' => 500,
            ]
        ]
    ];

    // Call the service method
    $requisition = $service->createRequisition($requisitionData);

    // Assertions
    expect($requisition)->not->toBeNull();
    expect($requisition->purpose)->toBe('Integration test requisition');
    // expect($requisition->total_amount)->toEqual(1000.00);
    expect($requisition->items)->toHaveCount(1);
    expect($requisition->history)->toHaveCount(1);

    // Ensure the temporary form was deleted
    $this->assertDatabaseMissing('requisition_form_details', [
        'requisition_form_uuid' => $formDetails->requisition_form_uuid,
    ]);
});
