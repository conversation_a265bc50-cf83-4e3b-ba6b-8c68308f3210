<?php
/*
* date: 2025-07-03
* description: Requisition schema integration test
* file: tests/Integration/Requisition/DatabaseStructureTest.php
*/

use Illuminate\Support\Facades\Schema;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

uses(TestCase::class, RefreshDatabase::class);

it('has requisitions table', function () {
    expect(Schema::hasTable('requisitions'))->toBeTrue();
});

it('has all expected requisition columns', function () {
    $columns = Schema::getColumnListing('requisitions');

    expect($columns)->toBeArray();

    expect($columns)->toContain(
        'id',
        'organization_id',
        'branch_id',
        'department_id',
        'requester_user_id',
        'requisition_number',
        'purpose',
        'status',
        'notes',
        'total_amount',
        'created_at',
        'updated_at',
    );
});
