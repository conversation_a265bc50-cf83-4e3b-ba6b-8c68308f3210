<?php

namespace Tests\Unit\Seeders;

use Database\Seeders\PurchaseRequisitionPermissionsSeeder;
use Database\Seeders\RoleAndPermissionSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

uses(TestCase::class, RefreshDatabase::class);

beforeEach(function () {
    // Seed basic roles first
    $this->seed(RoleAndPermissionSeeder::class);
});

it('creates all purchase requisition permissions', function () {
    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert - Check that all expected permissions exist
    $expectedPermissions = [
        'manage-purchase-requisitions',
        'create-purchase-requisition',
        'view-purchase-requisitions',
        'edit-purchase-requisition',
        'delete-purchase-requisition',
        'submit-purchase-requisition',
        'approve-purchase-requisition',
        'reject-purchase-requisition',
        'review-purchase-requisition',
        'amend-purchase-requisition',
        'cancel-purchase-requisition',
        'convert-to-tender',
        'manage-purchase-requisition-items',
        'add-purchase-requisition-items',
        'edit-purchase-requisition-items',
        'remove-purchase-requisition-items',
        'view-purchase-requisition-reports',
        'export-purchase-requisition-reports',
        'view-purchase-requisition-analytics',
        'manage-purchase-approval-workflows',
        'view-purchase-approval-workflows',
        'create-purchase-approval-workflows',
        'edit-purchase-approval-workflows',
        'delete-purchase-approval-workflows',
        'view-purchase-requisition-history',
        'view-purchase-requisition-audit-logs',
    ];

    foreach ($expectedPermissions as $permissionName) {
        expect(Permission::where('name', $permissionName)->exists())
            ->toBeTrue("Permission '{$permissionName}' should exist");
    }

    expect(Permission::whereIn('name', $expectedPermissions)->count())
        ->toBe(count($expectedPermissions));
});

it('assigns correct permissions to platform admin role', function () {
    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert
    $platformAdminRole = Role::where('name', 'Platform Admin')->first();
    expect($platformAdminRole)->not()->toBeNull();

    $criticalPermissions = [
        'manage-purchase-requisitions',
        'create-purchase-requisition',
        'view-purchase-requisitions',
        'approve-purchase-requisition',
        'convert-to-tender',
        'manage-purchase-approval-workflows',
    ];

    foreach ($criticalPermissions as $permission) {
        expect($platformAdminRole->hasPermissionTo($permission))
            ->toBeTrue("Platform Admin should have '{$permission}' permission");
    }
});

it('assigns correct permissions to employee role', function () {
    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert
    $employeeRole = Role::where('name', 'Employee')->first();
    expect($employeeRole)->not()->toBeNull();

    $employeePermissions = [
        'create-purchase-requisition',
        'view-purchase-requisitions',
        'edit-purchase-requisition',
        'submit-purchase-requisition',
        'add-purchase-requisition-items',
        'edit-purchase-requisition-items',
        'remove-purchase-requisition-items',
    ];

    foreach ($employeePermissions as $permission) {
        expect($employeeRole->hasPermissionTo($permission))
            ->toBeTrue("Employee should have '{$permission}' permission");
    }

    // Employee should NOT have these permissions
    $restrictedPermissions = [
        'approve-purchase-requisition',
        'delete-purchase-requisition',
        'manage-purchase-approval-workflows',
        'convert-to-tender',
    ];

    foreach ($restrictedPermissions as $permission) {
        expect($employeeRole->hasPermissionTo($permission))
            ->toBeFalse("Employee should NOT have '{$permission}' permission");
    }
});

it('assigns correct permissions to hod role', function () {
    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert
    $hodRole = Role::where('name', 'HOD')->first();
    expect($hodRole)->not()->toBeNull();

    $hodPermissions = [
        'view-purchase-requisitions',
        'approve-purchase-requisition',
        'reject-purchase-requisition',
        'review-purchase-requisition',
        'view-purchase-requisition-reports',
        'view-purchase-requisition-history',
    ];

    foreach ($hodPermissions as $permission) {
        expect($hodRole->hasPermissionTo($permission))
            ->toBeTrue("HOD should have '{$permission}' permission");
    }

    // HOD should NOT have these permissions
    $restrictedPermissions = [
        'delete-purchase-requisition',
        'manage-purchase-approval-workflows',
        'convert-to-tender',
        'manage-purchase-requisitions',
    ];

    foreach ($restrictedPermissions as $permission) {
        expect($hodRole->hasPermissionTo($permission))
            ->toBeFalse("HOD should NOT have '{$permission}' permission");
    }
});

it('assigns correct permissions to finance manager role', function () {
    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert
    $financeManagerRole = Role::where('name', 'Finance Manager')->first();
    expect($financeManagerRole)->not()->toBeNull();

    $financeManagerPermissions = [
        'view-purchase-requisitions',
        'approve-purchase-requisition',
        'reject-purchase-requisition',
        'review-purchase-requisition',
        'amend-purchase-requisition',
        'cancel-purchase-requisition',
        'convert-to-tender',
        'view-purchase-requisition-reports',
        'export-purchase-requisition-reports',
        'view-purchase-requisition-analytics',
        'view-purchase-requisition-history',
        'view-purchase-requisition-audit-logs',
    ];

    foreach ($financeManagerPermissions as $permission) {
        expect($financeManagerRole->hasPermissionTo($permission))
            ->toBeTrue("Finance Manager should have '{$permission}' permission");
    }
});

it('updates existing permissions descriptions', function () {
    // Arrange - Create a permission with old description
    Permission::create([
        'name' => 'manage-purchase-requisitions',
        'description' => 'Old description'
    ]);

    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert
    $permission = Permission::where('name', 'manage-purchase-requisitions')->first();
    expect($permission->description)->toBe('Manage all aspects of purchase requisitions');
});

it('handles missing roles gracefully', function () {
    // Arrange - Delete a role
    Role::where('name', 'Employee')->delete();

    // Act & Assert - Should not throw an exception
    expect(fn() => $this->seed(PurchaseRequisitionPermissionsSeeder::class))
        ->not()->toThrow();

    // Verify other roles still get permissions
    $platformAdminRole = Role::where('name', 'Platform Admin')->first();
    expect($platformAdminRole->hasPermissionTo('manage-purchase-requisitions'))->toBeTrue();
});
