<?php

namespace Tests\Unit\Seeders;

use Database\Seeders\PurchaseRequisitionPermissionsSeeder;
use Database\Seeders\RoleAndPermissionSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

uses(TestCase::class, RefreshDatabase::class);

beforeEach(function () {
    // Seed basic roles first
    $this->seed(RoleAndPermissionSeeder::class);
});

it('creates all purchase requisition permissions', function () {
    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert - Check that all expected permissions exist
    $expectedPermissions = [
        'create-purchase-requisitions',
        'view-purchase-requisitions',
        'approve-purchase-requisitions',
        'manage-purchase-requisitions',
        'view-purchase-reports',
    ];

    foreach ($expectedPermissions as $permissionName) {
        expect(Permission::where('name', $permissionName)->exists())
            ->toBeTrue("Permission '{$permissionName}' should exist");
    }

    expect(Permission::whereIn('name', $expectedPermissions)->count())
        ->toBe(count($expectedPermissions));
});

it('assigns correct permissions to platform admin role', function () {
    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert
    $platformAdminRole = Role::where('name', 'Platform Admin')->first();
    expect($platformAdminRole)->not()->toBeNull();

    $criticalPermissions = [
        'create-purchase-requisitions',
        'view-purchase-requisitions',
        'approve-purchase-requisitions',
        'manage-purchase-requisitions',
        'view-purchase-reports',
    ];

    foreach ($criticalPermissions as $permission) {
        expect($platformAdminRole->hasPermissionTo($permission))
            ->toBeTrue("Platform Admin should have '{$permission}' permission");
    }
});

it('assigns correct permissions to employee role template', function () {
    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert - Check template role (organization_id = null)
    $employeeRoleTemplate = Role::where('name', 'Employee')->whereNull('organization_id')->first();
    expect($employeeRoleTemplate)->not()->toBeNull();

    $employeePermissions = [
        'create-purchase-requisitions',
        'view-purchase-requisitions',
    ];

    foreach ($employeePermissions as $permission) {
        expect($employeeRoleTemplate->hasPermissionTo($permission))
            ->toBeTrue("Employee template should have '{$permission}' permission");
    }

    // Employee should NOT have these permissions
    $restrictedPermissions = [
        'approve-purchase-requisitions',
        'manage-purchase-requisitions',
        'view-purchase-reports',
    ];

    foreach ($restrictedPermissions as $permission) {
        expect($employeeRoleTemplate->hasPermissionTo($permission))
            ->toBeFalse("Employee template should NOT have '{$permission}' permission");
    }
});

it('assigns correct permissions to hod role template', function () {
    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert - Check template role (organization_id = null)
    $hodRoleTemplate = Role::where('name', 'HOD')->whereNull('organization_id')->first();
    expect($hodRoleTemplate)->not()->toBeNull();

    $hodPermissions = [
        'view-purchase-requisitions',
        'approve-purchase-requisitions',
    ];

    foreach ($hodPermissions as $permission) {
        expect($hodRoleTemplate->hasPermissionTo($permission))
            ->toBeTrue("HOD template should have '{$permission}' permission");
    }

    // HOD should NOT have these permissions
    $restrictedPermissions = [
        'create-purchase-requisitions',
        'manage-purchase-requisitions',
        'view-purchase-reports',
    ];

    foreach ($restrictedPermissions as $permission) {
        expect($hodRoleTemplate->hasPermissionTo($permission))
            ->toBeFalse("HOD template should NOT have '{$permission}' permission");
    }
});

it('assigns correct permissions to finance manager role template', function () {
    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert - Check template role (organization_id = null)
    $financeManagerRoleTemplate = Role::where('name', 'Finance Manager')->whereNull('organization_id')->first();
    expect($financeManagerRoleTemplate)->not()->toBeNull();

    $financeManagerPermissions = [
        'view-purchase-requisitions',
        'approve-purchase-requisitions',
        'view-purchase-reports',
    ];

    foreach ($financeManagerPermissions as $permission) {
        expect($financeManagerRoleTemplate->hasPermissionTo($permission))
            ->toBeTrue("Finance Manager template should have '{$permission}' permission");
    }
});

it('updates existing permissions descriptions', function () {
    // Arrange - Create a permission with old description
    Permission::create([
        'name' => 'create-purchase-requisitions',
        'description' => 'Old description'
    ]);

    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert
    $permission = Permission::where('name', 'create-purchase-requisitions')->first();
    expect($permission->description)->toBe('Create and edit purchase requisitions');
});

it('handles missing roles gracefully', function () {
    // Arrange - Delete a role template
    Role::where('name', 'Employee')->whereNull('organization_id')->delete();

    // Act & Assert - Should not throw an exception
    expect(fn() => $this->seed(PurchaseRequisitionPermissionsSeeder::class))
        ->not()->toThrow();

    // Verify other roles still get permissions
    $platformAdminRole = Role::where('name', 'Platform Admin')->first();
    expect($platformAdminRole->hasPermissionTo('create-purchase-requisitions'))->toBeTrue();
});

it('updates existing organization-specific roles', function () {
    // Arrange - Create an organization-specific Employee role
    $organization = \App\Models\Organization::factory()->create();
    $orgEmployeeRole = Role::create([
        'name' => 'Employee',
        'organization_id' => $organization->id,
        'guard_name' => 'web'
    ]);

    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert
    $orgEmployeeRole->refresh();
    expect($orgEmployeeRole->hasPermissionTo('create-purchase-requisitions'))->toBeTrue();
    expect($orgEmployeeRole->hasPermissionTo('view-purchase-requisitions'))->toBeTrue();
    expect($orgEmployeeRole->hasPermissionTo('approve-purchase-requisitions'))->toBeFalse();
});
