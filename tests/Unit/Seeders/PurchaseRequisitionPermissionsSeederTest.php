<?php

namespace Tests\Unit\Seeders;

use Database\Seeders\PurchaseRequisitionPermissionsSeeder;
use Database\Seeders\RoleAndPermissionSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

uses(TestCase::class, RefreshDatabase::class);

beforeEach(function () {
    // Seed basic roles first
    $this->seed(RoleAndPermissionSeeder::class);
});

it('creates all purchase requisition permissions', function () {
    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert - Check that all expected permissions exist
    $expectedPermissions = [
        'create-purchase-requisitions',
        'view-purchase-requisitions',
        'approve-purchase-requisitions',
        'manage-purchase-requisitions',
        'view-purchase-reports',
    ];

    foreach ($expectedPermissions as $permissionName) {
        expect(Permission::where('name', $permissionName)->exists())
            ->toBeTrue("Permission '{$permissionName}' should exist");
    }

    expect(Permission::whereIn('name', $expectedPermissions)->count())
        ->toBe(count($expectedPermissions));
});

it('assigns correct permissions to platform admin role', function () {
    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert
    $platformAdminRole = Role::where('name', 'Platform Admin')->first();
    expect($platformAdminRole)->not()->toBeNull();

    $criticalPermissions = [
        'create-purchase-requisitions',
        'view-purchase-requisitions',
        'approve-purchase-requisitions',
        'manage-purchase-requisitions',
        'view-purchase-reports',
    ];

    foreach ($criticalPermissions as $permission) {
        expect($platformAdminRole->hasPermissionTo($permission))
            ->toBeTrue("Platform Admin should have '{$permission}' permission");
    }
});

it('assigns correct permissions to employee role', function () {
    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert
    $employeeRole = Role::where('name', 'Employee')->first();
    expect($employeeRole)->not()->toBeNull();

    $employeePermissions = [
        'create-purchase-requisitions',
        'view-purchase-requisitions',
    ];

    foreach ($employeePermissions as $permission) {
        expect($employeeRole->hasPermissionTo($permission))
            ->toBeTrue("Employee should have '{$permission}' permission");
    }

    // Employee should NOT have these permissions
    $restrictedPermissions = [
        'approve-purchase-requisitions',
        'manage-purchase-requisitions',
        'view-purchase-reports',
    ];

    foreach ($restrictedPermissions as $permission) {
        expect($employeeRole->hasPermissionTo($permission))
            ->toBeFalse("Employee should NOT have '{$permission}' permission");
    }
});

it('assigns correct permissions to hod role', function () {
    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert
    $hodRole = Role::where('name', 'HOD')->first();
    expect($hodRole)->not()->toBeNull();

    $hodPermissions = [
        'view-purchase-requisitions',
        'approve-purchase-requisitions',
    ];

    foreach ($hodPermissions as $permission) {
        expect($hodRole->hasPermissionTo($permission))
            ->toBeTrue("HOD should have '{$permission}' permission");
    }

    // HOD should NOT have these permissions
    $restrictedPermissions = [
        'create-purchase-requisitions',
        'manage-purchase-requisitions',
        'view-purchase-reports',
    ];

    foreach ($restrictedPermissions as $permission) {
        expect($hodRole->hasPermissionTo($permission))
            ->toBeFalse("HOD should NOT have '{$permission}' permission");
    }
});

it('assigns correct permissions to finance manager role', function () {
    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert
    $financeManagerRole = Role::where('name', 'Finance Manager')->first();
    expect($financeManagerRole)->not()->toBeNull();

    $financeManagerPermissions = [
        'view-purchase-requisitions',
        'approve-purchase-requisitions',
        'view-purchase-reports',
    ];

    foreach ($financeManagerPermissions as $permission) {
        expect($financeManagerRole->hasPermissionTo($permission))
            ->toBeTrue("Finance Manager should have '{$permission}' permission");
    }
});

it('updates existing permissions descriptions', function () {
    // Arrange - Create a permission with old description
    Permission::create([
        'name' => 'create-purchase-requisitions',
        'description' => 'Old description'
    ]);

    // Act
    $this->seed(PurchaseRequisitionPermissionsSeeder::class);

    // Assert
    $permission = Permission::where('name', 'create-purchase-requisitions')->first();
    expect($permission->description)->toBe('Create and edit purchase requisitions');
});

it('handles missing roles gracefully', function () {
    // Arrange - Delete a role
    Role::where('name', 'Employee')->delete();

    // Act & Assert - Should not throw an exception
    expect(fn() => $this->seed(PurchaseRequisitionPermissionsSeeder::class))
        ->not()->toThrow();

    // Verify other roles still get permissions
    $platformAdminRole = Role::where('name', 'Platform Admin')->first();
    expect($platformAdminRole->hasPermissionTo('create-purchase-requisitions'))->toBeTrue();
});
