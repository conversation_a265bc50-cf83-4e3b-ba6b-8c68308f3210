<?php

namespace Tests\Unit\Models;

use App\Models\InventoryItem;
use App\Models\PurchaseRequisition;
use App\Models\PurchaseRequisitionItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

uses(TestCase::class, RefreshDatabase::class);

it('can be created and associated with a purchase requisition', function () {
    // Arrange
    $purchaseRequisition = PurchaseRequisition::factory()->create();

    // Act
    $item = PurchaseRequisitionItem::create([
        'purchase_requisition_id' => $purchaseRequisition->id,
        'item_name' => 'Office Chair',
        'description' => 'Ergonomic office chair with lumbar support',
        'specifications' => 'Height adjustable, 5-year warranty',
        'quantity' => 5,
        'unit_of_measure' => 'pieces',
        'estimated_unit_price' => 250.00,
        'urgency_level' => 'medium',
    ]);

    // Assert
    expect($item)->toBeInstanceOf(PurchaseRequisitionItem::class);
    expect($item->item_name)->toBe('Office Chair');
    expect($item->description)->toBe('Ergonomic office chair with lumbar support');
    expect($item->specifications)->toBe('Height adjustable, 5-year warranty');
    expect($item->quantity)->toBe('5.00');
    expect($item->unit_of_measure)->toBe('pieces');
    expect($item->estimated_unit_price)->toBe('250.00');
    expect($item->estimated_total_price)->toBe('1250.00');
    expect($item->urgency_level)->toBe('medium');

    // Assert Relationships
    expect($item->purchaseRequisition)->toBeInstanceOf(PurchaseRequisition::class);
    expect($item->purchaseRequisition->id)->toBe($purchaseRequisition->id);

    // Assert Database
    $this->assertDatabaseHas('purchase_requisition_items', [
        'id' => $item->id,
        'purchase_requisition_id' => $purchaseRequisition->id,
        'item_name' => 'Office Chair',
        'quantity' => 5.00,
        'estimated_unit_price' => 250.00,
        'estimated_total_price' => 1250.00,
        'urgency_level' => 'medium',
    ]);
});

it('automatically calculates total price on saving', function () {
    // Arrange
    $purchaseRequisition = PurchaseRequisition::factory()->create();

    $item = new PurchaseRequisitionItem([
        'purchase_requisition_id' => $purchaseRequisition->id,
        'item_name' => 'Laptop',
        'description' => 'Business laptop',
        'quantity' => 3,
        'unit_of_measure' => 'pieces',
        'estimated_unit_price' => 1200.00,
        'urgency_level' => 'high',
    ]);

    // Act
    $item->save();

    // Assert
    expect($item->estimated_total_price)->toBe('3600.00');
    $this->assertDatabaseHas('purchase_requisition_items', [
        'id' => $item->id,
        'estimated_total_price' => 3600.00,
    ]);

    // Act: Update quantity
    $item->quantity = 5;
    $item->save();

    // Assert
    expect($item->estimated_total_price)->toBe('6000.00');
    $this->assertDatabaseHas('purchase_requisition_items', [
        'id' => $item->id,
        'estimated_total_price' => 6000.00,
    ]);

    // Act: Update unit price
    $item->estimated_unit_price = 1500.00;
    $item->save();

    // Assert
    expect($item->estimated_total_price)->toBe('7500.00');
    $this->assertDatabaseHas('purchase_requisition_items', [
        'id' => $item->id,
        'estimated_total_price' => 7500.00,
    ]);
});

it('can be associated with an inventory item', function () {
    // Arrange
    $purchaseRequisition = PurchaseRequisition::factory()->create();
    $inventoryItem = InventoryItem::factory()->create();

    // Act
    $item = PurchaseRequisitionItem::create([
        'purchase_requisition_id' => $purchaseRequisition->id,
        'inventory_item_id' => $inventoryItem->id,
        'item_name' => 'Printer Paper',
        'description' => 'A4 white paper',
        'quantity' => 10,
        'unit_of_measure' => 'reams',
        'estimated_unit_price' => 15.00,
        'urgency_level' => 'low',
    ]);

    // Assert
    expect($item->inventoryItem)->toBeInstanceOf(InventoryItem::class);
    expect($item->inventoryItem->id)->toBe($inventoryItem->id);
});

it('updates purchase requisition total when saved', function () {
    // Arrange
    $purchaseRequisition = PurchaseRequisition::factory()->create([
        'estimated_total_amount' => 0
    ]);

    // Act: Create first item
    $item1 = PurchaseRequisitionItem::create([
        'purchase_requisition_id' => $purchaseRequisition->id,
        'item_name' => 'Item 1',
        'description' => 'First item',
        'quantity' => 2,
        'unit_of_measure' => 'pieces',
        'estimated_unit_price' => 100.00,
        'urgency_level' => 'medium',
    ]);

    // Assert
    expect($purchaseRequisition->fresh()->estimated_total_amount)->toBe('200.00');

    // Act: Create second item
    $item2 = PurchaseRequisitionItem::create([
        'purchase_requisition_id' => $purchaseRequisition->id,
        'item_name' => 'Item 2',
        'description' => 'Second item',
        'quantity' => 3,
        'unit_of_measure' => 'pieces',
        'estimated_unit_price' => 150.00,
        'urgency_level' => 'high',
    ]);

    // Assert
    expect($purchaseRequisition->fresh()->estimated_total_amount)->toBe('650.00');
});

it('updates purchase requisition total when deleted', function () {
    // Arrange
    $purchaseRequisition = PurchaseRequisition::factory()->create([
        'estimated_total_amount' => 0
    ]);

    $item1 = PurchaseRequisitionItem::create([
        'purchase_requisition_id' => $purchaseRequisition->id,
        'item_name' => 'Item 1',
        'description' => 'First item',
        'quantity' => 2,
        'unit_of_measure' => 'pieces',
        'estimated_unit_price' => 100.00,
        'urgency_level' => 'medium',
    ]);

    $item2 = PurchaseRequisitionItem::create([
        'purchase_requisition_id' => $purchaseRequisition->id,
        'item_name' => 'Item 2',
        'description' => 'Second item',
        'quantity' => 3,
        'unit_of_measure' => 'pieces',
        'estimated_unit_price' => 150.00,
        'urgency_level' => 'high',
    ]);

    expect($purchaseRequisition->fresh()->estimated_total_amount)->toBe('650.00');

    // Act: Delete one item
    $item1->delete();

    // Assert
    expect($purchaseRequisition->fresh()->estimated_total_amount)->toBe('450.00');
});

it('has correct urgency scopes', function () {
    $purchaseRequisition = PurchaseRequisition::factory()->create();

    PurchaseRequisitionItem::factory()->create([
        'purchase_requisition_id' => $purchaseRequisition->id,
        'urgency_level' => 'low'
    ]);
    PurchaseRequisitionItem::factory()->create([
        'purchase_requisition_id' => $purchaseRequisition->id,
        'urgency_level' => 'medium'
    ]);
    PurchaseRequisitionItem::factory()->create([
        'purchase_requisition_id' => $purchaseRequisition->id,
        'urgency_level' => 'high'
    ]);
    PurchaseRequisitionItem::factory()->create([
        'purchase_requisition_id' => $purchaseRequisition->id,
        'urgency_level' => 'critical'
    ]);

    expect(PurchaseRequisitionItem::byUrgency('low')->count())->toBe(1);
    expect(PurchaseRequisitionItem::highPriority()->count())->toBe(2);
    expect(PurchaseRequisitionItem::critical()->count())->toBe(1);
});

it('has correct label accessors', function () {
    $item = PurchaseRequisitionItem::factory()->create([
        'urgency_level' => 'critical',
        'quantity' => 5.50,
        'estimated_unit_price' => 123.45,
        'estimated_total_price' => 678.975
    ]);

    expect($item->urgency_label)->toBe('Critical');
    expect($item->urgency_color)->toBe('red');
    expect($item->formatted_quantity)->toBe('5.50');
    expect($item->formatted_estimated_unit_price)->toBe('123.45');
    expect($item->formatted_estimated_total_price)->toBe('678.98');
});

it('can calculate total price manually', function () {
    $item = PurchaseRequisitionItem::factory()->make([
        'quantity' => 4,
        'estimated_unit_price' => 25.75
    ]);

    $total = $item->calculateTotalPrice();

    expect($total)->toBe(103.00);
});
