<?php

namespace Tests\Unit\Models;

use App\Models\ApprovalWorkflow;
use App\Models\ApprovalWorkflowStep;
use App\Models\Branch;
use App\Models\Department;
use App\Models\Organization;
use App\Models\PurchaseRequisition;
use App\Models\PurchaseRequisitionItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

uses(TestCase::class, RefreshDatabase::class);

it('can be created with all required fields', function () {
    // Arrange
    $organization = Organization::factory()->create();
    $branch = Branch::factory()->create(['organization_id' => $organization->id]);
    $department = Department::factory()->create([
        'organization_id' => $organization->id,
        'branch_id' => $branch->id
    ]);
    $user = User::factory()->create();

    // Act
    $purchaseRequisition = PurchaseRequisition::create([
        'organization_id' => $organization->id,
        'branch_id' => $branch->id,
        'department_id' => $department->id,
        'requester_user_id' => $user->id,
        'purpose' => 'Office supplies for Q1',
        'required_by_date' => '2025-09-01',
        'estimated_total_amount' => 5000.00,
        'status' => 'draft',
        'requisition_type' => 'operational',
    ]);

    // Assert
    expect($purchaseRequisition)->toBeInstanceOf(PurchaseRequisition::class);
    expect($purchaseRequisition->purpose)->toBe('Office supplies for Q1');
    expect($purchaseRequisition->status)->toBe('draft');
    expect($purchaseRequisition->requisition_type)->toBe('operational');
    expect($purchaseRequisition->estimated_total_amount)->toBe('5000.00');
    expect($purchaseRequisition->required_by_date->format('Y-m-d'))->toBe('2025-09-01');

    // Assert Database
    $this->assertDatabaseHas('purchase_requisitions', [
        'id' => $purchaseRequisition->id,
        'organization_id' => $organization->id,
        'branch_id' => $branch->id,
        'department_id' => $department->id,
        'requester_user_id' => $user->id,
        'purpose' => 'Office supplies for Q1',
        'status' => 'draft',
    ]);
});

it('automatically generates requisition number on creation', function () {
    // Arrange
    $purchaseRequisition = PurchaseRequisition::factory()->make([
        'requisition_number' => null
    ]);

    // Act
    $purchaseRequisition->save();

    // Assert
    expect($purchaseRequisition->requisition_number)->not()->toBeNull();
    expect($purchaseRequisition->requisition_number)->toMatch('/^PR\d{8}$/');
});

it('has correct relationships', function () {
    // Arrange
    $organization = Organization::factory()->create();
    $branch = Branch::factory()->create(['organization_id' => $organization->id]);
    $department = Department::factory()->create([
        'organization_id' => $organization->id,
        'branch_id' => $branch->id
    ]);
    $user = User::factory()->create();
    $approvalWorkflow = ApprovalWorkflow::factory()->create([
        'organization_id' => $organization->id
    ]);
    $approvalStep = ApprovalWorkflowStep::factory()->create([
        'approval_workflow_id' => $approvalWorkflow->id
    ]);

    $purchaseRequisition = PurchaseRequisition::factory()->create([
        'organization_id' => $organization->id,
        'branch_id' => $branch->id,
        'department_id' => $department->id,
        'requester_user_id' => $user->id,
        'approval_workflow_id' => $approvalWorkflow->id,
        'current_approval_step_id' => $approvalStep->id,
    ]);

    // Assert Relationships
    expect($purchaseRequisition->organization)->toBeInstanceOf(Organization::class);
    expect($purchaseRequisition->branch)->toBeInstanceOf(Branch::class);
    expect($purchaseRequisition->department)->toBeInstanceOf(Department::class);
    expect($purchaseRequisition->requester)->toBeInstanceOf(User::class);
    expect($purchaseRequisition->approvalWorkflow)->toBeInstanceOf(ApprovalWorkflow::class);
    expect($purchaseRequisition->currentApprovalStep)->toBeInstanceOf(ApprovalWorkflowStep::class);
});

it('can have multiple items', function () {
    // Arrange
    $purchaseRequisition = PurchaseRequisition::factory()->create();
    
    // Act
    $item1 = PurchaseRequisitionItem::factory()->create([
        'purchase_requisition_id' => $purchaseRequisition->id,
        'estimated_total_price' => 1000.00
    ]);
    $item2 = PurchaseRequisitionItem::factory()->create([
        'purchase_requisition_id' => $purchaseRequisition->id,
        'estimated_total_price' => 2000.00
    ]);

    // Assert
    expect($purchaseRequisition->items)->toHaveCount(2);
    expect($purchaseRequisition->items->first())->toBeInstanceOf(PurchaseRequisitionItem::class);
});

it('calculates total amount from items', function () {
    // Arrange
    $purchaseRequisition = PurchaseRequisition::factory()->create([
        'estimated_total_amount' => 0
    ]);
    
    PurchaseRequisitionItem::factory()->create([
        'purchase_requisition_id' => $purchaseRequisition->id,
        'estimated_total_price' => 1500.00
    ]);
    PurchaseRequisitionItem::factory()->create([
        'purchase_requisition_id' => $purchaseRequisition->id,
        'estimated_total_price' => 2500.00
    ]);

    // Act
    $total = $purchaseRequisition->calculateTotalAmount();

    // Assert
    expect($total)->toBe('4000.00');
    expect($purchaseRequisition->fresh()->estimated_total_amount)->toBe('4000.00');
});

it('has correct status methods', function () {
    // Test canBeEdited
    $draftRequisition = PurchaseRequisition::factory()->draft()->create();
    $approvedRequisition = PurchaseRequisition::factory()->approved()->create();
    
    expect($draftRequisition->canBeEdited())->toBeTrue();
    expect($approvedRequisition->canBeEdited())->toBeFalse();

    // Test canBeSubmitted
    $draftWithItems = PurchaseRequisition::factory()->draft()->create();
    PurchaseRequisitionItem::factory()->create(['purchase_requisition_id' => $draftWithItems->id]);
    
    $draftWithoutItems = PurchaseRequisition::factory()->draft()->create();
    
    expect($draftWithItems->canBeSubmitted())->toBeTrue();
    expect($draftWithoutItems->canBeSubmitted())->toBeFalse();

    // Test canBeApproved
    $pendingRequisition = PurchaseRequisition::factory()->pendingApproval()->create();
    
    expect($pendingRequisition->canBeApproved())->toBeTrue();
    expect($draftRequisition->canBeApproved())->toBeFalse();

    // Test canBeCancelled
    expect($draftRequisition->canBeCancelled())->toBeTrue();
    expect($pendingRequisition->canBeCancelled())->toBeTrue();
    expect($approvedRequisition->canBeCancelled())->toBeFalse();
});

it('has correct label accessors', function () {
    $purchaseRequisition = PurchaseRequisition::factory()->create([
        'status' => 'pending_approval',
        'requisition_type' => 'capital_expenditure'
    ]);

    expect($purchaseRequisition->status_label)->toBe('Pending Approval');
    expect($purchaseRequisition->requisition_type_label)->toBe('Capital Expenditure');
});

it('has correct scopes', function () {
    $organization = Organization::factory()->create();
    $branch = Branch::factory()->create(['organization_id' => $organization->id]);
    $department = Department::factory()->create([
        'organization_id' => $organization->id,
        'branch_id' => $branch->id
    ]);
    $user = User::factory()->create();

    PurchaseRequisition::factory()->create([
        'organization_id' => $organization->id,
        'branch_id' => $branch->id,
        'department_id' => $department->id,
        'requester_user_id' => $user->id,
        'status' => 'draft'
    ]);
    PurchaseRequisition::factory()->create([
        'organization_id' => $organization->id,
        'status' => 'approved'
    ]);
    PurchaseRequisition::factory()->create(['status' => 'pending_approval']);

    expect(PurchaseRequisition::byOrganization($organization->id)->count())->toBe(2);
    expect(PurchaseRequisition::byBranch($branch->id)->count())->toBe(1);
    expect(PurchaseRequisition::byDepartment($department->id)->count())->toBe(1);
    expect(PurchaseRequisition::byRequester($user->id)->count())->toBe(1);
    expect(PurchaseRequisition::byStatus('draft')->count())->toBe(1);
    expect(PurchaseRequisition::draft()->count())->toBe(1);
    expect(PurchaseRequisition::approved()->count())->toBe(1);
    expect(PurchaseRequisition::pending()->count())->toBe(1);
});
