<?php

namespace Tests\Unit\Models;

use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\StoreRequisitionItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

uses(TestCase::class, RefreshDatabase::class);

beforeEach(function () {
    $this->organization = Organization::factory()->create();
    $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
    $this->department = Department::factory()->create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id
    ]);
    
    $this->user = User::factory()->create();
    $this->user->organizations()->attach($this->organization->id);
    
    $this->inventoryItem = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'TEST-ITEM-001',
        'name' => 'Test Item',
        'description' => 'Test inventory item',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 100,
        'reorder_level' => 10,
    ]);
    
    $this->storeRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Test store requisition',
        'status' => StoreRequisition::STATUS_DRAFT,
    ]);
});

it('can be created with required fields', function () {
    $item = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 5,
        'quantity_issued' => 0, // Explicitly set default
    ]);

    expect($item)->toBeInstanceOf(StoreRequisitionItem::class);
    expect($item->store_requisition_id)->toBe($this->storeRequisition->id);
    expect($item->inventory_item_id)->toBe($this->inventoryItem->id);
    expect($item->quantity_requested)->toBe('5.00'); // Decimal cast returns string
    expect($item->quantity_issued)->toBe('0.00'); // Default value as string
});

it('has correct relationships', function () {
    $item = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 3,
    ]);
    
    expect($item->storeRequisition)->toBeInstanceOf(StoreRequisition::class);
    expect($item->inventoryItem)->toBeInstanceOf(InventoryItem::class);
    expect($item->storeRequisition->id)->toBe($this->storeRequisition->id);
    expect($item->inventoryItem->id)->toBe($this->inventoryItem->id);
});

it('calculates remaining quantity correctly', function () {
    $item = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 10,
        'quantity_issued' => 6,
    ]);
    
    $remainingQuantity = $item->quantity_requested - $item->quantity_issued;
    expect($remainingQuantity)->toBe(4.0);
});

it('tracks issued quantities correctly', function () {
    $item = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 8,
        'quantity_issued' => 0, // Explicitly set
    ]);

    // Initially no quantity issued
    expect($item->quantity_issued)->toBe('0.00');

    // Update issued quantity
    $item->update(['quantity_issued' => 5]);
    expect($item->quantity_issued)->toBe('5.00');
    
    // Verify database
    $this->assertDatabaseHas('store_requisition_items', [
        'id' => $item->id,
        'quantity_issued' => 5.0,
    ]);
});

it('allows zero and positive quantities', function () {
    // Zero quantity is allowed (no database constraint)
    $item = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 0,
    ]);
    expect($item->quantity_requested)->toBe('0.00');

    // Positive quantities work normally
    $item2 = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 5,
    ]);
    expect($item2->quantity_requested)->toBe('5.00');
});

it('correctly identifies if item is fully issued', function () {
    $item = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 10,
        'quantity_issued' => 10,
    ]);
    
    $isFullyIssued = $item->quantity_issued >= $item->quantity_requested;
    expect($isFullyIssued)->toBeTrue();
});

it('correctly identifies if item is partially issued', function () {
    $item = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 10,
        'quantity_issued' => 6,
    ]);
    
    $isPartiallyIssued = $item->quantity_issued > 0 && $item->quantity_issued < $item->quantity_requested;
    expect($isPartiallyIssued)->toBeTrue();
});

it('correctly identifies if item is not issued', function () {
    $item = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 10,
        'quantity_issued' => 0,
    ]);
    
    $isNotIssued = $item->quantity_issued == 0;
    expect($isNotIssued)->toBeTrue();
});

it('handles decimal quantities correctly', function () {
    $item = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 2.5,
        'quantity_issued' => 1.25,
    ]);
    
    expect($item->quantity_requested)->toBe('2.50');
    expect($item->quantity_issued)->toBe('1.25');

    $remaining = (float)$item->quantity_requested - (float)$item->quantity_issued;
    expect($remaining)->toBe(1.25);
});

it('maintains referential integrity with store requisition', function () {
    $item = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 5,
    ]);
    
    // Force deleting the store requisition should cascade delete the item
    $this->storeRequisition->forceDelete();
    
    $this->assertDatabaseMissing('store_requisition_items', [
        'id' => $item->id,
    ]);
});

it('maintains referential integrity with inventory item', function () {
    $item = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 5,
    ]);

    // With cascadeOnDelete, deleting inventory item will delete the item
    $this->inventoryItem->delete();

    // Verify the item was also deleted
    $this->assertDatabaseMissing('store_requisition_items', [
        'id' => $item->id,
    ]);
});

it('can be updated with new quantities', function () {
    $item = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 5,
    ]);
    
    // Update requested quantity
    $item->update(['quantity_requested' => 8]);
    expect($item->quantity_requested)->toBe('8.00');

    // Update issued quantity
    $item->update(['quantity_issued' => 3]);
    expect($item->quantity_issued)->toBe('3.00');
    
    // Verify in database
    $this->assertDatabaseHas('store_requisition_items', [
        'id' => $item->id,
        'quantity_requested' => '8.00',
        'quantity_issued' => '3.00',
    ]);
});

it('stores timestamps correctly', function () {
    $item = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 5,
    ]);
    
    expect($item->created_at)->not->toBeNull();
    expect($item->updated_at)->not->toBeNull();
    expect($item->created_at)->toBeInstanceOf(\Illuminate\Support\Carbon::class);
    expect($item->updated_at)->toBeInstanceOf(\Illuminate\Support\Carbon::class);
});

it('can have multiple items for the same requisition', function () {
    $secondInventoryItem = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'TEST-ITEM-002',
        'name' => 'Test Item 2',
        'description' => 'Second test inventory item',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 50,
        'reorder_level' => 5,
    ]);
    
    $item1 = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 5,
    ]);
    
    $item2 = StoreRequisitionItem::create([
        'store_requisition_id' => $this->storeRequisition->id,
        'inventory_item_id' => $secondInventoryItem->id,
        'quantity_requested' => 3,
    ]);
    
    $this->storeRequisition->refresh();
    expect($this->storeRequisition->items->count())->toBe(2);
    expect($this->storeRequisition->items->pluck('id'))->toContain($item1->id, $item2->id);
});
