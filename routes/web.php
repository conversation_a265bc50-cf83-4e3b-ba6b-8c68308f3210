<?php

use App\Http\Controllers\AppController;
use App\Http\Controllers\AttachmentController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DisbursementController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\OrganizationController;
use App\Http\Controllers\OrganizationSetupController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\DepartmentController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\ApprovalWorkflowController;
use App\Http\Controllers\RequisitionController;
use App\Http\Controllers\TransactionController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\ChartOfAccountController;
use App\Http\Controllers\CashFloatController;
use App\Http\Controllers\StoreRequisitionController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\SupplierController;

Route::get('/', function () {
    return Inertia::render('homepage');
})->name('homepage');

Route::get('/support', function () {
    return Inertia::render('support');
})->name('support');

Route::get('/privacy-policy', function () {
    return Inertia::render('privacy-policy');
})->name('privacy-policy');

Route::get('/terms-of-service', function () {
    return Inertia::render('terms-of-service');
})->name('terms-of-service');

// Unified login routes (handles both users and suppliers)
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthenticatedSessionController::class, 'create'])
        ->name('login');

    Route::post('/login', [AuthenticatedSessionController::class, 'store']);
});

Route::get('/register', [RegisteredUserController::class, 'create'])
    ->name('register');

Route::post('/register', [RegisteredUserController::class, 'store']);

Route::get('/supplier-register', function () {
    return Inertia::render('auth/supplier-register');
})->name('supplier-register');

Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard routes
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/department/{department}', [DashboardController::class, 'switchDepartment'])->name('dashboard.department');


    // API routes
    Route::get('/api/pending-approvals-count', [AppController::class, 'getPendingApprovalsCount'])->name('api.pending-approvals-count');
    Route::get('/api/requisition-status-counts', [AppController::class, 'getRequisitionStatusCounts'])->name('api.requisition-status-counts');

    // Platform Admin routes
    Route::resource('organizations', OrganizationController::class);

    // Organization Setup Wizard
    Route::get('/organization-setup', [OrganizationSetupController::class, 'index'])->name('organization-setup.index');
    Route::post('/organization-setup', [OrganizationSetupController::class, 'store'])->name('organization-setup.store');

    // Organization Admin routes
    Route::resource('branches', BranchController::class);
    Route::resource('departments', DepartmentController::class);
    Route::resource('users', UserController::class);
    Route::resource('roles', RoleController::class);

    // Requisition routes - Note: Order matters for routes with similar patterns
    // Special routes first
    Route::get('/requisitions/approvals', [RequisitionController::class, 'approvals'])->name('requisitions.approvals');
    Route::get('/requisitions/my-approvals', [RequisitionController::class, 'myApprovals'])->name('requisitions.my-approvals');
    Route::get('/requisitions/my-approval-history', [RequisitionController::class, 'myApprovalHistory'])->name('requisitions.my-approval-history');
    Route::get('/requisitions/history', [RequisitionController::class, 'history'])->name('requisitions.history');
    Route::get('/requisitions/create/{department?}', [RequisitionController::class, 'create'])->name('requisitions.create.department');

    // Standard CRUD routes
    Route::get('/requisitions', [RequisitionController::class, 'create'])->name('requisitions.create');
    Route::post('/requisitions', [RequisitionController::class, 'store'])->name('requisitions.store');
    Route::get('/requisitions/{requisition}', [RequisitionController::class, 'show'])->name('requisitions.show');
    Route::get('/requisitions/{requisition}/attachment-ui-state', [RequisitionController::class, 'getAttachmentUIState'])->name('requisitions.attachment-ui-state');

    // Approval actions
    Route::post('/requisitions/{requisition}/approve', [RequisitionController::class, 'approve'])->name('requisitions.approve');
    Route::post('/requisitions/{requisition}/reject', [RequisitionController::class, 'reject'])->name('requisitions.reject');
    Route::post('/requisitions/{requisition}/return-for-revision', [RequisitionController::class, 'returnForRevision'])->name('requisitions.return-for-revision');
    Route::post('/requisitions/{requisition}/resubmit', [RequisitionController::class, 'resubmit'])->name('requisitions.resubmit');

    // Rejected requisition editing
    Route::get('/requisitions/{requisition}/edit-rejected', [RequisitionController::class, 'editRejected'])->name('requisitions.edit-rejected');
    Route::post('/requisitions/{requisition}/update-rejected', [RequisitionController::class, 'updateRejected'])->name('requisitions.update-rejected');

    // Approval Workflow routes - Define routes explicitly to avoid model binding issues
    Route::get('approval-workflows', [ApprovalWorkflowController::class, 'index'])->name('approval-workflows.index');
    Route::get('approval-workflows/create', [ApprovalWorkflowController::class, 'create'])->name('approval-workflows.create');
    Route::post('approval-workflows', [ApprovalWorkflowController::class, 'store'])->name('approval-workflows.store');
    Route::get('approval-workflows/{id}', [ApprovalWorkflowController::class, 'show'])->name('approval-workflows.show');
    Route::get('approval-workflows/{id}/edit', [ApprovalWorkflowController::class, 'edit'])->name('approval-workflows.edit');
    Route::put('approval-workflows/{id}', [ApprovalWorkflowController::class, 'update'])->name('approval-workflows.update');
    Route::delete('approval-workflows/{id}', [ApprovalWorkflowController::class, 'destroy'])->name('approval-workflows.destroy');

    // Workflow Template routes
    Route::get('workflow-templates', [App\Http\Controllers\WorkflowTemplateController::class, 'index'])->name('workflow-templates.index');
    Route::get('workflow-templates/categories', [App\Http\Controllers\WorkflowTemplateController::class, 'categories'])->name('workflow-templates.categories');
    Route::get('workflow-templates/{template}/preview', [App\Http\Controllers\WorkflowTemplateController::class, 'preview'])->name('workflow-templates.preview');
    Route::post('workflow-templates/{template}/create', [App\Http\Controllers\WorkflowTemplateController::class, 'createFromTemplate'])->name('workflow-templates.create-from-template');
    Route::get('workflow-templates/{template}', [App\Http\Controllers\WorkflowTemplateController::class, 'show'])->name('workflow-templates.show');

    // Disbursement routes
    Route::get('disbursement', [DisbursementController::class, 'index'])->name('disbursement.index');
    Route::get('disbursement/{id}', [DisbursementController::class, 'show'])->name('disbursement.show');
    Route::get('disbursement/{id}/edit', [DisbursementController::class, 'edit'])->name('disbursement.edit');
    Route::put('disbursement/{id}', [DisbursementController::class, 'update'])->name('disbursement.update');

    // Transaction routes
    Route::get('transactions', [TransactionController::class, 'index'])->name('transactions.index');
    Route::get('transactions/{id}', [TransactionController::class, 'show'])->name('transactions.show');
    Route::get('transactions/{id}/edit', [TransactionController::class, 'edit'])->name('transactions.edit');
    Route::put('transactions/{id}', [TransactionController::class, 'update'])->name('transactions.update');
    Route::post('transactions/{id}/complete', [TransactionController::class, 'complete'])->name('transactions.complete');
    Route::post('/transactions/{id}/approve', [TransactionController::class, 'approve'])->name('transactions.approve');

    // Define routes for Chart of Accounts
    Route::get('chart-of-accounts', [ChartOfAccountController::class, 'index'])->name('chart-of-accounts.index');
    Route::get('chart-of-accounts/create', [ChartOfAccountController::class, 'create'])->name('chart-of-accounts.create');
    Route::post('chart-of-accounts', [ChartOfAccountController::class, 'store'])->name('chart-of-accounts.store');
    Route::get('chart-of-accounts/{id}', [ChartOfAccountController::class, 'show'])->name('chart-of-accounts.show');
    Route::get('chart-of-accounts/{id}/edit', [ChartOfAccountController::class, 'edit'])->name('chart-of-accounts.edit');
    Route::put('chart-of-accounts/{id}', [ChartOfAccountController::class, 'update'])->name('chart-of-accounts.update');
    Route::delete('chart-of-accounts/{id}', [ChartOfAccountController::class, 'destroy'])->name('chart-of-accounts.destroy');

    // Notification routes
    Route::get('notifications', [NotificationController::class, 'index'])->name('notifications.index');
    Route::post('notifications/{id}/mark-as-read', [NotificationController::class, 'markAsRead'])->where('id', '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}')->name('notifications.mark-as-read');
    Route::post('notifications/{id}/mark-as-read-ajax', [NotificationController::class, 'markAsReadAjax'])->where('id', '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}')->name('notifications.mark-as-read-ajax');
    Route::post('notifications/mark-all-as-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-as-read');
    Route::get('notifications/unread-count', [NotificationController::class, 'getUnreadCount'])->name('notifications.unread-count');
    Route::delete('notifications/{id}', [NotificationController::class, 'destroy'])->where('id', '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}')->name('notifications.destroy');
    Route::delete('notifications/delete-all', [NotificationController::class, 'deleteAll'])->name('notifications.delete-all');
    Route::get('notifications/latest', [NotificationController::class, 'getLatest'])->name('notifications.latest');

    // Cash Float Routes
    Route::get('/cash-floats', [CashFloatController::class, 'index'])->name('cash-floats.index');
    Route::get('/cash-floats/create', [CashFloatController::class, 'create'])->name('cash-floats.create');
    Route::post('/cash-floats', [CashFloatController::class, 'store'])->name('cash-floats.store');
    Route::get('/cash-floats/{cashFloat}', [CashFloatController::class, 'show'])->name('cash-floats.show');
    Route::get('/cash-floats/{cashFloat}/edit', [CashFloatController::class, 'edit'])->name('cash-floats.edit');
    Route::put('/cash-floats/{cashFloat}', [CashFloatController::class, 'update'])->name('cash-floats.update');
    Route::delete('/cash-floats/{cashFloat}', [CashFloatController::class, 'destroy'])->name('cash-floats.destroy');
    Route::post('/cash-floats/{cashFloat}/transactions', [CashFloatController::class, 'storeTransaction'])->name('cash-floats.transactions.store');
    Route::get('/cash-floats/{cashFloat}/export-pdf', [CashFloatController::class, 'exportPDF'])->name('cash-floats.export-pdf');
    Route::get('/cash-floats/{cashFloat}/debug-accounts', [CashFloatController::class, 'debugChartOfAccounts'])
        ->middleware('can:debug-cash-floats')
        ->name('cash-floats.debug-accounts');

    // Store Requisition routes
    Route::prefix('store-requisitions')->name('store-requisitions.')->group(function () {

    Route::post('store-requisitions/{store_requisition}/submit', [StoreRequisitionController::class, 'submit'])
        ->name('store-requisitions.submit');
    Route::post('store-requisitions/{store_requisition}/approve', [StoreRequisitionController::class, 'approve'])
        ->name('store-requisitions.approve');
    Route::post('store-requisitions/{store_requisition}/reject', [StoreRequisitionController::class, 'reject'])
        ->name('store-requisitions.reject');
    Route::post('store-requisitions/{store_requisition}/issue', [StoreRequisitionController::class, 'issue'])
        ->name('store-requisitions.issue');

        // Management views (must come before parameterized routes)
        // Route::get('/store-requisitions/{store_requisition}', [StoreRequisitionController::class, 'show'])->name('store-requisitions.show');
        // Route::get('/store-requisitions/{storeRequisition}', [StoreRequisitionController::class, 'show']);
        // Route::get('/store-requisitions/{storeRequisition}', [StoreRequisitionController::class, 'show']);
        Route::get('pending-items', [StoreRequisitionController::class, 'pendingItems'])->name('pending-items');
        Route::get('approvals', [StoreRequisitionController::class, 'approvals'])
            ->name('approvals')
            ->middleware('permission:approve-store-requisition|store-keep');

        // Standard CRUD operations
        Route::get('/', [StoreRequisitionController::class, 'index'])->name('index');
        Route::get('create', [StoreRequisitionController::class, 'create'])->name('create');
        Route::post('/', [StoreRequisitionController::class, 'store'])->name('store');

        // Issue management (must come before parameterized routes)
        Route::get('issue', [StoreRequisitionController::class, 'issueIndex'])->name('issue.index');

        Route::get('{storeRequisition}', [StoreRequisitionController::class, 'show'])->name('show');
        
        // Edit operations
        Route::get('{storeRequisition}/edit', [StoreRequisitionController::class, 'edit'])->name('edit');
        Route::put('{storeRequisition}', [StoreRequisitionController::class, 'update'])->name('update');
        Route::get('{storeRequisition}/edit-rejected', [StoreRequisitionController::class, 'editRejected'])->name('edit-rejected');
        Route::put('{storeRequisition}/update-rejected', [StoreRequisitionController::class, 'updateRejected'])->name('update-rejected');

        // Workflow actions
        Route::post('{storeRequisition}/submit', [StoreRequisitionController::class, 'submit'])->name('submit');

        // Rate-limited approval actions (10 requests per minute for security)
        Route::middleware(['throttle:10,1'])->group(function () {
            Route::post('{storeRequisition}/approve', [StoreRequisitionController::class, 'approve'])->name('approve');
            Route::post('{storeRequisition}/reject', [StoreRequisitionController::class, 'reject'])->name('reject');
            Route::post('{storeRequisition}/return-for-revision', [StoreRequisitionController::class, 'returnForRevision'])->name('return-for-revision');
        });

        Route::post('{storeRequisition}/issue', [StoreRequisitionController::class, 'issue'])->name('issue');

        // Store keeper tools
        Route::get('{storeRequisition}/picking-list', [StoreRequisitionController::class, 'pickingList'])->name('picking-list');
        Route::get('{storeRequisition}/goods-issue-note', [StoreRequisitionController::class, 'goodsIssueNote'])->name('goods-issue-note');
    });

    // Store Keeper Dashboard routes
    Route::prefix('store-keeper')->name('store-keeper.')->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\StoreKeeperDashboardController::class, 'index'])
            ->name('dashboard')
            ->middleware('permission:store-keep');

        Route::get('/pending-approvals', [App\Http\Controllers\StoreKeeperDashboardController::class, 'pendingApprovals'])
            ->name('pending-approvals')
            ->middleware('permission:store-keep');

        Route::get('/inventory-alerts', [App\Http\Controllers\StoreKeeperDashboardController::class, 'inventoryAlerts'])
            ->name('inventory-alerts')
            ->middleware('permission:store-keep');

        Route::get('/recent-activity', [App\Http\Controllers\StoreKeeperDashboardController::class, 'recentActivity'])
            ->name('recent-activity')
            ->middleware('permission:store-keep');
    });

    // Inventory routes
    Route::prefix('inventory')->name('inventory.')->group(function () {
        // Dashboard and reporting (must come before parameterized routes)
        Route::get('low-stock', [InventoryController::class, 'lowStock'])->name('low-stock');
        Route::get('replenishment-suggestions', [InventoryController::class, 'replenishmentSuggestions'])->name('replenishment-suggestions');
        Route::get('dashboard-summary', [InventoryController::class, 'dashboardSummary'])->name('dashboard-summary');
        Route::get('unit-types', [InventoryController::class, 'getUnitTypes'])->name('unit-types');
        
        // Frontend pages
        Route::get('/', function () {
            $user = Auth::user();

            if (!$user->can('view-inventory') && !$user->can('store-keep')) {
                abort(403, 'Unauthorized to view inventory');
            }

            // Get user's organization
            $organization = $user->organizations()->first();
            if (!$organization) {
                return Inertia::render('Inventory/InventoryIndex', [
                    'items' => [],
                    'error' => 'User must belong to an organization to view inventory'
                ]);
            }

            $items = \App\Models\InventoryItem::where('organization_id', $organization->id)
                ->with(['organization', 'branch'])
                ->orderBy('name')
                ->get();

            return Inertia::render('Inventory/InventoryIndex', [
                'items' => $items
            ]);
        })->name('index')->middleware('permission:view-inventory|store-keep');

        Route::get('/create', function () {
            $branches = Auth::user()->organizations()->first()?->branches ?? collect();
            return Inertia::render('Inventory/CreateInventory', [
                'branches' => $branches
            ]);
        })->name('create')->middleware('permission:manage-inventory|store-keep');

        Route::get('/{inventoryItem}/edit', function (\App\Models\InventoryItem $inventoryItem) {
            $user = Auth::user();

            if (!$user->can('manage-inventory') && !$user->can('store-keep')) {
                abort(403, 'Unauthorized to edit inventory items');
            }

            // Check if user has access to this item's organization
            $organization = $user->organizations()->first();
            if (!$organization || $inventoryItem->organization_id !== $organization->id) {
                abort(403, 'Unauthorized to edit this inventory item');
            }

            $branches = $organization->branches ?? collect();
            return Inertia::render('Inventory/EditInventory', [
                'inventoryItem' => $inventoryItem->load(['organization', 'branch']),
                'branches' => $branches
            ]);
        })->name('edit')->middleware('permission:manage-inventory|store-keep');

        // API endpoints for CRUD operations
        Route::get('/api', [InventoryController::class, 'index'])->name('api.index');
        Route::post('/', [InventoryController::class, 'store'])->name('store');
        Route::get('{inventoryItem}', [InventoryController::class, 'show'])->name('show');
        Route::put('{inventoryItem}', [InventoryController::class, 'update'])->name('update');
        Route::delete('{inventoryItem}', [InventoryController::class, 'destroy'])->name('destroy');
        
        // Stock management
        Route::post('{inventoryItem}/adjust-stock', [InventoryController::class, 'adjustStock'])->name('adjust-stock');
        Route::post('{inventoryItem}/receive-goods', [InventoryController::class, 'receiveGoods'])->name('receive-goods');
        Route::get('{inventoryItem}/transactions', [InventoryController::class, 'transactionHistory'])->name('transactions');
    });
});

// API routes for store requisition issue management
Route::middleware(['throttle:60,1'])->prefix('api/store-requisitions')->name('api.store-requisitions.')->group(function () {
    Route::post('validate-stock', [StoreRequisitionController::class, 'validateStock'])->name('validate-stock');
    Route::get('issue-stats', [StoreRequisitionController::class, 'issueStats'])->name('issue-stats');
});

// CSRF token refresh route
Route::get('/csrf-token', function () {
    return response()->json(['csrf_token' => csrf_token()]);
});

    // Attachment routes
    Route::prefix('attachments')->name('attachments.')->group(function () {
        // Generic attachment routes
        Route::post('{attachable_type}/{attachable_id}/upload', [AttachmentController::class, 'uploadToAttachable'])
            ->name('upload')
            ->where('attachable_type', 'requisitions|store-requisitions|transactions');

        Route::get('{attachable_type}/{attachable_id}', [AttachmentController::class, 'getAttachmentsForAttachable'])
            ->name('index')
            ->where('attachable_type', 'requisitions|store-requisitions|transactions');

        // Download, view, and delete attachments
        Route::get('{attachment}/download', [AttachmentController::class, 'download'])->name('download');
        Route::get('{attachment}/view', [AttachmentController::class, 'view'])->name('view');
        Route::delete('{attachment}', [AttachmentController::class, 'destroy'])->name('destroy');
    });

Route::prefix('suppliers')->name('suppliers.')->group(function () {
    // Guest routes (not authenticated)
    Route::middleware('guest:supplier')->group(function () {
        Route::get('/register', [SupplierController::class, 'create'])->name('create');
        Route::post('/register', [SupplierController::class, 'store'])->name('store');

        // Backward compatibility: redirect supplier login to unified login
        Route::get('/login', function () {
            return redirect()->route('login');
        })->name('login');

        // Keep the authorize route for any existing forms that might still use it
        Route::post('/login', [SupplierController::class, 'authorize'])->name('authorize');
    });

    // Authenticated supplier routes
    Route::middleware('auth:supplier')->group(function () {
        Route::get('/', [SupplierController::class, 'dashboard'])->name('dashboard');
        Route::get('/documents', [SupplierController::class, 'documents'])->name('documents');
        Route::post('/documents', [SupplierController::class, 'uploadDocuments'])->name('documents.upload');
        Route::post('/logout', [SupplierController::class, 'logout'])->name('logout');
    });

    // Admin routes for managing suppliers
    Route::middleware('auth')->group(function () {
        Route::get('/{supplier}', [SupplierController::class, 'show'])->name('show');
        Route::get('/index', [SupplierController::class, 'suppliers'])->name('index');
    });
});


require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';