<?php

namespace Src\ChartOfAccounts\Application\Services;

use Src\ChartOfAccounts\Domain\Repositories\ChartOfAccountRepositoryInterface;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Models\ChartOfAccount;

class ChartOfAccountService
{
    /**
     * @var ChartOfAccountRepositoryInterface
     */
    protected $chartOfAccountRepository;

    /**
     * ChartOfAccountService constructor.
     *
     * @param ChartOfAccountRepositoryInterface $chartOfAccountRepository
     */
    public function __construct(ChartOfAccountRepositoryInterface $chartOfAccountRepository)
    {
        $this->chartOfAccountRepository = $chartOfAccountRepository;
    }

    /**
     * Get all chart of accounts with optional filtering and pagination.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllChartOfAccounts(array $filters, int $perPage = 10): LengthAwarePaginator
    {
        $filtered = $this->chartOfAccountRepository->getAllWithFilters($filters, $perPage);
        return $filtered;
    }

    /**
     * Get all chart of accounts for an organization.
     *
     * @param int $organizationId
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getChartOfAccountsByOrganizationId(int $organizationId, array $filters = [], int $perPage = 10): LengthAwarePaginator
    {
        $coa = $this->chartOfAccountRepository->getByOrganizationId($organizationId, $filters, $perPage);
        return $coa;
    }

    /**
     * Find a chart of account by its ID.
     *
     * @param int $id
     * @return ChartOfAccount|null
     */
    public function getChartOfAccountById(int $id): ?ChartOfAccount
    {
        return $this->chartOfAccountRepository->findById($id);
    }

    /**
     * Create a new chart of account.
     *
     * @param array $data
     * @return ChartOfAccount
     * @throws \Exception
     */
    public function createChartOfAccount(array $data): ChartOfAccount
    {
        // Determine account type from parent account if not provided
        if (empty($data['account_type'])) {
            $data['account_type'] = $this->determineAccountTypeFromParent($data['parent_id'] ?? null);
        }

        // Generate account code if not provided
        if (empty($data['code'])) {
            $data['code'] = $this->generateAccountCode($data['organization_id'], $data['account_type'], $data['parent_id'] ?? null);
        }

        try {
            return $this->chartOfAccountRepository->create($data);
        } catch (\Illuminate\Database\QueryException $e) {
            // Handle constraint violations gracefully
            if (str_contains($e->getMessage(), 'UNIQUE constraint failed')) {
                // Check if it's a code duplication issue
                if (str_contains($e->getMessage(), 'chart_of_accounts.organization_id, chart_of_accounts.code')) {
                    // Generate a new unique code and retry
                    $data['code'] = $this->generateUniqueAccountCode($data['organization_id'], $data['account_type'], $data['parent_id'] ?? null);
                    return $this->chartOfAccountRepository->create($data);
                }
            }

            // Re-throw other database exceptions
            throw $e;
        }
    }

    /**
     * Generate a unique account code based on account type and organization
     *
     * @param int $organizationId
     * @param string $accountType
     * @param int|null $parentId
     * @return string
     */
    protected function generateAccountCode(int $organizationId, string $accountType, ?int $parentId = null): string
    {
        // Get prefix using the helper method
        $prefix = $this->getCodePrefix($accountType, $parentId);

        // Find all codes with this prefix in this organization
        $codes = \App\Models\ChartOfAccount::where('organization_id', $organizationId)
            ->where('code', 'LIKE', $prefix . '-%')
            ->pluck('code')
            ->toArray();

        // Find the highest number by parsing the codes manually
        $highestNumber = 0;
        foreach ($codes as $code) {
            // Extract the number part after the last dash
            $parts = explode('-', $code);
            $lastPart = end($parts);

            // Convert to number if it's numeric
            if (is_numeric($lastPart)) {
                $number = (int) $lastPart;
                if ($number > $highestNumber) {
                    $highestNumber = $number;
                }
            }
        }

        // Set the new number
        $newNumber = $highestNumber > 0 ? $highestNumber + 1 : 1;

        // Format the new code with leading zeros (e.g., EXP-001)
        return $prefix . '-' . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Generate a unique account code with retry logic for duplicates
     *
     * @param int $organizationId
     * @param string $accountType
     * @param int|null $parentId
     * @param int $maxRetries
     * @return string
     * @throws \Exception
     */
    protected function generateUniqueAccountCode(int $organizationId, string $accountType, ?int $parentId = null, int $maxRetries = 10): string
    {
        for ($attempt = 0; $attempt < $maxRetries; $attempt++) {
            $code = $this->generateAccountCode($organizationId, $accountType, $parentId);

            // Check if this code already exists
            $exists = \App\Models\ChartOfAccount::where('organization_id', $organizationId)
                ->where('code', $code)
                ->exists();

            if (!$exists) {
                return $code;
            }

            // If code exists, add a random suffix and try again
            $randomSuffix = str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
            $code = $this->getCodePrefix($accountType, $parentId) . '-' . $organizationId . '-' . $randomSuffix;

            $exists = \App\Models\ChartOfAccount::where('organization_id', $organizationId)
                ->where('code', $code)
                ->exists();

            if (!$exists) {
                return $code;
            }
        }

        throw new \Exception("Unable to generate unique account code after {$maxRetries} attempts for organization {$organizationId}");
    }

    /**
     * Get the code prefix based on account type and parent
     *
     * @param string $accountType
     * @param int|null $parentId
     * @return string
     */
    protected function getCodePrefix(string $accountType, ?int $parentId = null): string
    {
        // Get prefix based on account type
        $prefix = strtoupper(substr($accountType, 0, 3));

        // If there's a parent, use its code as a prefix
        if ($parentId) {
            $parent = $this->chartOfAccountRepository->findById($parentId);
            if ($parent && $parent->code) {
                $prefix = $parent->code;
            }
        }

        return $prefix;
    }

    /**
     * Determine account type from parent account.
     *
     * @param int|null $parentId
     * @return string
     */
    protected function determineAccountTypeFromParent(?int $parentId): string
    {
        if (!$parentId) {
            // If no parent, default to expense (most common for new accounts)
            return 'expense';
        }

        $parent = $this->chartOfAccountRepository->findById($parentId);
        if (!$parent) {
            // If parent not found, default to expense
            return 'expense';
        }

        // Return the same account type as the parent
        return $parent->account_type;
    }

    /**
     * Update an existing chart of account.
     *
     * @param int $id
     * @param array $data
     * @return ChartOfAccount|null
     */
    public function updateChartOfAccount(int $id, array $data): ?ChartOfAccount
    {
        $chartOfAccount = $this->chartOfAccountRepository->findById($id);
        if (!$chartOfAccount) {
            return null;
        }

        // Determine account type from parent account if not provided
        if (empty($data['account_type'])) {
            $data['account_type'] = $this->determineAccountTypeFromParent($data['parent_id'] ?? $chartOfAccount->parent_id);
        }

        // If account type or parent has changed, and code is empty or not provided, regenerate the code
        $accountTypeChanged = isset($data['account_type']) && $data['account_type'] !== $chartOfAccount->account_type;
        $parentChanged = isset($data['parent_id']) && $data['parent_id'] != $chartOfAccount->parent_id;

        if (($accountTypeChanged || $parentChanged) && empty($data['code'])) {
            $data['code'] = $this->generateAccountCode(
                $data['organization_id'] ?? $chartOfAccount->organization_id,
                $data['account_type'] ?? $chartOfAccount->account_type,
                $data['parent_id'] ?? $chartOfAccount->parent_id
            );
        }

        return $this->chartOfAccountRepository->update($id, $data);
    }

    /**
     * Delete a chart of account.
     *
     * @param int $id
     * @return bool
     */
    public function deleteChartOfAccount(int $id): bool
    {
        return $this->chartOfAccountRepository->delete($id);
    }

    /**
     * Get all parent chart of accounts for an organization.
     *
     * @param int $organizationId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getParentAccountsByOrganizationId(int $organizationId)
    {
        return $this->chartOfAccountRepository->getParentAccountsByOrganizationId($organizationId);
    }

    /**
     * Get all chart of accounts for an organization as a hierarchical structure.
     *
     * @param int $organizationId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getHierarchicalAccountsByOrganizationId(int $organizationId)
    {
        return $this->chartOfAccountRepository->getHierarchicalAccountsByOrganizationId($organizationId);
    }
}
