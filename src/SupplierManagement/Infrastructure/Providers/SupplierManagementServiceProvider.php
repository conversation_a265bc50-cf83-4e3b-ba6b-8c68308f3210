<?php

namespace Src\SupplierManagement\Infrastructure\Providers;

use Illuminate\Support\ServiceProvider;
// use Src\SupplierManagement\Domain\Repositories\SupplierRepositoryInterface;
// use Src\SupplierManagement\Infrastructure\Repositories\EloquentSupplierRepository;
use Src\SupplierManagement\Application\Services\SupplierService;
use Src\SupplierManagement\Application\Services\MetricService;
use Src\SupplierManagement\Domain\Services\SupplierServiceInterface;
use Src\SupplierManagement\Domain\Services\MetricServiceInterface;

class SupplierManagementServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(
            SupplierServiceInterface::class,
            SupplierService::class,
        );

        $this->app->bind(
            MetricServiceInterface::class,
            MetricService::class,
        );

        // // Register the repository
        // $this->app->bind(
        //     SupplierRepositoryInterface::class,
        //     EloquentSupplierRepository::class
        // );

        // // Register the service
        // $this->app->bind(SupplierService::class, function ($app) {
        //     return new SupplierService(
        //         $app->make(SupplierRepositoryInterface::class)
        //     );
        // });
    }

    /**
     * Bootstrap services.
     *
     * Note: Routes are defined in routes/web.php to keep all framework-specific code together
     * and separate from the domain logic.
     */
    public function boot(): void
    {
        // Routes are defined in routes/web.php
    }
}
