<?php

namespace Src\SupplierManagement\Application\Services;

use App\Models\Supplier;
use Illuminate\Support\Facades\Log;
use Src\SupplierManagement\Domain\Services\SupplierServiceInterface;

class SupplierService implements SupplierServiceInterface
{
    public function createSupplier(array $data): array
    {
        try {
            $supplier = Supplier::create([
                'name' => $data['org_name'],
                'contact_person' => $data['first_name'] . ' ' . $data['last_name'],
                'email' => $data['email'],
                'phone' => $data['phone'],
                'address' => $data['address'] ?? null,
                'tax_number' => $data['tax_number'] ?? null,
                'registration_number' => $data['registration_number'] ?? null,
                'company_description' => $data['company_description'] ?? null,
                'established_year' => $data['established_year'] ?? null,
                'website' => $data['website'] ?? null,
                'password' => $data['password'], // auto-hashed via model casting
            ]);

            return $supplier->toArray();
        } catch (\Exception $e) {
            Log::error('Supplier creation failed', [
                'data' => $data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return [];
        }
    }
}
