# Purchase Requisition Permissions

This document outlines the simplified permission structure for the Purchase Requisition module in the SIPPAR application.

## Overview

The Purchase Requisition module uses a streamlined set of 5 core permissions that control access to purchase requisition functionality. These permissions follow the same naming convention as other modules in the system: `action-resource`.

## Core Permissions

| Permission | Description | Typical Roles |
|------------|-------------|---------------|
| `create-purchase-requisitions` | Create and edit purchase requisitions | Employee, Finance Admin, Organization Admin, Procurement Manager |
| `view-purchase-requisitions` | View purchase requisition details and lists | All roles |
| `approve-purchase-requisitions` | Approve or reject purchase requisitions | HOD, Finance Manager, Finance Admin, Organization Admin |
| `manage-purchase-requisitions` | Full management including deletion and workflow management | Platform Admin, Organization Admin, Finance Admin, Procurement Manager |
| `view-purchase-reports` | View and export purchase requisition reports | Finance Manager, Finance Admin, Organization Admin, Procurement Manager |

## Role-Based Permission Assignment

### Platform Admin
- **All 5 permissions** - Complete system access

### Organization Admin
- `create-purchase-requisitions` - Can create and edit requisitions
- `view-purchase-requisitions` - Can view all requisitions
- `approve-purchase-requisitions` - Can approve/reject requisitions
- `manage-purchase-requisitions` - Full management capabilities
- `view-purchase-reports` - Can view and export reports

### Finance Manager
- `view-purchase-requisitions` - Can view all requisitions
- `approve-purchase-requisitions` - Can approve/reject requisitions
- `view-purchase-reports` - Can view and export reports

### HOD (Head of Department)
- `view-purchase-requisitions` - Can view requisitions
- `approve-purchase-requisitions` - Can approve/reject department requisitions

### Employee
- `create-purchase-requisitions` - Can create and edit their own requisitions
- `view-purchase-requisitions` - Can view requisitions

### Finance Admin
- `create-purchase-requisitions` - Can create and edit requisitions
- `view-purchase-requisitions` - Can view all requisitions
- `approve-purchase-requisitions` - Can approve/reject requisitions
- `manage-purchase-requisitions` - Full management capabilities
- `view-purchase-reports` - Can view and export reports

### Procurement Manager
- `create-purchase-requisitions` - Can create and edit requisitions
- `view-purchase-requisitions` - Can view all requisitions
- `approve-purchase-requisitions` - Can approve/reject requisitions
- `manage-purchase-requisitions` - Full management capabilities
- `view-purchase-reports` - Can view and export reports

## Implementation Notes

### Seeder Structure
The `PurchaseRequisitionPermissionsSeeder` handles:
1. Creating 5 core purchase requisition permissions
2. Assigning permissions to existing roles based on their responsibilities
3. Updating permission descriptions if they already exist
4. Graceful handling of missing roles

### Migration Integration
A migration (`2025_08_07_000003_add_purchase_requisition_permissions.php`) automatically runs the seeder for existing installations.

### Testing
Comprehensive tests verify:
- All 5 permissions are created correctly
- Role assignments work as expected for each role type
- Permission descriptions are updated properly
- Missing roles are handled gracefully without errors

## Usage Examples

### Checking Permissions in Controllers
```php
// Check if user can create purchase requisitions
if (auth()->user()->can('create-purchase-requisitions')) {
    // Allow creation
}

// Check if user can approve purchase requisitions
if (auth()->user()->can('approve-purchase-requisitions')) {
    // Show approval buttons
}
```

### Middleware Usage
```php
// Protect routes with specific permissions
Route::middleware(['permission:view-purchase-requisitions'])->group(function () {
    Route::get('/purchase-requisitions', [PurchaseRequisitionController::class, 'index']);
});

Route::middleware(['permission:approve-purchase-requisitions'])->group(function () {
    Route::post('/purchase-requisitions/{id}/approve', [PurchaseRequisitionController::class, 'approve']);
});
```

### Policy Usage
```php
// In PurchaseRequisitionPolicy
public function approve(User $user, PurchaseRequisition $requisition)
{
    return $user->can('approve-purchase-requisitions') &&
           $requisition->status === 'pending_approval';
}
```

## Security Considerations

1. **Principle of Least Privilege**: Each role only gets the minimum permissions needed
2. **Separation of Duties**: Creation and approval permissions are clearly separated
3. **Simplified Structure**: Only 5 permissions reduce complexity and potential security gaps
4. **Clear Boundaries**: Each permission has a well-defined scope of access

## Benefits of Simplified Structure

1. **Easier Management**: Fewer permissions to track and assign
2. **Reduced Complexity**: Less chance for permission conflicts or gaps
3. **Better Performance**: Fewer permission checks in the application
4. **Clearer Understanding**: Roles and responsibilities are more obvious
5. **Consistent Naming**: Follows the existing pattern in the application (`create-requisitions`, `view-requisitions`, etc.)
