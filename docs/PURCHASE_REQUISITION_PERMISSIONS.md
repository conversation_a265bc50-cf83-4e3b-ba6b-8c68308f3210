# Purchase Requisition Permissions

This document outlines the permission structure for the Purchase Requisition module in the SIPPAR application.

## Overview

The Purchase Requisition module introduces a comprehensive set of permissions that control access to purchase requisition functionality. These permissions follow the same naming convention as other modules in the system: `action-resource`.

## Permission Categories

### Core Purchase Requisition Permissions

| Permission | Description | Typical Roles |
|------------|-------------|---------------|
| `manage-purchase-requisitions` | Full management of all purchase requisitions | Platform Admin, Organization Admin |
| `create-purchase-requisition` | Create new purchase requisitions | Employee, Finance Admin, Procurement Manager |
| `view-purchase-requisitions` | View purchase requisition details and lists | All roles |
| `edit-purchase-requisition` | Edit existing purchase requisitions | Employee (own), Finance Admin, Organization Admin |
| `delete-purchase-requisition` | Delete purchase requisitions | Finance Admin, Organization Admin, Platform Admin |
| `submit-purchase-requisition` | Submit purchase requisitions for approval | Employee, Finance Admin, Procurement Manager |

### Approval Workflow Permissions

| Permission | Description | Typical Roles |
|------------|-------------|---------------|
| `approve-purchase-requisition` | Approve purchase requisitions | HOD, Finance Manager, Organization Admin |
| `reject-purchase-requisition` | Reject purchase requisitions | HOD, Finance Manager, Organization Admin |
| `review-purchase-requisition` | Review requisitions (acknowledge before approval) | HOD, Finance Manager |
| `amend-purchase-requisition` | Make changes to submitted requisitions | Finance Manager, Organization Admin |
| `cancel-purchase-requisition` | Cancel purchase requisitions | Finance Manager, Organization Admin |

### Advanced Workflow Permissions

| Permission | Description | Typical Roles |
|------------|-------------|---------------|
| `convert-to-tender` | Convert purchase requisitions to tender processes | Finance Manager, Procurement Manager, Organization Admin |
| `manage-purchase-approval-workflows` | Manage approval workflow configurations | Platform Admin, Organization Admin |
| `view-purchase-approval-workflows` | View approval workflow configurations | Finance Admin, Organization Admin |
| `create-purchase-approval-workflows` | Create new approval workflows | Platform Admin, Organization Admin |
| `edit-purchase-approval-workflows` | Edit existing approval workflows | Platform Admin, Organization Admin |
| `delete-purchase-approval-workflows` | Delete approval workflows | Platform Admin, Organization Admin |

### Item Management Permissions

| Permission | Description | Typical Roles |
|------------|-------------|---------------|
| `manage-purchase-requisition-items` | Full management of requisition items | Finance Admin, Organization Admin |
| `add-purchase-requisition-items` | Add items to purchase requisitions | Employee, Finance Admin, Procurement Manager |
| `edit-purchase-requisition-items` | Edit items in purchase requisitions | Employee, Finance Admin, Procurement Manager |
| `remove-purchase-requisition-items` | Remove items from purchase requisitions | Employee, Finance Admin, Procurement Manager |

### Reporting and Analytics Permissions

| Permission | Description | Typical Roles |
|------------|-------------|---------------|
| `view-purchase-requisition-reports` | View purchase requisition reports and analytics | HOD, Finance Manager, Finance Admin |
| `export-purchase-requisition-reports` | Export reports to Excel/PDF | Finance Manager, Finance Admin, Organization Admin |
| `view-purchase-requisition-analytics` | View analytics and dashboards | Finance Manager, Finance Admin, Organization Admin |

### Audit and History Permissions

| Permission | Description | Typical Roles |
|------------|-------------|---------------|
| `view-purchase-requisition-history` | View change history and audit logs | HOD, Finance Manager, Finance Admin |
| `view-purchase-requisition-audit-logs` | View detailed audit logs | Finance Manager, Finance Admin, Organization Admin |

## Role-Based Permission Assignment

### Platform Admin
- **All permissions** - Complete system access

### Organization Admin
- All permissions except platform-level workflow management
- Can manage organization-specific workflows and settings

### Finance Manager
- Approval and management permissions
- Reporting and analytics access
- Can convert requisitions to tenders
- Full audit trail access

### HOD (Head of Department)
- Review and approval permissions for department requisitions
- Basic reporting access
- History viewing capabilities

### Employee
- Basic creation and editing permissions
- Can manage items in their own requisitions
- Can submit requisitions for approval
- **Cannot** approve, delete, or manage workflows

### Finance Admin
- Comprehensive permissions similar to Organization Admin
- Cannot manage approval workflows (view only)
- Full CRUD operations on requisitions

### Procurement Manager
- Specialized role for procurement processes
- Can convert requisitions to tenders
- Full requisition management capabilities
- Reporting and analytics access

## Implementation Notes

### Seeder Structure
The `PurchaseRequisitionPermissionsSeeder` handles:
1. Creating all purchase requisition permissions
2. Assigning permissions to existing roles
3. Updating permission descriptions if they already exist
4. Graceful handling of missing roles

### Migration Integration
A migration (`2025_08_07_000003_add_purchase_requisition_permissions.php`) automatically runs the seeder for existing installations.

### Testing
Comprehensive tests verify:
- All permissions are created correctly
- Role assignments work as expected
- Permission descriptions are updated
- Missing roles are handled gracefully

## Usage Examples

### Checking Permissions in Controllers
```php
// Check if user can create purchase requisitions
if (auth()->user()->can('create-purchase-requisition')) {
    // Allow creation
}

// Check if user can approve purchase requisitions
if (auth()->user()->can('approve-purchase-requisition')) {
    // Show approval buttons
}
```

### Middleware Usage
```php
// Protect routes with specific permissions
Route::middleware(['permission:view-purchase-requisitions'])->group(function () {
    Route::get('/purchase-requisitions', [PurchaseRequisitionController::class, 'index']);
});

Route::middleware(['permission:approve-purchase-requisition'])->group(function () {
    Route::post('/purchase-requisitions/{id}/approve', [PurchaseRequisitionController::class, 'approve']);
});
```

### Policy Usage
```php
// In PurchaseRequisitionPolicy
public function approve(User $user, PurchaseRequisition $requisition)
{
    return $user->can('approve-purchase-requisition') && 
           $requisition->status === 'pending_approval';
}
```

## Security Considerations

1. **Principle of Least Privilege**: Each role only gets the minimum permissions needed
2. **Separation of Duties**: Creation and approval permissions are separated
3. **Audit Trail**: Comprehensive logging permissions for compliance
4. **Workflow Control**: Approval workflow management is restricted to admin roles

## Future Enhancements

Potential additional permissions for future features:
- `bulk-approve-purchase-requisitions` - For batch approval operations
- `delegate-purchase-approval` - For temporary approval delegation
- `override-purchase-workflow` - For emergency workflow bypassing
- `archive-purchase-requisitions` - For archiving old requisitions
