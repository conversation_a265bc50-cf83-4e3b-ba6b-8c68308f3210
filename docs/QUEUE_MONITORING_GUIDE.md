# 📊 Queue Monitoring System Guide

## Overview
This guide covers the comprehensive queue monitoring system that tracks your queue health, performance, and alerts you to issues.

## 📁 Monitoring Files

### Essential Files (3 files):

#### 1. **`app/Console/Commands/MonitorQueueHealth.php`**
**Purpose:** Main health monitoring and alerting system
- ✅ Monitors failed job count, pending jobs, and job age
- ✅ Sends email alerts when thresholds exceeded
- ✅ Prevents alert spam with 30-minute cooldown
- ✅ Logs all issues to <PERSON><PERSON> logs

#### 2. **`app/Console/Commands/QueueDashboard.php`**
**Purpose:** Real-time queue dashboard and statistics
- ✅ Shows current queue status (pending/failed jobs)
- ✅ Displays recent activity and daily statistics
- ✅ Lists failed job details and worker health
- ✅ Supports live monitoring with auto-refresh

#### 3. **`app/Console/Commands/ProcessQueueWithMonitoring.php`**
**Purpose:** Enhanced queue processing with metrics
- ✅ Processes queue jobs with monitoring
- ✅ Records performance metrics and memory usage
- ✅ Tracks processing duration and job counts
- ✅ Provides heartbeat functionality

## ⚡ Quick Setup

### 1. Files Already Created
✅ **3 monitoring command files** are already in place
✅ **Cron jobs updated** in `setup.sh` with your email
✅ **Laravel scheduler updated** in `Kernel.php`
✅ **Ready to use** - no additional setup needed!

### 2. Test the System
```bash
# Test the dashboard
php artisan queue:dashboard

# Test health monitoring
php artisan queue:monitor-health --alert-email=<EMAIL>

# Test queue processing with monitoring
php artisan queue:process-monitored
```

## 🚀 Quick Start

### 1. Basic Monitoring Commands

```bash
# Check current queue status
php artisan queue:dashboard

# Monitor queue health
php artisan queue:monitor-health

# Watch real-time dashboard (refreshes every 5 seconds)
php artisan queue:dashboard --watch

# Process queue with monitoring
php artisan queue:process-monitored
```

### 2. Setup Email Alerts

```bash
# Monitor with email alerts
php artisan queue:monitor-health --alert-email=<EMAIL>

# Custom thresholds
php artisan queue:monitor-health \
  --alert-email=<EMAIL> \
  --max-failed=5 \
  --max-pending=50 \
  --max-age=180
```

## 📈 Monitoring Commands

### `queue:dashboard`
Real-time queue dashboard showing:
- Current status (pending/failed jobs)
- Recent activity
- Daily statistics
- Failed job details
- Worker health

**Usage:**
```bash
# One-time view
php artisan queue:dashboard

# Continuous monitoring (updates every 5 seconds)
php artisan queue:dashboard --watch

# Custom refresh rate
php artisan queue:dashboard --watch --refresh=10
```

### `queue:monitor-health`
Health check with configurable alerts:

**Parameters:**
- `--alert-email`: Email for alerts
- `--max-failed`: Max failed jobs before alert (default: 10)
- `--max-pending`: Max pending jobs before alert (default: 100)
- `--max-age`: Max job age in seconds before alert (default: 300)

**Usage:**
```bash
# Basic health check
php artisan queue:monitor-health

# With email alerts
php artisan queue:monitor-health --alert-email=<EMAIL>

# Custom thresholds
php artisan queue:monitor-health \
  --alert-email=<EMAIL> \
  --max-failed=5 \
  --max-pending=50 \
  --max-age=180
```

### `queue:process-monitored`
Enhanced queue processing with monitoring:

**Features:**
- Records heartbeat before/after processing
- Logs performance metrics
- Tracks memory usage
- Counts processed jobs

**Usage:**
```bash
# Basic monitored processing
php artisan queue:process-monitored

# Custom settings
php artisan queue:process-monitored --timeout=120 --tries=5 --worker-id=manual
```

## 🔧 Configuration

### 1. Update Your Cron Jobs

The setup.sh already includes monitoring cron jobs:

```bash
# Queue processing with monitoring (every minute)
* * * * * cd /path/to/app && php artisan schedule:run >> /dev/null 2>&1

# Health monitoring (every 5 minutes)
*/5 * * * * cd /path/to/app && php artisan queue:monitor-health --alert-email=<EMAIL> >/dev/null 2>&1
```

### 2. Email Configuration

Update your `.env` file:
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Your App Name"
```

### 3. Monitoring Thresholds

Adjust thresholds based on your needs:

**Low Traffic Sites:**
```bash
--max-failed=3 --max-pending=20 --max-age=120
```

**High Traffic Sites:**
```bash
--max-failed=20 --max-pending=200 --max-age=600
```

## 📊 Understanding the Dashboard

### Current Status Section
- **Pending Jobs**: Number of jobs waiting to be processed
- **Failed Jobs**: Number of jobs that failed
- **Oldest Pending**: How long the oldest job has been waiting

### Recent Activity Section
- Shows last 10 jobs in the queue
- Displays job type and creation time

### Daily Statistics Section
- **Total Runs**: How many times queue processing ran today
- **Jobs Processed**: Total jobs processed today
- **Failed Runs**: Number of failed processing attempts
- **Success Rate**: Percentage of successful runs
- **Avg Duration**: Average time per processing run

### Worker Health Section
- **Last Seen**: When the worker last reported status
- **Last Processed**: When jobs were last processed
- **Last Health Check**: When health monitoring last ran

## 🚨 Alert Types

### 1. Too Many Failed Jobs
**Trigger**: Failed jobs exceed threshold
**Action**: Check failed job details, fix underlying issues

### 2. Too Many Pending Jobs
**Trigger**: Pending jobs exceed threshold
**Action**: Check if queue processing is running, consider scaling

### 3. Jobs Too Old
**Trigger**: Jobs pending longer than threshold
**Action**: Check queue processing frequency, investigate bottlenecks

### 4. Worker Not Responding
**Trigger**: Worker hasn't reported status recently
**Action**: Check cron jobs, server health, PHP processes

## 🔍 Troubleshooting

### No Jobs Being Processed
```bash
# Check if cron is running
crontab -l
systemctl status cron

# Check Laravel scheduler
php artisan schedule:list

# Manual queue processing
php artisan queue:work --once
```

### High Memory Usage
```bash
# Check current memory usage
php artisan queue:dashboard

# Process with memory limit
php artisan queue:work --memory=128
```

### Failed Jobs Investigation
```bash
# View failed jobs
php artisan queue:failed

# Retry specific failed job
php artisan queue:retry 1

# Retry all failed jobs
php artisan queue:retry all

# Clear failed jobs
php artisan queue:flush
```

### Email Alerts Not Working
```bash
# Test email configuration
php artisan tinker
Mail::raw('Test', function($m) { $m->to('<EMAIL>')->subject('Test'); });

# Check mail logs
tail -f storage/logs/laravel.log | grep -i mail
```

## 📈 Performance Optimization

### 1. Monitoring Overhead
The monitoring system is designed to be lightweight:
- Heartbeat: ~1ms overhead
- Health check: ~50ms every 5 minutes
- Dashboard: On-demand only

### 2. Cache Usage
Monitoring data is stored in cache:
- Heartbeats: 5 minutes
- Daily stats: Until end of day
- Metrics: 24 hours
- Health alerts: 30 minutes (to prevent spam)

### 3. Log Management
```bash
# Clean old monitoring logs
php artisan cache:clear

# View monitoring logs
tail -f storage/logs/laravel.log | grep -i queue
```

## 🎯 Best Practices

1. **Set Realistic Thresholds**: Start conservative, adjust based on your traffic
2. **Monitor Regularly**: Check dashboard weekly, investigate trends
3. **Act on Alerts**: Don't ignore alerts, investigate root causes
4. **Test Email Alerts**: Verify alerts work before relying on them
5. **Keep Logs**: Monitor storage/logs/laravel.log for queue issues
6. **Regular Cleanup**: Clear old failed jobs periodically

## 🗂️ File Management

### Current Monitoring Files (Keep These)
```
app/Console/Commands/
├── MonitorQueueHealth.php      # Main health monitoring & alerts
├── QueueDashboard.php          # Real-time dashboard
└── ProcessQueueWithMonitoring.php  # Enhanced queue processing
```

### Configuration Files
```
app/Console/Kernel.php          # Scheduled commands
setup.sh                       # Cron job configuration
docs/QUEUE_MONITORING_GUIDE.md  # This guide
```

### What Each File Does

**MonitorQueueHealth.php:**
- Runs every 5 minutes via cron
- Checks queue health metrics
- Sends email alerts when issues detected
- Prevents alert spam

**QueueDashboard.php:**
- On-demand dashboard display
- Real-time monitoring with `--watch`
- Shows comprehensive queue statistics
- Used for manual monitoring

**ProcessQueueWithMonitoring.php:**
- Replaces basic `queue:work` command
- Adds performance tracking
- Records heartbeat and metrics
- Runs every minute via cron

## 📱 Integration with External Monitoring

### Slack Notifications (Optional)
You can extend the monitoring to send Slack notifications:

```php
// In MonitorQueueHealth.php, add Slack notification
use Illuminate\Support\Facades\Http;

private function sendSlackAlert($issues) {
    Http::post('YOUR_SLACK_WEBHOOK_URL', [
        'text' => '🚨 Queue Health Alert: ' . implode(', ', $issues)
    ]);
}
```

### Metrics Export (Optional)
Export metrics to external monitoring systems:

```bash
# Export daily stats
php artisan tinker
Cache::get('queue_daily_' . now()->format('Y-m-d'));
```
