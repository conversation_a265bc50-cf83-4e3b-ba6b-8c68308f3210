# Supplier Authentication System

This document explains the supplier authentication system, including routes, functionality, and implementation details.

## Overview

The supplier authentication system provides a separate login and registration flow for suppliers, independent from the main user authentication system. Suppliers have their own authentication guard, dashboard, and user interface.

## Authentication Guard

The system uses a dedicated `supplier` authentication guard configured in `config/auth.php`:

```php
'guards' => [
    'web' => [
        'driver' => 'session',
        'provider' => 'users',
    ],
    'supplier' => [
        'driver' => 'session',
        'provider' => 'suppliers',
    ],
],

'providers' => [
    'suppliers' => [
        'driver' => 'eloquent',
        'model' => App\Models\Supplier::class,
    ],
],
```

## Routes

### Public Routes (Guest Access)

| Method | URI | Name | Description |
|--------|-----|------|-------------|
| GET | `/suppliers/register` | `suppliers.create` | Supplier registration form |
| POST | `/suppliers/register` | `suppliers.store` | Process supplier registration |
| GET | `/suppliers/login` | `suppliers.login` | Supplier login form |
| POST | `/suppliers/login` | `suppliers.authorize` | Process supplier login |

### Protected Routes (Authenticated Suppliers)

| Method | URI | Name | Description |
|--------|-----|------|-------------|
| GET | `/suppliers/` | `suppliers.dashboard` | Supplier dashboard |
| POST | `/suppliers/logout` | `suppliers.logout` | Supplier logout |

### Admin Routes (Regular User Access)

| Method | URI | Name | Description |
|--------|-----|------|-------------|
| GET | `/suppliers/{supplier}` | `suppliers.show` | View supplier details |
| GET | `/suppliers/index` | `suppliers.index` | List all suppliers |

## Components

### Frontend Pages

1. **Supplier Login** (`resources/js/pages/suppliers/login.tsx`)
   - Dedicated login form for suppliers
   - Links to supplier registration
   - Uses `suppliers.authorize` route

2. **Supplier Registration** (`resources/js/pages/suppliers/register.tsx`)
   - Multi-step registration form
   - Company and contact information
   - Links to supplier login

3. **Supplier Dashboard** (`resources/js/pages/suppliers/dashboard.tsx`)
   - Overview of supplier metrics
   - Order statistics and performance data
   - Account status information

### Backend Controllers

**SupplierController** (`app/Http/Controllers/SupplierController.php`)

Key methods:
- `login()` - Display supplier login page
- `authorize(SupplierLoginRequest $request)` - Handle supplier authentication
- `logout(Request $request)` - Handle supplier logout
- `dashboard()` - Display supplier dashboard
- `create()` - Display registration form
- `store(StoreSupplierRequest $request)` - Process registration

## Authentication Flow

### Registration Process

1. Supplier visits `/suppliers/register`
2. Fills out registration form with company details
3. Form submits to `suppliers.store` route
4. `SupplierController@store` processes the registration
5. Supplier account created with `pending_approval` status
6. Redirect to supplier dashboard

### Login Process

1. Supplier visits `/suppliers/login`
2. Enters email and password
3. Form submits to `suppliers.authorize` route
4. `SupplierLoginRequest` validates credentials
5. Authentication attempted using `supplier` guard
6. On success: redirect to supplier dashboard
7. On failure: return with validation errors

### Logout Process

1. Supplier clicks logout button
2. POST request to `suppliers.logout`
3. `SupplierController@logout` destroys session
4. Redirect to supplier login page

## Security Features

### Authentication Request Validation

**SupplierLoginRequest** (`app/Http/Requests/Auth/SupplierLoginRequest.php`)

- Email and password validation
- Rate limiting (5 attempts per email/IP combination)
- CSRF protection
- Automatic session regeneration on successful login

### Password Security

- Passwords are automatically hashed using Laravel's default hasher
- Strong password validation rules applied
- Password confirmation required during registration

### Session Management

- Separate session handling for supplier guard
- Session regeneration on login/logout
- Remember me functionality
- Last login timestamp tracking

## Middleware

### Guest Middleware

Routes use `guest:supplier` middleware to prevent authenticated suppliers from accessing login/register pages.

### Authentication Middleware

Protected routes use `auth:supplier` middleware to ensure only authenticated suppliers can access them.

## Model

**Supplier Model** (`app/Models/Supplier.php`)

- Extends `Illuminate\Foundation\Auth\User` (Authenticatable)
- Uses `Notifiable` trait for notifications
- Automatic password hashing via model casting
- Performance tracking attributes
- Business logic methods for verification and status management

Key attributes:
- `name` - Company name
- `contact_person` - Primary contact
- `email` - Login email (unique)
- `phone` - Contact phone
- `status` - Account status (active, pending_approval, etc.)
- `is_verified` - Verification status
- `overall_rating` - Performance rating
- `total_orders` - Order count
- `successful_deliveries` - Successful delivery count

## Navigation

### Cross-linking

- Supplier login page links to supplier registration
- Supplier registration page links to supplier login
- Both pages maintain links to regular user authentication for flexibility

### Breadcrumbs

The supplier dashboard uses breadcrumbs for navigation:
```php
$breadcrumbs = [
    [
        'title' => 'Supplier Dashboard',
        'href' => '/suppliers/',
    ],
];
```

## Error Handling

### Authentication Errors

- Invalid credentials return validation errors
- Rate limiting prevents brute force attacks
- Proper error messages displayed to users

### Middleware Compatibility

The system includes compatibility fixes for the `HandleInertiaRequests` middleware to prevent errors when suppliers don't have role-based permissions:

```php
'roles' => method_exists($request->user(), 'getRoleNames') ? $request->user()->getRoleNames() : [],
'permissions' => method_exists($request->user(), 'getAllPermissions') ? $request->user()->getAllPermissions()->pluck('name') : [],
```

## Testing

Comprehensive test coverage is provided in `tests/Feature/SupplierAuthTest.php`:

- Login page accessibility
- Authentication with valid/invalid credentials
- Dashboard access control
- Logout functionality
- Guest access restrictions

## Configuration

### Environment Variables

No additional environment variables are required. The system uses Laravel's default authentication configuration.

### Database

The supplier authentication system uses the `suppliers` table with the following key fields:
- Authentication: `email`, `password`, `remember_token`
- Timestamps: `email_verified_at`, `last_login_at`
- Status: `status`, `is_verified`, `verification_date`

## Future Enhancements

Potential improvements for the supplier authentication system:

1. **Email Verification**: Add email verification flow for new suppliers
2. **Password Reset**: Implement password reset functionality
3. **Role-Based Permissions**: Implement supplier-specific roles and permissions
