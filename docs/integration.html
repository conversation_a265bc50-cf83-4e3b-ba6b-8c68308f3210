<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sippar - Integration Testing Strategy</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
        }
        .tech-highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.9em;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        ul, ol {
            padding-left: 25px;
        }
        li {
            margin: 8px 0;
        }
        code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: #3498db;
            color: white;
        }
        .priority-high { background: #ffebee; }
        .priority-medium { background: #fff3e0; }
        .priority-low { background: #e8f5e8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Sippar Integration Testing Strategy</h1>

        <div class="info">
            <strong>Document Purpose:</strong> This document outlines a comprehensive integration testing strategy for the Sippar petty cash management system, highlighting all required technologies and testing approaches.
        </div>

        <h2>1. Application Overview</h2>
        <div class="section">
            <p><strong>Sippar</strong> is a comprehensive web-based petty cash management platform built with modern technologies:</p>
            <ul>
                <li><span class="tech-highlight">Laravel 12</span> - Backend framework with PHP 8.2+</li>
                <li><span class="tech-highlight">React 19</span> - Frontend framework with TypeScript</li>
                <li><span class="tech-highlight">Inertia.js</span> - Full-stack framework bridging Laravel and React</li>
                <li><span class="tech-highlight">Domain-Driven Design</span> - Architecture pattern with business logic in src/domains</li>
                <li><span class="tech-highlight">Multi-tenant</span> - Organization-based isolation</li>
            </ul>

            <h3>Core Business Domains</h3>
            <ul>
                <li><strong>Requisition Management</strong> - Request creation, approval workflows, status tracking</li>
                <li><strong>Approval Workflows</strong> - Multi-step approval processes with role-based permissions</li>
                <li><strong>Transaction Processing</strong> - Financial transaction handling and disbursement</li>
                <li><strong>Cash Float Management</strong> - Department cash allocation and monitoring</li>
                <li><strong>User Management</strong> - Multi-organizational user roles and permissions</li>
                <li><strong>Attachment Management</strong> - File upload, storage, and retrieval system</li>
                <li><strong>Notification System</strong> - Real-time notifications and email alerts</li>
            </ul>
        </div>

        <h2>2. Technology Stack Requirements</h2>
        <div class="section">
            <h3>Backend Technologies</h3>
            <table>
                <tr>
                    <th>Technology</th>
                    <th>Version</th>
                    <th>Purpose</th>
                    <th>Testing Requirements</th>
                </tr>
                <tr>
                    <td><span class="tech-highlight">PHP</span></td>
                    <td>8.2+</td>
                    <td>Core runtime</td>
                    <td>Unit tests, Feature tests, Integration tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Laravel</span></td>
                    <td>12.0</td>
                    <td>Web framework</td>
                    <td>HTTP tests, Artisan command tests, Queue tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Composer</span></td>
                    <td>Latest</td>
                    <td>Dependency management</td>
                    <td>Dependency resolution tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">SQLite/MySQL/PostgreSQL</span></td>
                    <td>Latest</td>
                    <td>Database systems</td>
                    <td>Database integration tests, Migration tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Spatie Laravel Permission</span></td>
                    <td>6.0</td>
                    <td>Role-based access control</td>
                    <td>Permission integration tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Laravel DomPDF</span></td>
                    <td>3.1</td>
                    <td>PDF generation</td>
                    <td>PDF generation tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Inertia Laravel</span></td>
                    <td>2.0</td>
                    <td>Frontend-backend bridge</td>
                    <td>Inertia response tests</td>
                </tr>
            </table>
        </div>

        <div class="section">
            <h3>Frontend Technologies</h3>
            <table>
                <tr>
                    <th>Technology</th>
                    <th>Version</th>
                    <th>Purpose</th>
                    <th>Testing Requirements</th>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Node.js</span></td>
                    <td>18.0.0+</td>
                    <td>JavaScript runtime</td>
                    <td>Build process tests, Package resolution</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">React</span></td>
                    <td>19.1.0</td>
                    <td>Frontend framework</td>
                    <td>Component integration tests, User interaction tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">TypeScript</span></td>
                    <td>5.7.2</td>
                    <td>Type safety</td>
                    <td>Type checking tests, Compilation tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Vite</span></td>
                    <td>6.0</td>
                    <td>Build tool</td>
                    <td>Build integration tests, Hot reload tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">TailwindCSS</span></td>
                    <td>4.0.0</td>
                    <td>CSS framework</td>
                    <td>Style compilation tests, Responsive tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Radix UI</span></td>
                    <td>Various</td>
                    <td>UI components</td>
                    <td>Component accessibility tests, Interaction tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Chart.js/Recharts</span></td>
                    <td>Latest</td>
                    <td>Data visualization</td>
                    <td>Chart rendering tests, Data binding tests</td>
                </tr>
            </table>
        </div>

        <div class="section">
            <h3>Testing Framework Technologies</h3>
            <table>
                <tr>
                    <th>Technology</th>
                    <th>Version</th>
                    <th>Purpose</th>
                    <th>Integration Focus</th>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Pest PHP</span></td>
                    <td>3.8</td>
                    <td>PHP testing framework</td>
                    <td>Backend integration tests, API tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">PHPUnit</span></td>
                    <td>Latest</td>
                    <td>Underlying test runner</td>
                    <td>Database tests, Service integration</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Laravel Testing</span></td>
                    <td>Built-in</td>
                    <td>Laravel-specific testing</td>
                    <td>HTTP tests, Database factories, Seeders</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Mockery</span></td>
                    <td>1.6</td>
                    <td>PHP mocking framework</td>
                    <td>Service mocking, External API mocking</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Faker</span></td>
                    <td>1.23</td>
                    <td>Test data generation</td>
                    <td>Realistic test data creation</td>
                </tr>
            </table>
        </div>

        <h2>3. Integration Testing Approach</h2>
        <div class="section">
            <div class="warning">
                <strong>Note:</strong> Integration testing for Sippar requires testing the interaction between multiple layers:
                Domain services, Laravel controllers, Inertia.js responses, React components, and external services.
            </div>

            <h3>Testing Pyramid Strategy</h3>
            <ol>
                <li><strong>Unit Tests (30%)</strong> - Individual domain services, utilities, and components</li>
                <li><strong>Integration Tests (50%)</strong> - Cross-layer interactions, API endpoints, database operations</li>
                <li><strong>End-to-End Tests (20%)</strong> - Complete user workflows, browser automation</li>
            </ol>
        </div>

        <div class="section">
            <h3>Frontend Technologies</h3>
            <table>
                <tr>
                    <th>Technology</th>
                    <th>Version</th>
                    <th>Purpose</th>
                    <th>Testing Requirements</th>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Node.js</span></td>
                    <td>18.0.0+</td>
                    <td>JavaScript runtime</td>
                    <td>Build process tests, Package resolution</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">React</span></td>
                    <td>19.1.0</td>
                    <td>Frontend framework</td>
                    <td>Component integration tests, User interaction tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">TypeScript</span></td>
                    <td>5.7.2</td>
                    <td>Type safety</td>
                    <td>Type checking tests, Compilation tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Vite</span></td>
                    <td>6.0</td>
                    <td>Build tool</td>
                    <td>Build integration tests, Hot reload tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">TailwindCSS</span></td>
                    <td>4.0.0</td>
                    <td>CSS framework</td>
                    <td>Style compilation tests, Responsive tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Radix UI</span></td>
                    <td>Various</td>
                    <td>UI components</td>
                    <td>Component accessibility tests, Interaction tests</td>
                </tr>
            </table>
        </div>

        <div class="section">
            <h3>Testing Framework Technologies</h3>
            <table>
                <tr>
                    <th>Technology</th>
                    <th>Version</th>
                    <th>Purpose</th>
                    <th>Integration Focus</th>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Pest PHP</span></td>
                    <td>3.8</td>
                    <td>PHP testing framework</td>
                    <td>Backend integration tests, API tests</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">PHPUnit</span></td>
                    <td>Latest</td>
                    <td>Underlying test runner</td>
                    <td>Database tests, Service integration</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Laravel Testing</span></td>
                    <td>Built-in</td>
                    <td>Laravel-specific testing</td>
                    <td>HTTP tests, Database factories, Seeders</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Mockery</span></td>
                    <td>1.6</td>
                    <td>PHP mocking framework</td>
                    <td>Service mocking, External API mocking</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Faker</span></td>
                    <td>1.23</td>
                    <td>Test data generation</td>
                    <td>Realistic test data creation</td>
                </tr>
            </table>
        </div>

        <h2>3. Integration Testing Approach</h2>
        <div class="section">
            <div class="warning">
                <strong>Note:</strong> Integration testing for Sippar requires testing the interaction between multiple layers:
                Domain services, Laravel controllers, Inertia.js responses, React components, and external services.
            </div>

            <h3>Testing Pyramid Strategy</h3>
            <ol>
                <li><strong>Unit Tests (30%)</strong> - Individual domain services, utilities, and components</li>
                <li><strong>Integration Tests (50%)</strong> - Cross-layer interactions, API endpoints, database operations</li>
                <li><strong>End-to-End Tests (20%)</strong> - Complete user workflows, browser automation</li>
            </ol>
        </div>

        <h2>4. Core Integration Testing Scenarios</h2>
        <div class="section">
            <h3>4.1 Requisition Workflow Integration</h3>
            <div class="priority-high">
                <strong>Priority: HIGH</strong> - Core business functionality
            </div>
            <p><strong>Technologies Required:</strong></p>
            <ul>
                <li><span class="tech-highlight">Pest PHP</span> - Test framework</li>
                <li><span class="tech-highlight">Laravel HTTP Testing</span> - API endpoint testing</li>
                <li><span class="tech-highlight">Database Factories</span> - Test data creation</li>
                <li><span class="tech-highlight">Storage Facade</span> - File upload testing</li>
                <li><span class="tech-highlight">Mail Facade</span> - Notification testing</li>
                <li><span class="tech-highlight">Queue Facade</span> - Background job testing</li>
            </ul>

            <p><strong>Test Scenarios:</strong></p>
            <ol>
                <li>Complete requisition creation with attachments</li>
                <li>Multi-step approval workflow execution</li>
                <li>Requisition status transitions and notifications</li>
                <li>Role-based access control validation</li>
                <li>Transaction generation from approved requisitions</li>
            </ol>

            <div class="code-block">
// Example Integration Test Structure
test('complete requisition workflow with approvals', function () {
    // Setup multi-organizational test data
    $organization = Organization::factory()->create();
    $department = Department::factory()->create(['organization_id' => $organization->id]);
    $workflow = ApprovalWorkflow::factory()->create(['department_id' => $department->id]);

    // Create users with different roles
    $requester = User::factory()->create();
    $hod = User::factory()->create();
    $financeManager = User::factory()->create();

    // Assign roles and permissions
    $requester->assignRole('Employee');
    $hod->assignRole('HOD');
    $financeManager->assignRole('Finance Manager');

    // Test requisition creation
    $this->actingAs($requester)
         ->post('/requisitions', $requisitionData)
         ->assertStatus(201);

    // Test approval workflow
    $this->actingAs($hod)
         ->post("/requisitions/{$requisition->id}/approve")
         ->assertStatus(200);

    // Verify notifications sent
    Mail::assertSent(RequisitionApproved::class);

    // Verify transaction creation
    $this->assertDatabaseHas('transactions', [
        'requisition_id' => $requisition->id
    ]);
});
            </div>

            <h3>4.2 Multi-Tenant Organization Integration</h3>
            <div class="priority-high">
                <strong>Priority: HIGH</strong> - Data isolation critical
            </div>
            <p><strong>Technologies Required:</strong></p>
            <ul>
                <li><span class="tech-highlight">Laravel Policies</span> - Authorization testing</li>
                <li><span class="tech-highlight">Spatie Permission</span> - Role-based access</li>
                <li><span class="tech-highlight">Database Transactions</span> - Data isolation</li>
                <li><span class="tech-highlight">Middleware Testing</span> - Request filtering</li>
            </ul>

            <p><strong>Test Scenarios:</strong></p>
            <ol>
                <li>Cross-organization data access prevention</li>
                <li>Organization-specific role assignments</li>
                <li>Department-based workflow isolation</li>
                <li>User permission inheritance across organizations</li>
            </ol>

            <h3>4.3 File Attachment System Integration</h3>
            <div class="priority-medium">
                <strong>Priority: MEDIUM</strong> - Supporting functionality
            </div>
            <p><strong>Technologies Required:</strong></p>
            <ul>
                <li><span class="tech-highlight">Laravel Storage</span> - File system testing</li>
                <li><span class="tech-highlight">File Upload Testing</span> - Multipart form handling</li>
                <li><span class="tech-highlight">Domain Services</span> - Attachment service integration</li>
                <li><span class="tech-highlight">Policy Authorization</span> - File access control</li>
            </ul>

            <p><strong>Test Scenarios:</strong></p>
            <ol>
                <li>File upload to requisitions and transactions</li>
                <li>Attachment copying between entities</li>
                <li>File access authorization</li>
                <li>Storage cleanup and lifecycle management</li>
            </ol>

            <h3>4.4 Cash Float Management Integration</h3>
            <div class="priority-medium">
                <strong>Priority: MEDIUM</strong> - Financial tracking
            </div>
            <p><strong>Technologies Required:</strong></p>
            <ul>
                <li><span class="tech-highlight">Laravel Scheduler</span> - Automated alerts</li>
                <li><span class="tech-highlight">Job Queues</span> - Background processing</li>
                <li><span class="tech-highlight">PDF Generation</span> - Report creation</li>
                <li><span class="tech-highlight">Chart of Accounts</span> - Financial categorization</li>
            </ul>

            <p><strong>Test Scenarios:</strong></p>
            <ol>
                <li>Cash float allocation and tracking</li>
                <li>Low balance alert generation</li>
                <li>Transaction impact on float balances</li>
                <li>PDF report generation and export</li>
            </ol>
        </div>

        <h2>5. Frontend Integration Testing</h2>
        <div class="section">
            <div class="info">
                <strong>Challenge:</strong> Testing React components that receive data from Laravel via Inertia.js requires
                specialized testing approaches that bridge server-side and client-side concerns.
            </div>

            <h3>5.1 Inertia.js Integration Testing</h3>
            <p><strong>Technologies Required:</strong></p>
            <ul>
                <li><span class="tech-highlight">Laravel Inertia Testing</span> - Server-side Inertia responses</li>
                <li><span class="tech-highlight">React Testing Library</span> - Component testing</li>
                <li><span class="tech-highlight">Jest/Vitest</span> - JavaScript test runner</li>
                <li><span class="tech-highlight">MSW (Mock Service Worker)</span> - API mocking</li>
                <li><span class="tech-highlight">Playwright/Cypress</span> - Browser automation</li>
            </ul>

            <p><strong>Test Scenarios:</strong></p>
            <ol>
                <li>Inertia page component rendering with Laravel data</li>
                <li>Form submission and validation error handling</li>
                <li>Real-time notification updates</li>
                <li>File upload progress and error states</li>
                <li>Dashboard data visualization components</li>
            </ol>

            <div class="code-block">
// Example Inertia Integration Test
test('requisition form submits and handles validation errors', function () {
    $user = User::factory()->create();

    $this->actingAs($user)
         ->get('/requisitions/create')
         ->assertInertia(fn ($page) =>
             $page->component('Requisitions/CreateRequisition')
                  ->has('departments')
                  ->has('chartOfAccounts')
         );

    // Test form submission with invalid data
    $this->actingAs($user)
         ->post('/requisitions', ['purpose' => ''])
         ->assertInertia(fn ($page) =>
             $page->component('Requisitions/CreateRequisition')
                  ->hasErrors('purpose')
         );
});
            </div>

            <h3>5.2 Component Integration Testing</h3>
            <p><strong>Technologies Required:</strong></p>
            <ul>
                <li><span class="tech-highlight">React Testing Library</span> - Component rendering</li>
                <li><span class="tech-highlight">User Event</span> - User interaction simulation</li>
                <li><span class="tech-highlight">Radix UI Testing</span> - Accessible component testing</li>
                <li><span class="tech-highlight">Chart.js Testing</span> - Data visualization testing</li>
            </ul>
        </div>

        <h2>6. Database Integration Testing</h2>
        <div class="section">
            <h3>6.1 Multi-Database Support Testing</h3>
            <p><strong>Technologies Required:</strong></p>
            <ul>
                <li><span class="tech-highlight">SQLite</span> - Development and testing database</li>
                <li><span class="tech-highlight">MySQL/MariaDB</span> - Production database options</li>
                <li><span class="tech-highlight">PostgreSQL</span> - Alternative production database</li>
                <li><span class="tech-highlight">Laravel Migrations</span> - Schema management</li>
                <li><span class="tech-highlight">Database Seeders</span> - Test data population</li>
            </ul>

            <p><strong>Test Scenarios:</strong></p>
            <ol>
                <li>Migration compatibility across database engines</li>
                <li>Foreign key constraint enforcement</li>
                <li>Database-specific query optimization</li>
                <li>Transaction isolation and rollback testing</li>
                <li>Large dataset performance testing</li>
            </ol>

            <h3>6.2 Domain Repository Integration</h3>
            <p><strong>Technologies Required:</strong></p>
            <ul>
                <li><span class="tech-highlight">Eloquent ORM</span> - Database abstraction</li>
                <li><span class="tech-highlight">Repository Pattern</span> - Domain isolation</li>
                <li><span class="tech-highlight">Database Factories</span> - Test data generation</li>
                <li><span class="tech-highlight">Model Observers</span> - Event-driven updates</li>
            </ul>

            <div class="code-block">
// Example Repository Integration Test
test('requisition repository handles complex queries', function () {
    $organization = Organization::factory()->create();
    $department = Department::factory()->create(['organization_id' => $organization->id]);

    // Create test requisitions with various statuses
    Requisition::factory()->count(5)->create([
        'department_id' => $department->id,
        'status' => 'pending'
    ]);

    $repository = app(RequisitionRepositoryInterface::class);

    // Test complex filtering
    $results = $repository->findByDepartmentAndStatus(
        $department->id,
        'pending',
        ['requester', 'items', 'approvals']
    );

    expect($results)->toHaveCount(5);
    expect($results->first()->requester)->not->toBeNull();
});
            </div>
        </div>

        <h2>7. External Service Integration</h2>
        <div class="section">
            <h3>7.1 Email Service Integration</h3>
            <p><strong>Technologies Required:</strong></p>
            <ul>
                <li><span class="tech-highlight">Laravel Mail</span> - Email sending</li>
                <li><span class="tech-highlight">SMTP/Mailgun/SES</span> - Email providers</li>
                <li><span class="tech-highlight">Queue System</span> - Asynchronous email processing</li>
                <li><span class="tech-highlight">Mail Testing</span> - Email assertion helpers</li>
            </ul>

            <p><strong>Test Scenarios:</strong></p>
            <ol>
                <li>Notification email delivery</li>
                <li>Email template rendering with dynamic data</li>
                <li>Bulk email processing via queues</li>
                <li>Email failure handling and retry logic</li>
            </ol>

            <h3>7.2 File Storage Integration</h3>
            <p><strong>Technologies Required:</strong></p>
            <ul>
                <li><span class="tech-highlight">Laravel Storage</span> - File system abstraction</li>
                <li><span class="tech-highlight">Local/S3/Digital Ocean</span> - Storage drivers</li>
                <li><span class="tech-highlight">File Validation</span> - Security and type checking</li>
                <li><span class="tech-highlight">Storage Testing</span> - Fake storage for tests</li>
            </ul>

            <p><strong>Test Scenarios:</strong></p>
            <ol>
                <li>File upload to different storage backends</li>
                <li>File access permission validation</li>
                <li>Storage quota and size limit enforcement</li>
                <li>File cleanup and garbage collection</li>
            </ol>
        </div>

        <h2>8. Testing Environment Setup</h2>
        <div class="section">
            <h3>8.1 Development Environment</h3>
            <p><strong>Required Technologies:</strong></p>
            <ul>
                <li><span class="tech-highlight">Docker</span> - Containerized testing environment</li>
                <li><span class="tech-highlight">Laravel Sail</span> - Docker development environment</li>
                <li><span class="tech-highlight">SQLite</span> - Fast in-memory testing database</li>
                <li><span class="tech-highlight">Redis</span> - Queue and cache testing</li>
                <li><span class="tech-highlight">Mailhog/MailCatcher</span> - Email testing</li>
            </ul>

            <div class="code-block">
# Docker Compose for Testing Environment
version: '3.8'
services:
  app:
    build: .
    environment:
      - APP_ENV=testing
      - DB_CONNECTION=sqlite
      - DB_DATABASE=:memory:
      - MAIL_MAILER=array
      - QUEUE_CONNECTION=sync
    volumes:
      - ./:/var/www/html

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"

  mailhog:
    image: mailhog/mailhog
    ports:
      - "8025:8025"
            </div>

            <h3>8.2 CI/CD Pipeline Integration</h3>
            <p><strong>Required Technologies:</strong></p>
            <ul>
                <li><span class="tech-highlight">GitHub Actions</span> - Automated testing</li>
                <li><span class="tech-highlight">PHPUnit XML</span> - Test configuration</li>
                <li><span class="tech-highlight">Code Coverage</span> - Test completeness metrics</li>
                <li><span class="tech-highlight">Parallel Testing</span> - Faster test execution</li>
            </ul>
        </div>

        <h2>9. Test Execution Commands</h2>
        <div class="section">
            <h3>9.1 Backend Testing Commands</h3>
            <div class="code-block">
# Run all tests
php artisan test

# Run specific test suites
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# Run tests with coverage
php artisan test --coverage

# Run parallel tests for faster execution
php artisan test --parallel

# Run specific integration tests
php artisan test tests/Feature/AttachmentTest.php
php artisan test tests/Feature/CompleteAttachmentWorkflowTest.php

# Database testing with fresh migrations
php artisan test --env=testing --migrate-fresh
            </div>

            <h3>9.2 Frontend Testing Commands</h3>
            <div class="code-block">
# Install frontend dependencies
npm install

# Run TypeScript type checking
npm run types

# Run ESLint for code quality
npm run lint

# Run frontend unit tests (when implemented)
npm run test

# Build assets for testing
npm run build

# Run development server for manual testing
npm run dev
            </div>

            <h3>9.3 Full Integration Test Suite</h3>
            <div class="code-block">
# Complete integration testing workflow
./setup.sh                    # Setup environment
php artisan migrate:fresh --seed  # Fresh database with test data
npm run build                 # Build frontend assets
php artisan test --parallel  # Run all backend tests
npm run test                  # Run frontend tests (when implemented)
            </div>
        </div>

        <h2>10. Recommended Testing Tools</h2>
        <div class="section">
            <h3>Additional Tools for Comprehensive Testing</h3>
            <table>
                <tr>
                    <th>Tool</th>
                    <th>Purpose</th>
                    <th>Technology Integration</th>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Laravel Dusk</span></td>
                    <td>Browser automation testing</td>
                    <td>Full-stack user workflow testing</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Playwright</span></td>
                    <td>Modern browser testing</td>
                    <td>Cross-browser React component testing</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Postman/Insomnia</span></td>
                    <td>API testing</td>
                    <td>Manual API endpoint validation</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Laravel Telescope</span></td>
                    <td>Application debugging</td>
                    <td>Test execution monitoring</td>
                </tr>
                <tr>
                    <td><span class="tech-highlight">Xdebug</span></td>
                    <td>PHP debugging and profiling</td>
                    <td>Test coverage analysis</td>
                </tr>
            </table>
        </div>

        <h2>11. Conclusion</h2>
        <div class="section">
            <div class="info">
                <strong>Integration Testing Success Factors:</strong>
            </div>
            <ul>
                <li><strong>Comprehensive Technology Coverage</strong> - Test all layers from database to UI</li>
                <li><strong>Realistic Test Data</strong> - Use factories and seeders for authentic scenarios</li>
                <li><strong>Cross-Domain Testing</strong> - Verify interactions between business domains</li>
                <li><strong>Multi-Tenant Validation</strong> - Ensure proper data isolation</li>
                <li><strong>Performance Considerations</strong> - Test with realistic data volumes</li>
                <li><strong>Security Testing</strong> - Validate authorization and access controls</li>
                <li><strong>Error Handling</strong> - Test failure scenarios and recovery</li>
            </ul>

            <div class="warning">
                <strong>Implementation Priority:</strong> Start with high-priority requisition workflow tests,
                then expand to cover multi-tenant scenarios, file management, and external service integrations.
            </div>

            <p>This integration testing strategy provides a comprehensive approach to validating the Sippar
            petty cash management system across all technology layers, ensuring robust functionality and
            reliable user experiences.</p>
        </div>
    </div>
</body>
</html>
